<?php

if (!defined('BASEPATH')) exit('No direct script access allowed');

/**
 * class saleskit_model
 * modeling untuk tabel saleskit
 * <AUTHOR>
 * Created at 2019 Aug 28 09:42
 */
class Aplikasi_model extends Custom_model
{
    public function __construct()
    {
        parent::__construct();
    }

    function getListBanner()
    {
        $data = $this->get('home_banner', '*', [], ' ORDER BY urutan ASC');

        return $data;
    }

    function getUrutanTerbesar()
    {
        $data = $this->first('home_banner', 'urutan', [], ' ORDER BY urutan DESC');

        return $data->urutan ?? 1;
    }

    function getListBrandMingguIni()
    {
        $data = $this->get('home_brand as hb
        INNER JOIN ref_brand as rb ON rb.id = hb.id_brand', 'hb.id_brand, hb.id, rb.banner, rb.nama_brand', [
            'tag' => 'minggu_ini'
        ], ' ORDER BY hb.urutan ASC');

        return $data;
    }

    function getListBrand($tipe = 'minggu_ini', $search_by = '')
    {
        $list_brand = [];
        $in = '';
        $where = '';

        switch ($tipe) {
            case 'minggu_ini':
                $list_brand = $this->getListBrandMingguIni();
                break;

            case 'web':
                $list_brand = $this->getListBrandWeb();
                break;
        }

        foreach ($list_brand as $brand) {
            $in .= empty($in) ? $brand->id_brand : ', ' . $brand->id_brand;
        }

        if (!empty($in)) {
            $where = 'WHERE id NOT IN (' . $in . ')';
        }

        if (!empty($search_by)) {
            $where .= (empty($where) ? 'WHERE' : 'AND') . " LOWER(nama_brand) LIKE '%" . strtolower($search_by) . "%'";
        }

        $query = 'SELECT * FROM ref_brand ' . $where;
        $data = $this->query($query, 'dropship')->result();

        return $data;
    }

    function getUrutanTerbesarBrandMingguIni()
    {
        $data = $this->first('home_brand', 'urutan', [
            'tag' => 'minggu_ini'
        ], ' ORDER BY urutan DESC');

        return $data->urutan ?? 1;
    }

    function getListHomeLayout()
    {
        $data = $this->get('home_layout', '*', [], ' ORDER BY urutan ASC');

        foreach ($data as $model) {
            $model->total_produk = $this->count('home_layout_detail', 'id', [
                'id_home_layout' => $model->id
            ]);
        }

        return $data;
    }

    function fetchProdukByHomeLayout($id, $searchBy = '')
    {
        $where = 'hld.id_home_layout = ' . $id;

        if (!empty($searchBy)) {
            $where .= " AND LOWER(p.nama_produk) LIKE '%" . strtolower($searchBy) . "%'";
        }

        $query = 'SELECT hld.id, p.id as id_produk, p.nama_produk, p.harga_final, p.tipe, p.join_sku
        FROM home_layout_detail as hld
        LEFT JOIN produk as p ON p.id = hld.id_produk
        WHERE ' . $where . ' and p.id is not null';
        $data = $this->query($query, 'dropship')->result();
        // return $data;
        foreach ($data as $model) {
            if (checkProductFrom('wholesale', $model->join_sku)) {
                $queryFoto = "SELECT CONCAT('" . imgProductWL() . "', id_produk, '/', namafile, '_t.jpg') as image
                FROM foto_produk
                WHERE id_produk = '" . str_replace('WL_', '', $model->join_sku) . "' AND mainfoto = 1";
                $dataFoto = $this->query($queryFoto, 'wholesale')->row();

                $model->foto = $dataFoto->image ?? '';
            } else {
                $dataFoto = $this->first('foto_produk', '*', [
                    'id_produk' => $model->id_produk,
                    'mainfoto' => 1
                ]);
                $model->foto = imgcdn($model->id_produk . '/' . ($dataFoto->namafile ?? '') . '_t.jpg');
            }

            $model->harga_final_as_string = 'Rp. ' . number_format($model->harga_final);
        }

        return $data;
    }

    function checkHomeLayoutExists($title, $id = '')
    {
        if (empty($id)) {
            $data = $this->count('home_layout', 'id', [
                'LOWER(title)' => strtolower($title)
            ]);
        } else {
            $query = "SELECT COUNT(id) as jml FROM home_layout WHERE LOWER(title) = '" . strtolower($title) . "' AND id != $id";
            $data = $this->query($query, 'dropship')->row()->jml;
        }

        return $data > 0;
    }

    function getUrutanTerbesarHomeLayout()
    {
        $data = $this->first('home_layout', 'urutan', [], ' ORDER BY urutan DESC');

        return $data->urutan ?? 1;
    }

    function fetchProdukAvailHomeLayout($id, $searchBy = '', $offset = 0)
    {
        $dataAssign = $this->fetchProdukByHomeLayout($id, $searchBy);
        $where = 'WHERE aktif = 1';
        $in = '';

        foreach ($dataAssign as $assign) {
            $in .= empty($in) ? $assign->id_produk : ', ' . $assign->id_produk;
        }

        if (!empty($in)) {
            $where .= ' AND id NOT IN (' . $in . ')';
        }

        if (!empty($searchBy)) {
            $where .= " AND LOWER(nama_produk) LIKE '%" . strtolower($searchBy) . "%'";
        }

        $query = 'SELECT * FROM produk ' . $where . ' LIMIT 12 OFFSET ' . $offset;
        $data = $this->query($query, 'dropship')->result();

        foreach ($data as $model) {
            if (checkProductFrom('wholesale', $model->join_sku)) {
                $queryFoto = "SELECT CONCAT('" . imgProductWL() . "', id_produk, '/', namafile, '_t.jpg') as image
                FROM foto_produk
                WHERE id_produk = '" . str_replace('WL_', '', $model->join_sku) . "' AND mainfoto = 1";
                $dataFoto = $this->query($queryFoto, 'wholesale')->row();

                $model->foto = $dataFoto->image ?? '';
            } else {
                $dataFoto = $this->first('foto_produk', '*', [
                    'id_produk' => $model->id,
                    'mainfoto' => 1
                ]);
                $model->foto = imgcdn($model->id . '/' . ($dataFoto->namafile ?? '') . '_t.jpg');
            }
            $model->harga_final_as_string = 'Rp. ' . number_format($model->harga_final);
        }

        return $data;
    }

    function getListBrandWeb()
    {
        $data = $this->get('home_brand as hb
        INNER JOIN ref_brand as rb ON rb.id = hb.id_brand', 'hb.id_brand, hb.id, rb.banner, rb.logo, rb.nama_brand', [
            'tag' => 'web'
        ], ' ORDER BY hb.urutan ASC');

        foreach ($data as $brand) {
            $brand->list_produk = $this->get('home_brand_produk as hbp
            INNER JOIN produk as p ON p.id = hbp.id_produk', 'p.nama_produk, p.tipe, hbp.id_produk, hbp.id_home_brand_produk, p.id_brand, p.harga_final, p.join_sku', [
                'hbp.id_home_brand' => $brand->id
            ]);

            foreach ($brand->list_produk as $produk) {
                if (checkProductFrom('wholesale', $produk->join_sku)) {
                    $queryFoto = "SELECT CONCAT('" . imgProductWL() . "', id_produk, '/', namafile, '_t.jpg') as image
                    FROM foto_produk
                    WHERE id_produk = '" . str_replace('WL_', '', $produk->join_sku) . "' AND mainfoto = 1";
                    $dataFoto = $this->query($queryFoto, 'wholesale')->row();

                    $produk->foto = $dataFoto->image ?? '';
                } else {
                    $foto = $this->first('foto_produk', 'namafile', [
                        'id_produk' => $produk->id_produk,
                        'mainfoto' => 1
                    ]);
                    $produk->foto = imgcdn($produk->id_produk . '/' . ($foto->namafile ?? '') . '_t.jpg');
                }
            }
        }

        return $data;
    }

    function getUrutanTerbesarBrandWeb()
    {
        $data = $this->first('home_brand', 'urutan', [
            'tag' => 'web'
        ], ' ORDER BY urutan DESC');

        return $data->urutan ?? 1;
    }

    function getListProdukByBrand($id_home_brand)
    {
        $data = $this->get('home_brand_produk as hbp
        INNER JOIN produk as p ON p.id = hbp.id_produk', 'p.nama_produk, p.tipe, hbp.id_produk, hbp.id_home_brand_produk, hbp.id_home_brand, p.id_brand, p.harga_final', [
            'hbp.id_home_brand' => $id_home_brand
        ]);

        foreach ($data as $produk) {
            $foto = $this->first('foto_produk', 'namafile', [
                'id_produk' => $produk->id_produk,
                'mainfoto' => 1
            ]);
            $produk->foto = imgcdn($produk->id_produk . '/' . ($foto->namafile ?? '') . '_t.jpg');
        }

        return $data;
    }

    function getListProdukSearchByBrand($id_brand, $id_home_brand, $search = '', $offset = 0)
    {
        $where = 'WHERE p.id_brand = ' . $id_brand;
        $in = '';
        $dataProdukExists = $this->get('home_brand_produk', 'id_produk', ['id_home_brand' => $id_home_brand]);

        foreach ($dataProdukExists as $exists) {
            $in .= (empty($in) ? '' : ',') . $exists->id_produk;
        }

        if (!empty($in)) {
            $where .= ' AND id NOT IN (' . $in . ')';
        }

        if (!empty($search)) {
            $where .= " AND LOWER(p.nama_produk) LIKE '%" . strtolower($search) . "%'";
        }

        $query = 'SELECT p.id as id_produk, p.nama_produk, p.tipe, p.id_brand, p.harga_final, p.join_sku
        FROM produk as p
        ' . $where . ' LIMIT 12 OFFSET ' . $offset;
        $data = $this->query($query, 'dropship')->result();

        foreach ($data as $produk) {
            if (checkProductFrom('wholesale', $produk->join_sku)) {
                $queryFoto = "SELECT CONCAT('" . imgProductWL() . "', id_produk, '/', namafile, '_t.jpg') as image
                FROM foto_produk
                WHERE id_produk = '" . str_replace('WL_', '', $produk->join_sku) . "' AND mainfoto = 1";
                $dataFoto = $this->query($queryFoto, 'wholesale')->row();

                $produk->foto = $dataFoto->image ?? '';
            } else {
                $foto = $this->first('foto_produk', 'namafile', [
                    'id_produk' => $produk->id_produk,
                    'mainfoto' => 1
                ]);
                $produk->foto = imgcdn($produk->id_produk . '/' . ($foto->namafile ?? '') . '_t.jpg');
            }
        }

        return $data;
    }
}
