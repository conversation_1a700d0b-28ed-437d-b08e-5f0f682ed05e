<?php

/**
 * class Pemberitahuan
 * class for Pemberitahuan member
 * <AUTHOR>
 * @property $ci variabel instance
 */
class Pemberitahuan extends CI_Controller
{

    private $ci;

    public function __construct()
    {
        $this->ci = &get_instance();
    }

    /**
     * Method Pertemanan
     * masukan Pemberitahuan pertemanan dalam database
     * @param array $data
     * @return boolean
     */
    public function pertemanan($data)
    {
        $simpan = $this->ci->db->query("INSERT INTO notifikasi 
                                        (
                                            tipe_notifikasi, 
                                            id_sekolah,
                                            id_member, 
                                            tujuan, 
                                            aktivitas,
                                            status, 
                                            tanggal, 
                                            jam
                                         )
                                    VALUES (?,?,?,?,
                                            ?,?,?,? )"
                , $data);

        //selalu close mysql connection, untuk menghindari error "to many connections"
        if($simpan != NULL)
        {
            $this->ci->db->close();
        }

        if($simpan == TRUE)
        {
            return SUCCESS;
        }
        else
        {
            show_error($this->ci->lang->line('db_update_error'));
            exit(0);
        }
    }

    /**
     * Method akademik
     * masukan Pemberitahuan akademik dalam database
     * @param array $data
     * @return boolean
     */
    public function akademik($data)
    {
        $simpan = $this->ci->db->query("INSERT INTO notifikasi 
                                        (
                                            id_notifikasi, 
                                            tipe_notifikasi, 
                                            id_sekolah,
                                            id_member, 
                                            tujuan, 
                                            aktivitas,
                                            status, 
                                            tanggal, 
                                            jam
                                         )
                                    VALUES (?,?,?,?,?,
                                            ?,?,?,? )"
                , $data);

        //selalu close mysql connection, untuk menghindari error "to many connections"
        if($simpan != NULL)
        {
            $this->ci->db->close();
        }

        if($simpan)
        {
            return SUCCESS;
        }
        else
        {
            show_error($this->ci->lang->line('db_update_error'));
            exit(0);
        }
    }

    public function mark_notification_read($id_notifikasi)
    {
        $this->ci->load->helper('security');

        $id_notifikasi = xss_clean($id_notifikasi);

        $query = $this->ci->db->query("UPDATE notifikasi SET status='Y' WHERE id_notifikasi=?", array($id_notifikasi));

        if($query != NULL)
        {
            $this->ci->db->close();
        }

        if($query)
        {
            return SUCCESS;
        }
    }

    /**
     * menampilkan pemberitahuan
     * @param int $id_member
     * @return array stdObject 
     */
    public function get_pemberitahuan($id_member)
    {
        $this->ci->load->helper('security');
        $id_member = xss_clean($id_member);

        $query = $this->ci->db->query("SELECT * FROM notifikasi 
                WHERE tipe_notifikasi='KOMENTAR' 
                    AND id_member=? AND status='N' LIMIT 3", array($id_member))->result_object();

        if($query != NULL)
        {
            $this->ci->db->close();
        }

        return $query;
    }

    /**
     * menampilkan pemberitahuan pesan baru
     * @param int $id_member
     * @return array stdObject 
     */
    public function get_pemberitahuan_new_messages($id_member)
    {
        $this->ci->load->helper('security');
        $id_member = xss_clean($id_member);

        $query = $this->ci->db->query("SELECT * FROM notifikasi 
                WHERE tipe_notifikasi='SURAT' 
                    AND id_member=? 
                    AND status='N' LIMIT 3", array($id_member))->result_object();

        if($query != NULL)
        {
            $this->ci->db->close();
        }

        return $query;
    }

    /**
     * menampilkan pemberitahuan pesan baru
     * @param int $id_member
     * @return array stdObject 
     */
    public function get_pemberitahuan_very_new_messages($id_member, $tanggal = NULL, $jam = NULL)
    {
        $this->ci->load->helper('security');
        $id_member = xss_clean($id_member);
        $jam = date("H") . ":" . date("i") . ":" . (date("s") + 5);

        $query = $this->ci->db->query("SELECT * FROM notifikasi 
                WHERE tipe_notifikasi='SURAT' 
                    AND id_member=? 
                    AND status='N' AND tanggal = CURRENT_DATE() AND jam='" . $jam . "' 
                        LIMIT 3", array($id_member))->result_object();

        if($query != NULL)
        {
            $this->ci->db->close();
        }

        return $query;
    }

    /**
     * menampilkan pemberitahuan event baru
     * @param int $id_member
     * @return array stdObject 
     */
    public function get_pemberitahuan_new_events($id_sekolah)
    {
        $this->ci->load->helper('security');
        $id_sekolah = xss_clean($id_sekolah);

        $query = $this->ci->db->query("SELECT * FROM notifikasi 
                WHERE tipe_notifikasi='EVENT' AND id_sekolah=?  
                    AND status='N' LIMIT 3", array($id_sekolah))->result_object();

        if($query != NULL)
        {
            $this->ci->db->close();
        }

        return $query;
    }

}

/* end of file */