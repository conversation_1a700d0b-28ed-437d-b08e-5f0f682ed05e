<?php

/**
 * class Access
 * class for checking session utk login
 * <AUTHOR>
 */
class Access extends CI_Controller
{

    var $ci;
 
	function __construct() 
	{
		$this->ci = &get_instance();
		$this->ci->load->model(array('access_model'));
	}
 
	function check_session() 
	{
		if ($this->ci->session->userdata('logged_in') == TRUE)
			{
				redirect(base_url());
			}
		else
			{
			return null;
			}
	} 
	 
	function check_previleges($module) 
	{
	if ($this->ci->session->userdata('logged_in') == TRUE)
		{
		$level = $this->ci->session->userdata('level');
		$check_previleges = $this->ci->access_model->check_previleges($module,$level);
		
		if($check_previleges == TRUE) 
			{}
		else
			{
			echo ' <section id="main-content">
			<section class="wrapper">
			<div class="row">
				<div class="col-sm-12">
					<section class="panel">
						<div class="alert alert-warning"><h4>Access Denied</h4>
						You have no permision to access this module.<br><br>
						<a href="'. $_SERVER['HTTP_REFERER'] .'" class="btn btn-sm btn-default"><i class="fa fa-arrow-left"></i> Back</a>
						</div>
					</section>
				</div>
			</div>
			<!-- page end-->
			</section>
		</section>';
			die();
			}
		}
	else
		{
		redirect(base_url('login'));
		}
	}
 
}