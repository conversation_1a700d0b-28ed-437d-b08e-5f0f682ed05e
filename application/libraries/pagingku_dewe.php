<?php

class Paging<PERSON>_dewe extends CI_Controller
{

    var $ci;
 
	function __construct() 
	{
		$this->ci = &get_instance();
	}
	
	function paging($totalhalaman, $halaman) 
	{

			$data ='<div class="pagination"><div class="results">Menampilkan halaman '.($this->uri->segment(5)+1).' dari ('.ceil($totalhalaman).' Halaman)</div></div>
			<h3>'; 
			
			// pembagian wilayah halaman
			if ($halaman==0)
				{
				$awal = $halaman;
				$data .=' Halaman : ';
				if ($totalhalaman < 5)
					{
					$akhir = $totalhalaman;
					$next = '';
					}
				else
					{
					$akhir = 5;
					$next =  '. . <a href="/produk/kategori/'.$this->uri->segment(4).'/'.$this->uri->segment(5).'/'.($akhir).'">selanjutnya</a>';
					}
				}
			elseif ($halaman==1)
				{
				$awal = $halaman - 1;
				$data .=' Halaman : ';
				if ($totalhalaman < 5)
					{
					$akhir = $totalhalaman;
					$next = '';
					}
				else
					{
					$akhir = 5;
					$next =  '. . <a href="/produk/kategori/'.$this->uri->segment(4).'/'.$this->uri->segment(5).'/'.($akhir).'">selanjutnya</a>';
					}				
				}
			elseif ($halaman==2)
				{
				$awal = $halaman - 2;
				$data .=' Halaman : ';
				if ($totalhalaman < 5)
					{
					$akhir = $totalhalaman;
					$next = '';
					}
				else
					{
					$akhir = 5;
					$next =  '. . <a href="/produk/kategori/'.$this->uri->segment(4).'/'.$this->uri->segment(5).'/'.($akhir).'">selanjutnya</a>';
					}				
				}				
			else	
				{
				$awal = $halaman - 1;
				if (($halaman + 5) > $totalhalaman)
					{
					$akhir = $totalhalaman;
					$data .=' <a style="color:#666;" href="/produk/kategori/'.$this->uri->segment(4).'/'.$this->uri->segment(5).'/'.($halaman - 1).'">sebelumnya . . </a> ';
					$next =  '';
					}
				elseif (($halaman + 6) < $totalhalaman)
					{
					$akhir = $halaman + 5;
					$next =  '. . <a style="color:#666;" href="/produk/kategori/'.$this->uri->segment(4).'/'.$this->uri->segment(5).'/'.($akhir + 1).'">selanjutnya</a>';
					$data .=' <a style="color:#666;" href="/produk/kategori/'.$this->uri->segment(4).'/'.$this->uri->segment(5).'/'.($halaman - 3).'">sebelumnya </a> . .';
					}
				else
					{
					$akhir = $halaman + 5;
					$next =  '';
					$data .=' <a style="color:#666;" href="/produk/kategori/'.$this->uri->segment(3).'/'.$this->uri->segment(4).'/'.($halaman - 3).'">sebelumnya </a> . .';
					}					
				}
			
			
			// generate nomor halaman
			for($hal=$awal; $hal < $akhir; $hal++)
				{
				$hal_2 = $hal+1;
				if ($halaman==$hal)
					{
					$data .= '<a style="font-size:15px; padding: 0 4px; font-weight:bold; color:#ccc;">'.$hal_2.'</a>';
					}
				else	
					{
					$data .= '<a style=" padding: 0 4px; font-weight:bold; color:#666;" href="/produk/kategori/'.$this->uri->segment(3).'/'.$this->uri->segment(4).'/'.$hal.'">'.$hal_2.'</a>';
					}
				
				}
			$data .= $next.'</h3>';
			
		return $data;	
	}
 
}