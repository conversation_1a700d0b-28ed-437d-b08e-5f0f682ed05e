<?php
/**
 * class Function Global
 * class utk function yang sering digunakan
 * <AUTHOR> budiantoro
 */
class Komisi extends CI_Controller
{
    var $ci;
 
	function __construct() 
	{
		$this->ci = &get_instance();
	}

	function get_komisi($idlevel,$code,$idtgl)
	{
		$CI=&get_instance();
		$CI->load->model('komisi_model');
		
		$get_level = $CI->komisi_model->get_level($idlevel);
						$list_username_all_downline = $CI->komisi_model->list_username_all_downline($code,$get_level->bobot);
						$jml_downline = count($list_username_all_downline);
						
						$jumlah = 0;
						for($i=0; $i < $jml_downline; $i++)
							{
							$oke = $CI->komisi_model->get_jumlah_by_username_n_idtgl($list_username_all_downline[$i],$idtgl);
								if($oke == null)
									{
									$now = 0;
									}
								else
									{
									$now = $oke->jumlah;
									}
								$jumlah = $jumlah + $now;
							}
						$total_downline_trans = $jumlah;
						$komisi = ($total_downline_trans / 100) * $get_level->komisi;
		
		
		return $komisi;
	}			

	
}