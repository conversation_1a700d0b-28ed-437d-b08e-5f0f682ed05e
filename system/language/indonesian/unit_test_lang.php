<?php

$lang['ut_test_name']		= 'Test Name';
$lang['ut_test_datatype']	= 'Test Datatype';
$lang['ut_res_datatype']	= 'Expected Datatype';
$lang['ut_result']			= 'Result';
$lang['ut_undefined']		= 'Undefined Test Name';
$lang['ut_file']			= 'File Name';
$lang['ut_line']			= 'Line Number';
$lang['ut_passed']			= 'Passed';
$lang['ut_failed']			= 'Failed';
$lang['ut_boolean']			= 'Boolean';
$lang['ut_integer']			= 'Integer';
$lang['ut_float']			= 'Float';
$lang['ut_double']			= 'Float'; // can be the same as float
$lang['ut_string']			= 'String';
$lang['ut_array']			= 'Array';
$lang['ut_object']			= 'Object';
$lang['ut_resource']		= 'Resource';
$lang['ut_null']			= 'Null';
$lang['ut_notes']			= 'Notes';


/* End of file unit_test_lang.php */
/* Location: ./system/language/english/unit_test_lang.php */