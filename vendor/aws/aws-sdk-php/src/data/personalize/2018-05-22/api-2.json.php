<?php
// This file was auto-generated from sdk-root/src/data/personalize/2018-05-22/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-05-22', 'endpointPrefix' => 'personalize', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceFullName' => 'Amazon Personalize', 'serviceId' => 'Personalize', 'signatureVersion' => 'v4', 'signingName' => 'personalize', 'targetPrefix' => 'AmazonPersonalize', 'uid' => 'personalize-2018-05-22', ], 'operations' => [ 'CreateBatchInferenceJob' => [ 'name' => 'CreateBatchInferenceJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateBatchInferenceJobRequest', ], 'output' => [ 'shape' => 'CreateBatchInferenceJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'CreateCampaign' => [ 'name' => 'CreateCampaign', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCampaignRequest', ], 'output' => [ 'shape' => 'CreateCampaignResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'CreateDataset' => [ 'name' => 'CreateDataset', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDatasetRequest', ], 'output' => [ 'shape' => 'CreateDatasetResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'CreateDatasetExportJob' => [ 'name' => 'CreateDatasetExportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDatasetExportJobRequest', ], 'output' => [ 'shape' => 'CreateDatasetExportJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'CreateDatasetGroup' => [ 'name' => 'CreateDatasetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDatasetGroupRequest', ], 'output' => [ 'shape' => 'CreateDatasetGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateDatasetImportJob' => [ 'name' => 'CreateDatasetImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDatasetImportJobRequest', ], 'output' => [ 'shape' => 'CreateDatasetImportJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'CreateEventTracker' => [ 'name' => 'CreateEventTracker', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEventTrackerRequest', ], 'output' => [ 'shape' => 'CreateEventTrackerResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'CreateFilter' => [ 'name' => 'CreateFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateFilterRequest', ], 'output' => [ 'shape' => 'CreateFilterResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateSchema' => [ 'name' => 'CreateSchema', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSchemaRequest', ], 'output' => [ 'shape' => 'CreateSchemaResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], ], 'idempotent' => true, ], 'CreateSolution' => [ 'name' => 'CreateSolution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSolutionRequest', ], 'output' => [ 'shape' => 'CreateSolutionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'CreateSolutionVersion' => [ 'name' => 'CreateSolutionVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSolutionVersionRequest', ], 'output' => [ 'shape' => 'CreateSolutionVersionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'DeleteCampaign' => [ 'name' => 'DeleteCampaign', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCampaignRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'DeleteDataset' => [ 'name' => 'DeleteDataset', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDatasetRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'DeleteDatasetGroup' => [ 'name' => 'DeleteDatasetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDatasetGroupRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'DeleteEventTracker' => [ 'name' => 'DeleteEventTracker', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEventTrackerRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'DeleteFilter' => [ 'name' => 'DeleteFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteFilterRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'DeleteSchema' => [ 'name' => 'DeleteSchema', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSchemaRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'DeleteSolution' => [ 'name' => 'DeleteSolution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSolutionRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'DescribeAlgorithm' => [ 'name' => 'DescribeAlgorithm', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAlgorithmRequest', ], 'output' => [ 'shape' => 'DescribeAlgorithmResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeBatchInferenceJob' => [ 'name' => 'DescribeBatchInferenceJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeBatchInferenceJobRequest', ], 'output' => [ 'shape' => 'DescribeBatchInferenceJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeCampaign' => [ 'name' => 'DescribeCampaign', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCampaignRequest', ], 'output' => [ 'shape' => 'DescribeCampaignResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeDataset' => [ 'name' => 'DescribeDataset', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDatasetRequest', ], 'output' => [ 'shape' => 'DescribeDatasetResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeDatasetExportJob' => [ 'name' => 'DescribeDatasetExportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDatasetExportJobRequest', ], 'output' => [ 'shape' => 'DescribeDatasetExportJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeDatasetGroup' => [ 'name' => 'DescribeDatasetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDatasetGroupRequest', ], 'output' => [ 'shape' => 'DescribeDatasetGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeDatasetImportJob' => [ 'name' => 'DescribeDatasetImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDatasetImportJobRequest', ], 'output' => [ 'shape' => 'DescribeDatasetImportJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeEventTracker' => [ 'name' => 'DescribeEventTracker', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEventTrackerRequest', ], 'output' => [ 'shape' => 'DescribeEventTrackerResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeFeatureTransformation' => [ 'name' => 'DescribeFeatureTransformation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFeatureTransformationRequest', ], 'output' => [ 'shape' => 'DescribeFeatureTransformationResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeFilter' => [ 'name' => 'DescribeFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFilterRequest', ], 'output' => [ 'shape' => 'DescribeFilterResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeRecipe' => [ 'name' => 'DescribeRecipe', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRecipeRequest', ], 'output' => [ 'shape' => 'DescribeRecipeResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeSchema' => [ 'name' => 'DescribeSchema', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSchemaRequest', ], 'output' => [ 'shape' => 'DescribeSchemaResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeSolution' => [ 'name' => 'DescribeSolution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSolutionRequest', ], 'output' => [ 'shape' => 'DescribeSolutionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeSolutionVersion' => [ 'name' => 'DescribeSolutionVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSolutionVersionRequest', ], 'output' => [ 'shape' => 'DescribeSolutionVersionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'GetSolutionMetrics' => [ 'name' => 'GetSolutionMetrics', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSolutionMetricsRequest', ], 'output' => [ 'shape' => 'GetSolutionMetricsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'ListBatchInferenceJobs' => [ 'name' => 'ListBatchInferenceJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListBatchInferenceJobsRequest', ], 'output' => [ 'shape' => 'ListBatchInferenceJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListCampaigns' => [ 'name' => 'ListCampaigns', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCampaignsRequest', ], 'output' => [ 'shape' => 'ListCampaignsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListDatasetExportJobs' => [ 'name' => 'ListDatasetExportJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDatasetExportJobsRequest', ], 'output' => [ 'shape' => 'ListDatasetExportJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListDatasetGroups' => [ 'name' => 'ListDatasetGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDatasetGroupsRequest', ], 'output' => [ 'shape' => 'ListDatasetGroupsResponse', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListDatasetImportJobs' => [ 'name' => 'ListDatasetImportJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDatasetImportJobsRequest', ], 'output' => [ 'shape' => 'ListDatasetImportJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListDatasets' => [ 'name' => 'ListDatasets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDatasetsRequest', ], 'output' => [ 'shape' => 'ListDatasetsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListEventTrackers' => [ 'name' => 'ListEventTrackers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEventTrackersRequest', ], 'output' => [ 'shape' => 'ListEventTrackersResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListFilters' => [ 'name' => 'ListFilters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFiltersRequest', ], 'output' => [ 'shape' => 'ListFiltersResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListRecipes' => [ 'name' => 'ListRecipes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRecipesRequest', ], 'output' => [ 'shape' => 'ListRecipesResponse', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListSchemas' => [ 'name' => 'ListSchemas', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSchemasRequest', ], 'output' => [ 'shape' => 'ListSchemasResponse', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListSolutionVersions' => [ 'name' => 'ListSolutionVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSolutionVersionsRequest', ], 'output' => [ 'shape' => 'ListSolutionVersionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListSolutions' => [ 'name' => 'ListSolutions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSolutionsRequest', ], 'output' => [ 'shape' => 'ListSolutionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'StopSolutionVersionCreation' => [ 'name' => 'StopSolutionVersionCreation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopSolutionVersionCreationRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'UpdateCampaign' => [ 'name' => 'UpdateCampaign', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateCampaignRequest', ], 'output' => [ 'shape' => 'UpdateCampaignResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccountId' => [ 'type' => 'string', 'max' => 256, ], 'Algorithm' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'algorithmArn' => [ 'shape' => 'Arn', ], 'algorithmImage' => [ 'shape' => 'AlgorithmImage', ], 'defaultHyperParameters' => [ 'shape' => 'HyperParameters', ], 'defaultHyperParameterRanges' => [ 'shape' => 'DefaultHyperParameterRanges', ], 'defaultResourceConfig' => [ 'shape' => 'ResourceConfig', ], 'trainingInputMode' => [ 'shape' => 'TrainingInputMode', ], 'roleArn' => [ 'shape' => 'Arn', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], ], ], 'AlgorithmImage' => [ 'type' => 'structure', 'required' => [ 'dockerURI', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'dockerURI' => [ 'shape' => 'DockerURI', ], ], ], 'Arn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:([a-z\\d-]+):personalize:.*:.*:.+', ], 'ArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], 'max' => 100, ], 'AutoMLConfig' => [ 'type' => 'structure', 'members' => [ 'metricName' => [ 'shape' => 'MetricName', ], 'recipeList' => [ 'shape' => 'ArnList', ], ], ], 'AutoMLResult' => [ 'type' => 'structure', 'members' => [ 'bestRecipeArn' => [ 'shape' => 'Arn', ], ], ], 'AvroSchema' => [ 'type' => 'string', 'max' => 10000, ], 'BatchInferenceJob' => [ 'type' => 'structure', 'members' => [ 'jobName' => [ 'shape' => 'Name', ], 'batchInferenceJobArn' => [ 'shape' => 'Arn', ], 'filterArn' => [ 'shape' => 'Arn', ], 'failureReason' => [ 'shape' => 'FailureReason', ], 'solutionVersionArn' => [ 'shape' => 'Arn', ], 'numResults' => [ 'shape' => 'NumBatchResults', ], 'jobInput' => [ 'shape' => 'BatchInferenceJobInput', ], 'jobOutput' => [ 'shape' => 'BatchInferenceJobOutput', ], 'batchInferenceJobConfig' => [ 'shape' => 'BatchInferenceJobConfig', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], ], ], 'BatchInferenceJobConfig' => [ 'type' => 'structure', 'members' => [ 'itemExplorationConfig' => [ 'shape' => 'HyperParameters', ], ], ], 'BatchInferenceJobInput' => [ 'type' => 'structure', 'required' => [ 's3DataSource', ], 'members' => [ 's3DataSource' => [ 'shape' => 'S3DataConfig', ], ], ], 'BatchInferenceJobOutput' => [ 'type' => 'structure', 'required' => [ 's3DataDestination', ], 'members' => [ 's3DataDestination' => [ 'shape' => 'S3DataConfig', ], ], ], 'BatchInferenceJobSummary' => [ 'type' => 'structure', 'members' => [ 'batchInferenceJobArn' => [ 'shape' => 'Arn', ], 'jobName' => [ 'shape' => 'Name', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'failureReason' => [ 'shape' => 'FailureReason', ], 'solutionVersionArn' => [ 'shape' => 'Arn', ], ], ], 'BatchInferenceJobs' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchInferenceJobSummary', ], 'max' => 100, ], 'Boolean' => [ 'type' => 'boolean', ], 'Campaign' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'campaignArn' => [ 'shape' => 'Arn', ], 'solutionVersionArn' => [ 'shape' => 'Arn', ], 'minProvisionedTPS' => [ 'shape' => 'TransactionsPerSecond', ], 'campaignConfig' => [ 'shape' => 'CampaignConfig', ], 'status' => [ 'shape' => 'Status', ], 'failureReason' => [ 'shape' => 'FailureReason', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'latestCampaignUpdate' => [ 'shape' => 'CampaignUpdateSummary', ], ], ], 'CampaignConfig' => [ 'type' => 'structure', 'members' => [ 'itemExplorationConfig' => [ 'shape' => 'HyperParameters', ], ], ], 'CampaignSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'campaignArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'failureReason' => [ 'shape' => 'FailureReason', ], ], ], 'CampaignUpdateSummary' => [ 'type' => 'structure', 'members' => [ 'solutionVersionArn' => [ 'shape' => 'Arn', ], 'minProvisionedTPS' => [ 'shape' => 'TransactionsPerSecond', ], 'campaignConfig' => [ 'shape' => 'CampaignConfig', ], 'status' => [ 'shape' => 'Status', ], 'failureReason' => [ 'shape' => 'FailureReason', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], ], ], 'Campaigns' => [ 'type' => 'list', 'member' => [ 'shape' => 'CampaignSummary', ], 'max' => 100, ], 'CategoricalHyperParameterRange' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ParameterName', ], 'values' => [ 'shape' => 'CategoricalValues', ], ], ], 'CategoricalHyperParameterRanges' => [ 'type' => 'list', 'member' => [ 'shape' => 'CategoricalHyperParameterRange', ], 'max' => 100, ], 'CategoricalValue' => [ 'type' => 'string', 'max' => 1000, ], 'CategoricalValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'CategoricalValue', ], 'max' => 100, ], 'ContinuousHyperParameterRange' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ParameterName', ], 'minValue' => [ 'shape' => 'ContinuousMinValue', ], 'maxValue' => [ 'shape' => 'ContinuousMaxValue', ], ], ], 'ContinuousHyperParameterRanges' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContinuousHyperParameterRange', ], 'max' => 100, ], 'ContinuousMaxValue' => [ 'type' => 'double', 'min' => -1000000, ], 'ContinuousMinValue' => [ 'type' => 'double', 'min' => -1000000, ], 'CreateBatchInferenceJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobName', 'solutionVersionArn', 'jobInput', 'jobOutput', 'roleArn', ], 'members' => [ 'jobName' => [ 'shape' => 'Name', ], 'solutionVersionArn' => [ 'shape' => 'Arn', ], 'filterArn' => [ 'shape' => 'Arn', ], 'numResults' => [ 'shape' => 'NumBatchResults', ], 'jobInput' => [ 'shape' => 'BatchInferenceJobInput', ], 'jobOutput' => [ 'shape' => 'BatchInferenceJobOutput', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'batchInferenceJobConfig' => [ 'shape' => 'BatchInferenceJobConfig', ], ], ], 'CreateBatchInferenceJobResponse' => [ 'type' => 'structure', 'members' => [ 'batchInferenceJobArn' => [ 'shape' => 'Arn', ], ], ], 'CreateCampaignRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'solutionVersionArn', 'minProvisionedTPS', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'solutionVersionArn' => [ 'shape' => 'Arn', ], 'minProvisionedTPS' => [ 'shape' => 'TransactionsPerSecond', ], 'campaignConfig' => [ 'shape' => 'CampaignConfig', ], ], ], 'CreateCampaignResponse' => [ 'type' => 'structure', 'members' => [ 'campaignArn' => [ 'shape' => 'Arn', ], ], ], 'CreateDatasetExportJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobName', 'datasetArn', 'roleArn', 'jobOutput', ], 'members' => [ 'jobName' => [ 'shape' => 'Name', ], 'datasetArn' => [ 'shape' => 'Arn', ], 'ingestionMode' => [ 'shape' => 'IngestionMode', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'jobOutput' => [ 'shape' => 'DatasetExportJobOutput', ], ], ], 'CreateDatasetExportJobResponse' => [ 'type' => 'structure', 'members' => [ 'datasetExportJobArn' => [ 'shape' => 'Arn', ], ], ], 'CreateDatasetGroupRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'CreateDatasetGroupResponse' => [ 'type' => 'structure', 'members' => [ 'datasetGroupArn' => [ 'shape' => 'Arn', ], ], ], 'CreateDatasetImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobName', 'datasetArn', 'dataSource', 'roleArn', ], 'members' => [ 'jobName' => [ 'shape' => 'Name', ], 'datasetArn' => [ 'shape' => 'Arn', ], 'dataSource' => [ 'shape' => 'DataSource', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'CreateDatasetImportJobResponse' => [ 'type' => 'structure', 'members' => [ 'datasetImportJobArn' => [ 'shape' => 'Arn', ], ], ], 'CreateDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'schemaArn', 'datasetGroupArn', 'datasetType', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'schemaArn' => [ 'shape' => 'Arn', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'datasetType' => [ 'shape' => 'DatasetType', ], ], ], 'CreateDatasetResponse' => [ 'type' => 'structure', 'members' => [ 'datasetArn' => [ 'shape' => 'Arn', ], ], ], 'CreateEventTrackerRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'datasetGroupArn', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], ], ], 'CreateEventTrackerResponse' => [ 'type' => 'structure', 'members' => [ 'eventTrackerArn' => [ 'shape' => 'Arn', ], 'trackingId' => [ 'shape' => 'TrackingId', ], ], ], 'CreateFilterRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'datasetGroupArn', 'filterExpression', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'filterExpression' => [ 'shape' => 'FilterExpression', ], ], ], 'CreateFilterResponse' => [ 'type' => 'structure', 'members' => [ 'filterArn' => [ 'shape' => 'Arn', ], ], ], 'CreateSchemaRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'schema', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'schema' => [ 'shape' => 'AvroSchema', ], ], ], 'CreateSchemaResponse' => [ 'type' => 'structure', 'members' => [ 'schemaArn' => [ 'shape' => 'Arn', ], ], ], 'CreateSolutionRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'datasetGroupArn', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'performHPO' => [ 'shape' => 'Boolean', ], 'performAutoML' => [ 'shape' => 'PerformAutoML', ], 'recipeArn' => [ 'shape' => 'Arn', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'eventType' => [ 'shape' => 'EventType', ], 'solutionConfig' => [ 'shape' => 'SolutionConfig', ], ], ], 'CreateSolutionResponse' => [ 'type' => 'structure', 'members' => [ 'solutionArn' => [ 'shape' => 'Arn', ], ], ], 'CreateSolutionVersionRequest' => [ 'type' => 'structure', 'required' => [ 'solutionArn', ], 'members' => [ 'solutionArn' => [ 'shape' => 'Arn', ], 'trainingMode' => [ 'shape' => 'TrainingMode', ], ], ], 'CreateSolutionVersionResponse' => [ 'type' => 'structure', 'members' => [ 'solutionVersionArn' => [ 'shape' => 'Arn', ], ], ], 'DataSource' => [ 'type' => 'structure', 'members' => [ 'dataLocation' => [ 'shape' => 'S3Location', ], ], ], 'Dataset' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'datasetArn' => [ 'shape' => 'Arn', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'datasetType' => [ 'shape' => 'DatasetType', ], 'schemaArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], ], ], 'DatasetExportJob' => [ 'type' => 'structure', 'members' => [ 'jobName' => [ 'shape' => 'Name', ], 'datasetExportJobArn' => [ 'shape' => 'Arn', ], 'datasetArn' => [ 'shape' => 'Arn', ], 'ingestionMode' => [ 'shape' => 'IngestionMode', ], 'roleArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'Status', ], 'jobOutput' => [ 'shape' => 'DatasetExportJobOutput', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'failureReason' => [ 'shape' => 'FailureReason', ], ], ], 'DatasetExportJobOutput' => [ 'type' => 'structure', 'required' => [ 's3DataDestination', ], 'members' => [ 's3DataDestination' => [ 'shape' => 'S3DataConfig', ], ], ], 'DatasetExportJobSummary' => [ 'type' => 'structure', 'members' => [ 'datasetExportJobArn' => [ 'shape' => 'Arn', ], 'jobName' => [ 'shape' => 'Name', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'failureReason' => [ 'shape' => 'FailureReason', ], ], ], 'DatasetExportJobs' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatasetExportJobSummary', ], 'max' => 100, ], 'DatasetGroup' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'Status', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'failureReason' => [ 'shape' => 'FailureReason', ], ], ], 'DatasetGroupSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'failureReason' => [ 'shape' => 'FailureReason', ], ], ], 'DatasetGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatasetGroupSummary', ], 'max' => 100, ], 'DatasetImportJob' => [ 'type' => 'structure', 'members' => [ 'jobName' => [ 'shape' => 'Name', ], 'datasetImportJobArn' => [ 'shape' => 'Arn', ], 'datasetArn' => [ 'shape' => 'Arn', ], 'dataSource' => [ 'shape' => 'DataSource', ], 'roleArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'failureReason' => [ 'shape' => 'FailureReason', ], ], ], 'DatasetImportJobSummary' => [ 'type' => 'structure', 'members' => [ 'datasetImportJobArn' => [ 'shape' => 'Arn', ], 'jobName' => [ 'shape' => 'Name', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'failureReason' => [ 'shape' => 'FailureReason', ], ], ], 'DatasetImportJobs' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatasetImportJobSummary', ], 'max' => 100, ], 'DatasetSchema' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'schemaArn' => [ 'shape' => 'Arn', ], 'schema' => [ 'shape' => 'AvroSchema', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], ], ], 'DatasetSchemaSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'schemaArn' => [ 'shape' => 'Arn', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], ], ], 'DatasetSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'datasetArn' => [ 'shape' => 'Arn', ], 'datasetType' => [ 'shape' => 'DatasetType', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], ], ], 'DatasetType' => [ 'type' => 'string', 'max' => 256, ], 'Datasets' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatasetSummary', ], 'max' => 100, ], 'Date' => [ 'type' => 'timestamp', ], 'DefaultCategoricalHyperParameterRange' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ParameterName', ], 'values' => [ 'shape' => 'CategoricalValues', ], 'isTunable' => [ 'shape' => 'Tunable', ], ], ], 'DefaultCategoricalHyperParameterRanges' => [ 'type' => 'list', 'member' => [ 'shape' => 'DefaultCategoricalHyperParameterRange', ], 'max' => 100, ], 'DefaultContinuousHyperParameterRange' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ParameterName', ], 'minValue' => [ 'shape' => 'ContinuousMinValue', ], 'maxValue' => [ 'shape' => 'ContinuousMaxValue', ], 'isTunable' => [ 'shape' => 'Tunable', ], ], ], 'DefaultContinuousHyperParameterRanges' => [ 'type' => 'list', 'member' => [ 'shape' => 'DefaultContinuousHyperParameterRange', ], 'max' => 100, ], 'DefaultHyperParameterRanges' => [ 'type' => 'structure', 'members' => [ 'integerHyperParameterRanges' => [ 'shape' => 'DefaultIntegerHyperParameterRanges', ], 'continuousHyperParameterRanges' => [ 'shape' => 'DefaultContinuousHyperParameterRanges', ], 'categoricalHyperParameterRanges' => [ 'shape' => 'DefaultCategoricalHyperParameterRanges', ], ], ], 'DefaultIntegerHyperParameterRange' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ParameterName', ], 'minValue' => [ 'shape' => 'IntegerMinValue', ], 'maxValue' => [ 'shape' => 'IntegerMaxValue', ], 'isTunable' => [ 'shape' => 'Tunable', ], ], ], 'DefaultIntegerHyperParameterRanges' => [ 'type' => 'list', 'member' => [ 'shape' => 'DefaultIntegerHyperParameterRange', ], 'max' => 100, ], 'DeleteCampaignRequest' => [ 'type' => 'structure', 'required' => [ 'campaignArn', ], 'members' => [ 'campaignArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteDatasetGroupRequest' => [ 'type' => 'structure', 'required' => [ 'datasetGroupArn', ], 'members' => [ 'datasetGroupArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'datasetArn', ], 'members' => [ 'datasetArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteEventTrackerRequest' => [ 'type' => 'structure', 'required' => [ 'eventTrackerArn', ], 'members' => [ 'eventTrackerArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteFilterRequest' => [ 'type' => 'structure', 'required' => [ 'filterArn', ], 'members' => [ 'filterArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteSchemaRequest' => [ 'type' => 'structure', 'required' => [ 'schemaArn', ], 'members' => [ 'schemaArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteSolutionRequest' => [ 'type' => 'structure', 'required' => [ 'solutionArn', ], 'members' => [ 'solutionArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeAlgorithmRequest' => [ 'type' => 'structure', 'required' => [ 'algorithmArn', ], 'members' => [ 'algorithmArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeAlgorithmResponse' => [ 'type' => 'structure', 'members' => [ 'algorithm' => [ 'shape' => 'Algorithm', ], ], ], 'DescribeBatchInferenceJobRequest' => [ 'type' => 'structure', 'required' => [ 'batchInferenceJobArn', ], 'members' => [ 'batchInferenceJobArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeBatchInferenceJobResponse' => [ 'type' => 'structure', 'members' => [ 'batchInferenceJob' => [ 'shape' => 'BatchInferenceJob', ], ], ], 'DescribeCampaignRequest' => [ 'type' => 'structure', 'required' => [ 'campaignArn', ], 'members' => [ 'campaignArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeCampaignResponse' => [ 'type' => 'structure', 'members' => [ 'campaign' => [ 'shape' => 'Campaign', ], ], ], 'DescribeDatasetExportJobRequest' => [ 'type' => 'structure', 'required' => [ 'datasetExportJobArn', ], 'members' => [ 'datasetExportJobArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeDatasetExportJobResponse' => [ 'type' => 'structure', 'members' => [ 'datasetExportJob' => [ 'shape' => 'DatasetExportJob', ], ], ], 'DescribeDatasetGroupRequest' => [ 'type' => 'structure', 'required' => [ 'datasetGroupArn', ], 'members' => [ 'datasetGroupArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeDatasetGroupResponse' => [ 'type' => 'structure', 'members' => [ 'datasetGroup' => [ 'shape' => 'DatasetGroup', ], ], ], 'DescribeDatasetImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'datasetImportJobArn', ], 'members' => [ 'datasetImportJobArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeDatasetImportJobResponse' => [ 'type' => 'structure', 'members' => [ 'datasetImportJob' => [ 'shape' => 'DatasetImportJob', ], ], ], 'DescribeDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'datasetArn', ], 'members' => [ 'datasetArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeDatasetResponse' => [ 'type' => 'structure', 'members' => [ 'dataset' => [ 'shape' => 'Dataset', ], ], ], 'DescribeEventTrackerRequest' => [ 'type' => 'structure', 'required' => [ 'eventTrackerArn', ], 'members' => [ 'eventTrackerArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeEventTrackerResponse' => [ 'type' => 'structure', 'members' => [ 'eventTracker' => [ 'shape' => 'EventTracker', ], ], ], 'DescribeFeatureTransformationRequest' => [ 'type' => 'structure', 'required' => [ 'featureTransformationArn', ], 'members' => [ 'featureTransformationArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeFeatureTransformationResponse' => [ 'type' => 'structure', 'members' => [ 'featureTransformation' => [ 'shape' => 'FeatureTransformation', ], ], ], 'DescribeFilterRequest' => [ 'type' => 'structure', 'required' => [ 'filterArn', ], 'members' => [ 'filterArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeFilterResponse' => [ 'type' => 'structure', 'members' => [ 'filter' => [ 'shape' => 'Filter', ], ], ], 'DescribeRecipeRequest' => [ 'type' => 'structure', 'required' => [ 'recipeArn', ], 'members' => [ 'recipeArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeRecipeResponse' => [ 'type' => 'structure', 'members' => [ 'recipe' => [ 'shape' => 'Recipe', ], ], ], 'DescribeSchemaRequest' => [ 'type' => 'structure', 'required' => [ 'schemaArn', ], 'members' => [ 'schemaArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeSchemaResponse' => [ 'type' => 'structure', 'members' => [ 'schema' => [ 'shape' => 'DatasetSchema', ], ], ], 'DescribeSolutionRequest' => [ 'type' => 'structure', 'required' => [ 'solutionArn', ], 'members' => [ 'solutionArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeSolutionResponse' => [ 'type' => 'structure', 'members' => [ 'solution' => [ 'shape' => 'Solution', ], ], ], 'DescribeSolutionVersionRequest' => [ 'type' => 'structure', 'required' => [ 'solutionVersionArn', ], 'members' => [ 'solutionVersionArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeSolutionVersionResponse' => [ 'type' => 'structure', 'members' => [ 'solutionVersion' => [ 'shape' => 'SolutionVersion', ], ], ], 'Description' => [ 'type' => 'string', ], 'DockerURI' => [ 'type' => 'string', 'max' => 256, ], 'ErrorMessage' => [ 'type' => 'string', ], 'EventTracker' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'eventTrackerArn' => [ 'shape' => 'Arn', ], 'accountId' => [ 'shape' => 'AccountId', ], 'trackingId' => [ 'shape' => 'TrackingId', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], ], ], 'EventTrackerSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'eventTrackerArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], ], ], 'EventTrackers' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventTrackerSummary', ], 'max' => 100, ], 'EventType' => [ 'type' => 'string', 'max' => 256, ], 'EventValueThreshold' => [ 'type' => 'string', 'max' => 256, ], 'FailureReason' => [ 'type' => 'string', ], 'FeatureTransformation' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'featureTransformationArn' => [ 'shape' => 'Arn', ], 'defaultParameters' => [ 'shape' => 'FeaturizationParameters', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'status' => [ 'shape' => 'Status', ], ], ], 'FeatureTransformationParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'ParameterName', ], 'value' => [ 'shape' => 'ParameterValue', ], 'max' => 100, ], 'FeaturizationParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'ParameterName', ], 'value' => [ 'shape' => 'ParameterValue', ], 'max' => 100, ], 'Filter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'filterArn' => [ 'shape' => 'Arn', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'failureReason' => [ 'shape' => 'FailureReason', ], 'filterExpression' => [ 'shape' => 'FilterExpression', ], 'status' => [ 'shape' => 'Status', ], ], ], 'FilterExpression' => [ 'type' => 'string', 'max' => 2500, 'min' => 1, 'sensitive' => true, ], 'FilterSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'filterArn' => [ 'shape' => 'Arn', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'failureReason' => [ 'shape' => 'FailureReason', ], 'status' => [ 'shape' => 'Status', ], ], ], 'Filters' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterSummary', ], 'max' => 100, ], 'GetSolutionMetricsRequest' => [ 'type' => 'structure', 'required' => [ 'solutionVersionArn', ], 'members' => [ 'solutionVersionArn' => [ 'shape' => 'Arn', ], ], ], 'GetSolutionMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'solutionVersionArn' => [ 'shape' => 'Arn', ], 'metrics' => [ 'shape' => 'Metrics', ], ], ], 'HPOConfig' => [ 'type' => 'structure', 'members' => [ 'hpoObjective' => [ 'shape' => 'HPOObjective', ], 'hpoResourceConfig' => [ 'shape' => 'HPOResourceConfig', ], 'algorithmHyperParameterRanges' => [ 'shape' => 'HyperParameterRanges', ], ], ], 'HPOObjective' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'HPOObjectiveType', ], 'metricName' => [ 'shape' => 'MetricName', ], 'metricRegex' => [ 'shape' => 'MetricRegex', ], ], ], 'HPOObjectiveType' => [ 'type' => 'string', 'max' => 256, ], 'HPOResource' => [ 'type' => 'string', 'max' => 256, ], 'HPOResourceConfig' => [ 'type' => 'structure', 'members' => [ 'maxNumberOfTrainingJobs' => [ 'shape' => 'HPOResource', ], 'maxParallelTrainingJobs' => [ 'shape' => 'HPOResource', ], ], ], 'HyperParameterRanges' => [ 'type' => 'structure', 'members' => [ 'integerHyperParameterRanges' => [ 'shape' => 'IntegerHyperParameterRanges', ], 'continuousHyperParameterRanges' => [ 'shape' => 'ContinuousHyperParameterRanges', ], 'categoricalHyperParameterRanges' => [ 'shape' => 'CategoricalHyperParameterRanges', ], ], ], 'HyperParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'ParameterName', ], 'value' => [ 'shape' => 'ParameterValue', ], 'max' => 100, ], 'IngestionMode' => [ 'type' => 'string', 'enum' => [ 'BULK', 'PUT', 'ALL', ], ], 'IntegerHyperParameterRange' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ParameterName', ], 'minValue' => [ 'shape' => 'IntegerMinValue', ], 'maxValue' => [ 'shape' => 'IntegerMaxValue', ], ], ], 'IntegerHyperParameterRanges' => [ 'type' => 'list', 'member' => [ 'shape' => 'IntegerHyperParameterRange', ], 'max' => 100, ], 'IntegerMaxValue' => [ 'type' => 'integer', 'max' => 1000000, ], 'IntegerMinValue' => [ 'type' => 'integer', 'min' => -1000000, ], 'InvalidInputException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidNextTokenException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ItemAttribute' => [ 'type' => 'string', 'max' => 150, 'min' => 1, ], 'KmsKeyArn' => [ 'type' => 'string', 'pattern' => 'arn:aws.*:kms:.*:[0-9]{12}:key/.*', ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ListBatchInferenceJobsRequest' => [ 'type' => 'structure', 'members' => [ 'solutionVersionArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListBatchInferenceJobsResponse' => [ 'type' => 'structure', 'members' => [ 'batchInferenceJobs' => [ 'shape' => 'BatchInferenceJobs', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCampaignsRequest' => [ 'type' => 'structure', 'members' => [ 'solutionArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListCampaignsResponse' => [ 'type' => 'structure', 'members' => [ 'campaigns' => [ 'shape' => 'Campaigns', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDatasetExportJobsRequest' => [ 'type' => 'structure', 'members' => [ 'datasetArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListDatasetExportJobsResponse' => [ 'type' => 'structure', 'members' => [ 'datasetExportJobs' => [ 'shape' => 'DatasetExportJobs', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDatasetGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListDatasetGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'datasetGroups' => [ 'shape' => 'DatasetGroups', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDatasetImportJobsRequest' => [ 'type' => 'structure', 'members' => [ 'datasetArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListDatasetImportJobsResponse' => [ 'type' => 'structure', 'members' => [ 'datasetImportJobs' => [ 'shape' => 'DatasetImportJobs', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDatasetsRequest' => [ 'type' => 'structure', 'members' => [ 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListDatasetsResponse' => [ 'type' => 'structure', 'members' => [ 'datasets' => [ 'shape' => 'Datasets', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEventTrackersRequest' => [ 'type' => 'structure', 'members' => [ 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListEventTrackersResponse' => [ 'type' => 'structure', 'members' => [ 'eventTrackers' => [ 'shape' => 'EventTrackers', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFiltersRequest' => [ 'type' => 'structure', 'members' => [ 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListFiltersResponse' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'Filters', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRecipesRequest' => [ 'type' => 'structure', 'members' => [ 'recipeProvider' => [ 'shape' => 'RecipeProvider', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListRecipesResponse' => [ 'type' => 'structure', 'members' => [ 'recipes' => [ 'shape' => 'Recipes', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSchemasRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListSchemasResponse' => [ 'type' => 'structure', 'members' => [ 'schemas' => [ 'shape' => 'Schemas', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSolutionVersionsRequest' => [ 'type' => 'structure', 'members' => [ 'solutionArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListSolutionVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'solutionVersions' => [ 'shape' => 'SolutionVersions', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSolutionsRequest' => [ 'type' => 'structure', 'members' => [ 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListSolutionsResponse' => [ 'type' => 'structure', 'members' => [ 'solutions' => [ 'shape' => 'Solutions', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MetricName' => [ 'type' => 'string', 'max' => 256, ], 'MetricRegex' => [ 'type' => 'string', 'max' => 256, ], 'MetricValue' => [ 'type' => 'double', ], 'Metrics' => [ 'type' => 'map', 'key' => [ 'shape' => 'MetricName', ], 'value' => [ 'shape' => 'MetricValue', ], 'max' => 100, ], 'Name' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9\\-_]*', ], 'NextToken' => [ 'type' => 'string', 'max' => 1300, ], 'NumBatchResults' => [ 'type' => 'integer', ], 'ObjectiveSensitivity' => [ 'type' => 'string', 'enum' => [ 'LOW', 'MEDIUM', 'HIGH', 'OFF', ], ], 'OptimizationObjective' => [ 'type' => 'structure', 'members' => [ 'itemAttribute' => [ 'shape' => 'ItemAttribute', ], 'objectiveSensitivity' => [ 'shape' => 'ObjectiveSensitivity', ], ], ], 'ParameterName' => [ 'type' => 'string', 'max' => 256, ], 'ParameterValue' => [ 'type' => 'string', 'max' => 1000, ], 'PerformAutoML' => [ 'type' => 'boolean', ], 'PerformHPO' => [ 'type' => 'boolean', ], 'Recipe' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'recipeArn' => [ 'shape' => 'Arn', ], 'algorithmArn' => [ 'shape' => 'Arn', ], 'featureTransformationArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'Status', ], 'description' => [ 'shape' => 'Description', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'recipeType' => [ 'shape' => 'RecipeType', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], ], ], 'RecipeProvider' => [ 'type' => 'string', 'enum' => [ 'SERVICE', ], ], 'RecipeSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'recipeArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], ], ], 'RecipeType' => [ 'type' => 'string', 'max' => 256, ], 'Recipes' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecipeSummary', ], 'max' => 100, ], 'ResourceAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceConfig' => [ 'type' => 'map', 'key' => [ 'shape' => 'ParameterName', ], 'value' => [ 'shape' => 'ParameterValue', ], 'max' => 100, ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'RoleArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:([a-z\\d-]+):iam::\\d{12}:role/?[a-zA-Z_0-9+=,.@\\-_/]+', ], 'S3DataConfig' => [ 'type' => 'structure', 'required' => [ 'path', ], 'members' => [ 'path' => [ 'shape' => 'S3Location', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'S3Location' => [ 'type' => 'string', 'max' => 256, 'pattern' => '(s3|http|https)://.+', ], 'Schemas' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatasetSchemaSummary', ], 'max' => 100, ], 'Solution' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'solutionArn' => [ 'shape' => 'Arn', ], 'performHPO' => [ 'shape' => 'PerformHPO', ], 'performAutoML' => [ 'shape' => 'PerformAutoML', ], 'recipeArn' => [ 'shape' => 'Arn', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'eventType' => [ 'shape' => 'EventType', ], 'solutionConfig' => [ 'shape' => 'SolutionConfig', ], 'autoMLResult' => [ 'shape' => 'AutoMLResult', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'latestSolutionVersion' => [ 'shape' => 'SolutionVersionSummary', ], ], ], 'SolutionConfig' => [ 'type' => 'structure', 'members' => [ 'eventValueThreshold' => [ 'shape' => 'EventValueThreshold', ], 'hpoConfig' => [ 'shape' => 'HPOConfig', ], 'algorithmHyperParameters' => [ 'shape' => 'HyperParameters', ], 'featureTransformationParameters' => [ 'shape' => 'FeatureTransformationParameters', ], 'autoMLConfig' => [ 'shape' => 'AutoMLConfig', ], 'optimizationObjective' => [ 'shape' => 'OptimizationObjective', ], ], ], 'SolutionSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'solutionArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], ], ], 'SolutionVersion' => [ 'type' => 'structure', 'members' => [ 'solutionVersionArn' => [ 'shape' => 'Arn', ], 'solutionArn' => [ 'shape' => 'Arn', ], 'performHPO' => [ 'shape' => 'PerformHPO', ], 'performAutoML' => [ 'shape' => 'PerformAutoML', ], 'recipeArn' => [ 'shape' => 'Arn', ], 'eventType' => [ 'shape' => 'EventType', ], 'datasetGroupArn' => [ 'shape' => 'Arn', ], 'solutionConfig' => [ 'shape' => 'SolutionConfig', ], 'trainingHours' => [ 'shape' => 'TrainingHours', ], 'trainingMode' => [ 'shape' => 'TrainingMode', ], 'tunedHPOParams' => [ 'shape' => 'TunedHPOParams', ], 'status' => [ 'shape' => 'Status', ], 'failureReason' => [ 'shape' => 'FailureReason', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], ], ], 'SolutionVersionSummary' => [ 'type' => 'structure', 'members' => [ 'solutionVersionArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'Status', ], 'creationDateTime' => [ 'shape' => 'Date', ], 'lastUpdatedDateTime' => [ 'shape' => 'Date', ], 'failureReason' => [ 'shape' => 'FailureReason', ], ], ], 'SolutionVersions' => [ 'type' => 'list', 'member' => [ 'shape' => 'SolutionVersionSummary', ], 'max' => 100, ], 'Solutions' => [ 'type' => 'list', 'member' => [ 'shape' => 'SolutionSummary', ], 'max' => 100, ], 'Status' => [ 'type' => 'string', 'max' => 256, ], 'StopSolutionVersionCreationRequest' => [ 'type' => 'structure', 'required' => [ 'solutionVersionArn', ], 'members' => [ 'solutionVersionArn' => [ 'shape' => 'Arn', ], ], ], 'TrackingId' => [ 'type' => 'string', 'max' => 256, ], 'TrainingHours' => [ 'type' => 'double', 'min' => 0, ], 'TrainingInputMode' => [ 'type' => 'string', 'max' => 256, ], 'TrainingMode' => [ 'type' => 'string', 'enum' => [ 'FULL', 'UPDATE', ], ], 'TransactionsPerSecond' => [ 'type' => 'integer', 'min' => 1, ], 'Tunable' => [ 'type' => 'boolean', ], 'TunedHPOParams' => [ 'type' => 'structure', 'members' => [ 'algorithmHyperParameters' => [ 'shape' => 'HyperParameters', ], ], ], 'UpdateCampaignRequest' => [ 'type' => 'structure', 'required' => [ 'campaignArn', ], 'members' => [ 'campaignArn' => [ 'shape' => 'Arn', ], 'solutionVersionArn' => [ 'shape' => 'Arn', ], 'minProvisionedTPS' => [ 'shape' => 'TransactionsPerSecond', ], 'campaignConfig' => [ 'shape' => 'CampaignConfig', ], ], ], 'UpdateCampaignResponse' => [ 'type' => 'structure', 'members' => [ 'campaignArn' => [ 'shape' => 'Arn', ], ], ], ],];
