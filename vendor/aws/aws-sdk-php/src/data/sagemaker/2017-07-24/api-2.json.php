<?php
// This file was auto-generated from sdk-root/src/data/sagemaker/2017-07-24/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-07-24', 'endpointPrefix' => 'api.sagemaker', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceAbbreviation' => 'SageMaker', 'serviceFullName' => 'Amazon SageMaker Service', 'serviceId' => 'SageMaker', 'signatureVersion' => 'v4', 'signingName' => 'sagemaker', 'targetPrefix' => 'SageMaker', 'uid' => 'sagemaker-2017-07-24', ], 'operations' => [ 'AddAssociation' => [ 'name' => 'AddAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddAssociationRequest', ], 'output' => [ 'shape' => 'AddAssociationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'AddTags' => [ 'name' => 'AddTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddTagsInput', ], 'output' => [ 'shape' => 'AddTagsOutput', ], ], 'AssociateTrialComponent' => [ 'name' => 'AssociateTrialComponent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateTrialComponentRequest', ], 'output' => [ 'shape' => 'AssociateTrialComponentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'CreateAction' => [ 'name' => 'CreateAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateActionRequest', ], 'output' => [ 'shape' => 'CreateActionResponse', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'CreateAlgorithm' => [ 'name' => 'CreateAlgorithm', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAlgorithmInput', ], 'output' => [ 'shape' => 'CreateAlgorithmOutput', ], ], 'CreateApp' => [ 'name' => 'CreateApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAppRequest', ], 'output' => [ 'shape' => 'CreateAppResponse', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], [ 'shape' => 'ResourceInUse', ], ], ], 'CreateAppImageConfig' => [ 'name' => 'CreateAppImageConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAppImageConfigRequest', ], 'output' => [ 'shape' => 'CreateAppImageConfigResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUse', ], ], ], 'CreateArtifact' => [ 'name' => 'CreateArtifact', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateArtifactRequest', ], 'output' => [ 'shape' => 'CreateArtifactResponse', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'CreateAutoMLJob' => [ 'name' => 'CreateAutoMLJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAutoMLJobRequest', ], 'output' => [ 'shape' => 'CreateAutoMLJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUse', ], [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'CreateCodeRepository' => [ 'name' => 'CreateCodeRepository', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCodeRepositoryInput', ], 'output' => [ 'shape' => 'CreateCodeRepositoryOutput', ], ], 'CreateCompilationJob' => [ 'name' => 'CreateCompilationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCompilationJobRequest', ], 'output' => [ 'shape' => 'CreateCompilationJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUse', ], [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'CreateContext' => [ 'name' => 'CreateContext', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateContextRequest', ], 'output' => [ 'shape' => 'CreateContextResponse', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'CreateDataQualityJobDefinition' => [ 'name' => 'CreateDataQualityJobDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDataQualityJobDefinitionRequest', ], 'output' => [ 'shape' => 'CreateDataQualityJobDefinitionResponse', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], [ 'shape' => 'ResourceInUse', ], ], ], 'CreateDeviceFleet' => [ 'name' => 'CreateDeviceFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDeviceFleetRequest', ], 'errors' => [ [ 'shape' => 'ResourceInUse', ], [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'CreateDomain' => [ 'name' => 'CreateDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDomainRequest', ], 'output' => [ 'shape' => 'CreateDomainResponse', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], [ 'shape' => 'ResourceInUse', ], ], ], 'CreateEdgePackagingJob' => [ 'name' => 'CreateEdgePackagingJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEdgePackagingJobRequest', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'CreateEndpoint' => [ 'name' => 'CreateEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEndpointInput', ], 'output' => [ 'shape' => 'CreateEndpointOutput', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'CreateEndpointConfig' => [ 'name' => 'CreateEndpointConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEndpointConfigInput', ], 'output' => [ 'shape' => 'CreateEndpointConfigOutput', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'CreateExperiment' => [ 'name' => 'CreateExperiment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateExperimentRequest', ], 'output' => [ 'shape' => 'CreateExperimentResponse', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'CreateFeatureGroup' => [ 'name' => 'CreateFeatureGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateFeatureGroupRequest', ], 'output' => [ 'shape' => 'CreateFeatureGroupResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUse', ], [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'CreateFlowDefinition' => [ 'name' => 'CreateFlowDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateFlowDefinitionRequest', ], 'output' => [ 'shape' => 'CreateFlowDefinitionResponse', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], [ 'shape' => 'ResourceInUse', ], ], ], 'CreateHumanTaskUi' => [ 'name' => 'CreateHumanTaskUi', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateHumanTaskUiRequest', ], 'output' => [ 'shape' => 'CreateHumanTaskUiResponse', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], [ 'shape' => 'ResourceInUse', ], ], ], 'CreateHyperParameterTuningJob' => [ 'name' => 'CreateHyperParameterTuningJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateHyperParameterTuningJobRequest', ], 'output' => [ 'shape' => 'CreateHyperParameterTuningJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUse', ], [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'CreateImage' => [ 'name' => 'CreateImage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateImageRequest', ], 'output' => [ 'shape' => 'CreateImageResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUse', ], [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'CreateImageVersion' => [ 'name' => 'CreateImageVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateImageVersionRequest', ], 'output' => [ 'shape' => 'CreateImageVersionResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUse', ], [ 'shape' => 'ResourceLimitExceeded', ], [ 'shape' => 'ResourceNotFound', ], ], ], 'CreateLabelingJob' => [ 'name' => 'CreateLabelingJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLabelingJobRequest', ], 'output' => [ 'shape' => 'CreateLabelingJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUse', ], [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'CreateModel' => [ 'name' => 'CreateModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateModelInput', ], 'output' => [ 'shape' => 'CreateModelOutput', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'CreateModelBiasJobDefinition' => [ 'name' => 'CreateModelBiasJobDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateModelBiasJobDefinitionRequest', ], 'output' => [ 'shape' => 'CreateModelBiasJobDefinitionResponse', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], [ 'shape' => 'ResourceInUse', ], ], ], 'CreateModelExplainabilityJobDefinition' => [ 'name' => 'CreateModelExplainabilityJobDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateModelExplainabilityJobDefinitionRequest', ], 'output' => [ 'shape' => 'CreateModelExplainabilityJobDefinitionResponse', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], [ 'shape' => 'ResourceInUse', ], ], ], 'CreateModelPackage' => [ 'name' => 'CreateModelPackage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateModelPackageInput', ], 'output' => [ 'shape' => 'CreateModelPackageOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'CreateModelPackageGroup' => [ 'name' => 'CreateModelPackageGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateModelPackageGroupInput', ], 'output' => [ 'shape' => 'CreateModelPackageGroupOutput', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'CreateModelQualityJobDefinition' => [ 'name' => 'CreateModelQualityJobDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateModelQualityJobDefinitionRequest', ], 'output' => [ 'shape' => 'CreateModelQualityJobDefinitionResponse', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], [ 'shape' => 'ResourceInUse', ], ], ], 'CreateMonitoringSchedule' => [ 'name' => 'CreateMonitoringSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateMonitoringScheduleRequest', ], 'output' => [ 'shape' => 'CreateMonitoringScheduleResponse', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], [ 'shape' => 'ResourceInUse', ], ], ], 'CreateNotebookInstance' => [ 'name' => 'CreateNotebookInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateNotebookInstanceInput', ], 'output' => [ 'shape' => 'CreateNotebookInstanceOutput', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'CreateNotebookInstanceLifecycleConfig' => [ 'name' => 'CreateNotebookInstanceLifecycleConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateNotebookInstanceLifecycleConfigInput', ], 'output' => [ 'shape' => 'CreateNotebookInstanceLifecycleConfigOutput', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'CreatePipeline' => [ 'name' => 'CreatePipeline', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePipelineRequest', ], 'output' => [ 'shape' => 'CreatePipelineResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'CreatePresignedDomainUrl' => [ 'name' => 'CreatePresignedDomainUrl', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePresignedDomainUrlRequest', ], 'output' => [ 'shape' => 'CreatePresignedDomainUrlResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'CreatePresignedNotebookInstanceUrl' => [ 'name' => 'CreatePresignedNotebookInstanceUrl', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePresignedNotebookInstanceUrlInput', ], 'output' => [ 'shape' => 'CreatePresignedNotebookInstanceUrlOutput', ], ], 'CreateProcessingJob' => [ 'name' => 'CreateProcessingJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateProcessingJobRequest', ], 'output' => [ 'shape' => 'CreateProcessingJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUse', ], [ 'shape' => 'ResourceLimitExceeded', ], [ 'shape' => 'ResourceNotFound', ], ], ], 'CreateProject' => [ 'name' => 'CreateProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateProjectInput', ], 'output' => [ 'shape' => 'CreateProjectOutput', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'CreateTrainingJob' => [ 'name' => 'CreateTrainingJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTrainingJobRequest', ], 'output' => [ 'shape' => 'CreateTrainingJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUse', ], [ 'shape' => 'ResourceLimitExceeded', ], [ 'shape' => 'ResourceNotFound', ], ], ], 'CreateTransformJob' => [ 'name' => 'CreateTransformJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTransformJobRequest', ], 'output' => [ 'shape' => 'CreateTransformJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUse', ], [ 'shape' => 'ResourceLimitExceeded', ], [ 'shape' => 'ResourceNotFound', ], ], ], 'CreateTrial' => [ 'name' => 'CreateTrial', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTrialRequest', ], 'output' => [ 'shape' => 'CreateTrialResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'CreateTrialComponent' => [ 'name' => 'CreateTrialComponent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTrialComponentRequest', ], 'output' => [ 'shape' => 'CreateTrialComponentResponse', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'CreateUserProfile' => [ 'name' => 'CreateUserProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateUserProfileRequest', ], 'output' => [ 'shape' => 'CreateUserProfileResponse', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], [ 'shape' => 'ResourceInUse', ], ], ], 'CreateWorkforce' => [ 'name' => 'CreateWorkforce', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateWorkforceRequest', ], 'output' => [ 'shape' => 'CreateWorkforceResponse', ], ], 'CreateWorkteam' => [ 'name' => 'CreateWorkteam', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateWorkteamRequest', ], 'output' => [ 'shape' => 'CreateWorkteamResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUse', ], [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'DeleteAction' => [ 'name' => 'DeleteAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteActionRequest', ], 'output' => [ 'shape' => 'DeleteActionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DeleteAlgorithm' => [ 'name' => 'DeleteAlgorithm', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAlgorithmInput', ], ], 'DeleteApp' => [ 'name' => 'DeleteApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAppRequest', ], 'errors' => [ [ 'shape' => 'ResourceInUse', ], [ 'shape' => 'ResourceNotFound', ], ], ], 'DeleteAppImageConfig' => [ 'name' => 'DeleteAppImageConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAppImageConfigRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DeleteArtifact' => [ 'name' => 'DeleteArtifact', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteArtifactRequest', ], 'output' => [ 'shape' => 'DeleteArtifactResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DeleteAssociation' => [ 'name' => 'DeleteAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAssociationRequest', ], 'output' => [ 'shape' => 'DeleteAssociationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DeleteCodeRepository' => [ 'name' => 'DeleteCodeRepository', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCodeRepositoryInput', ], ], 'DeleteContext' => [ 'name' => 'DeleteContext', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteContextRequest', ], 'output' => [ 'shape' => 'DeleteContextResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DeleteDataQualityJobDefinition' => [ 'name' => 'DeleteDataQualityJobDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDataQualityJobDefinitionRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DeleteDeviceFleet' => [ 'name' => 'DeleteDeviceFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDeviceFleetRequest', ], 'errors' => [ [ 'shape' => 'ResourceInUse', ], ], ], 'DeleteDomain' => [ 'name' => 'DeleteDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDomainRequest', ], 'errors' => [ [ 'shape' => 'ResourceInUse', ], [ 'shape' => 'ResourceNotFound', ], ], ], 'DeleteEndpoint' => [ 'name' => 'DeleteEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEndpointInput', ], ], 'DeleteEndpointConfig' => [ 'name' => 'DeleteEndpointConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEndpointConfigInput', ], ], 'DeleteExperiment' => [ 'name' => 'DeleteExperiment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteExperimentRequest', ], 'output' => [ 'shape' => 'DeleteExperimentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DeleteFeatureGroup' => [ 'name' => 'DeleteFeatureGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteFeatureGroupRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DeleteFlowDefinition' => [ 'name' => 'DeleteFlowDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteFlowDefinitionRequest', ], 'output' => [ 'shape' => 'DeleteFlowDefinitionResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUse', ], [ 'shape' => 'ResourceNotFound', ], ], ], 'DeleteHumanTaskUi' => [ 'name' => 'DeleteHumanTaskUi', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteHumanTaskUiRequest', ], 'output' => [ 'shape' => 'DeleteHumanTaskUiResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DeleteImage' => [ 'name' => 'DeleteImage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteImageRequest', ], 'output' => [ 'shape' => 'DeleteImageResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUse', ], [ 'shape' => 'ResourceNotFound', ], ], ], 'DeleteImageVersion' => [ 'name' => 'DeleteImageVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteImageVersionRequest', ], 'output' => [ 'shape' => 'DeleteImageVersionResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUse', ], [ 'shape' => 'ResourceNotFound', ], ], ], 'DeleteModel' => [ 'name' => 'DeleteModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteModelInput', ], ], 'DeleteModelBiasJobDefinition' => [ 'name' => 'DeleteModelBiasJobDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteModelBiasJobDefinitionRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DeleteModelExplainabilityJobDefinition' => [ 'name' => 'DeleteModelExplainabilityJobDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteModelExplainabilityJobDefinitionRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DeleteModelPackage' => [ 'name' => 'DeleteModelPackage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteModelPackageInput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], ], ], 'DeleteModelPackageGroup' => [ 'name' => 'DeleteModelPackageGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteModelPackageGroupInput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], ], ], 'DeleteModelPackageGroupPolicy' => [ 'name' => 'DeleteModelPackageGroupPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteModelPackageGroupPolicyInput', ], ], 'DeleteModelQualityJobDefinition' => [ 'name' => 'DeleteModelQualityJobDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteModelQualityJobDefinitionRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DeleteMonitoringSchedule' => [ 'name' => 'DeleteMonitoringSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteMonitoringScheduleRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DeleteNotebookInstance' => [ 'name' => 'DeleteNotebookInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteNotebookInstanceInput', ], ], 'DeleteNotebookInstanceLifecycleConfig' => [ 'name' => 'DeleteNotebookInstanceLifecycleConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteNotebookInstanceLifecycleConfigInput', ], ], 'DeletePipeline' => [ 'name' => 'DeletePipeline', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePipelineRequest', ], 'output' => [ 'shape' => 'DeletePipelineResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DeleteProject' => [ 'name' => 'DeleteProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteProjectInput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], ], ], 'DeleteTags' => [ 'name' => 'DeleteTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTagsInput', ], 'output' => [ 'shape' => 'DeleteTagsOutput', ], ], 'DeleteTrial' => [ 'name' => 'DeleteTrial', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTrialRequest', ], 'output' => [ 'shape' => 'DeleteTrialResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DeleteTrialComponent' => [ 'name' => 'DeleteTrialComponent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTrialComponentRequest', ], 'output' => [ 'shape' => 'DeleteTrialComponentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DeleteUserProfile' => [ 'name' => 'DeleteUserProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteUserProfileRequest', ], 'errors' => [ [ 'shape' => 'ResourceInUse', ], [ 'shape' => 'ResourceNotFound', ], ], ], 'DeleteWorkforce' => [ 'name' => 'DeleteWorkforce', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteWorkforceRequest', ], 'output' => [ 'shape' => 'DeleteWorkforceResponse', ], ], 'DeleteWorkteam' => [ 'name' => 'DeleteWorkteam', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteWorkteamRequest', ], 'output' => [ 'shape' => 'DeleteWorkteamResponse', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'DeregisterDevices' => [ 'name' => 'DeregisterDevices', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeregisterDevicesRequest', ], ], 'DescribeAction' => [ 'name' => 'DescribeAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeActionRequest', ], 'output' => [ 'shape' => 'DescribeActionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeAlgorithm' => [ 'name' => 'DescribeAlgorithm', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAlgorithmInput', ], 'output' => [ 'shape' => 'DescribeAlgorithmOutput', ], ], 'DescribeApp' => [ 'name' => 'DescribeApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAppRequest', ], 'output' => [ 'shape' => 'DescribeAppResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeAppImageConfig' => [ 'name' => 'DescribeAppImageConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAppImageConfigRequest', ], 'output' => [ 'shape' => 'DescribeAppImageConfigResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeArtifact' => [ 'name' => 'DescribeArtifact', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeArtifactRequest', ], 'output' => [ 'shape' => 'DescribeArtifactResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeAutoMLJob' => [ 'name' => 'DescribeAutoMLJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAutoMLJobRequest', ], 'output' => [ 'shape' => 'DescribeAutoMLJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeCodeRepository' => [ 'name' => 'DescribeCodeRepository', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCodeRepositoryInput', ], 'output' => [ 'shape' => 'DescribeCodeRepositoryOutput', ], ], 'DescribeCompilationJob' => [ 'name' => 'DescribeCompilationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCompilationJobRequest', ], 'output' => [ 'shape' => 'DescribeCompilationJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeContext' => [ 'name' => 'DescribeContext', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeContextRequest', ], 'output' => [ 'shape' => 'DescribeContextResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeDataQualityJobDefinition' => [ 'name' => 'DescribeDataQualityJobDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDataQualityJobDefinitionRequest', ], 'output' => [ 'shape' => 'DescribeDataQualityJobDefinitionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeDevice' => [ 'name' => 'DescribeDevice', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDeviceRequest', ], 'output' => [ 'shape' => 'DescribeDeviceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeDeviceFleet' => [ 'name' => 'DescribeDeviceFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDeviceFleetRequest', ], 'output' => [ 'shape' => 'DescribeDeviceFleetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeDomain' => [ 'name' => 'DescribeDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDomainRequest', ], 'output' => [ 'shape' => 'DescribeDomainResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeEdgePackagingJob' => [ 'name' => 'DescribeEdgePackagingJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEdgePackagingJobRequest', ], 'output' => [ 'shape' => 'DescribeEdgePackagingJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeEndpoint' => [ 'name' => 'DescribeEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEndpointInput', ], 'output' => [ 'shape' => 'DescribeEndpointOutput', ], ], 'DescribeEndpointConfig' => [ 'name' => 'DescribeEndpointConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEndpointConfigInput', ], 'output' => [ 'shape' => 'DescribeEndpointConfigOutput', ], ], 'DescribeExperiment' => [ 'name' => 'DescribeExperiment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeExperimentRequest', ], 'output' => [ 'shape' => 'DescribeExperimentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeFeatureGroup' => [ 'name' => 'DescribeFeatureGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFeatureGroupRequest', ], 'output' => [ 'shape' => 'DescribeFeatureGroupResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeFlowDefinition' => [ 'name' => 'DescribeFlowDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFlowDefinitionRequest', ], 'output' => [ 'shape' => 'DescribeFlowDefinitionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeHumanTaskUi' => [ 'name' => 'DescribeHumanTaskUi', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeHumanTaskUiRequest', ], 'output' => [ 'shape' => 'DescribeHumanTaskUiResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeHyperParameterTuningJob' => [ 'name' => 'DescribeHyperParameterTuningJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeHyperParameterTuningJobRequest', ], 'output' => [ 'shape' => 'DescribeHyperParameterTuningJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeImage' => [ 'name' => 'DescribeImage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeImageRequest', ], 'output' => [ 'shape' => 'DescribeImageResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeImageVersion' => [ 'name' => 'DescribeImageVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeImageVersionRequest', ], 'output' => [ 'shape' => 'DescribeImageVersionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeLabelingJob' => [ 'name' => 'DescribeLabelingJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLabelingJobRequest', ], 'output' => [ 'shape' => 'DescribeLabelingJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeModel' => [ 'name' => 'DescribeModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeModelInput', ], 'output' => [ 'shape' => 'DescribeModelOutput', ], ], 'DescribeModelBiasJobDefinition' => [ 'name' => 'DescribeModelBiasJobDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeModelBiasJobDefinitionRequest', ], 'output' => [ 'shape' => 'DescribeModelBiasJobDefinitionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeModelExplainabilityJobDefinition' => [ 'name' => 'DescribeModelExplainabilityJobDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeModelExplainabilityJobDefinitionRequest', ], 'output' => [ 'shape' => 'DescribeModelExplainabilityJobDefinitionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeModelPackage' => [ 'name' => 'DescribeModelPackage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeModelPackageInput', ], 'output' => [ 'shape' => 'DescribeModelPackageOutput', ], ], 'DescribeModelPackageGroup' => [ 'name' => 'DescribeModelPackageGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeModelPackageGroupInput', ], 'output' => [ 'shape' => 'DescribeModelPackageGroupOutput', ], ], 'DescribeModelQualityJobDefinition' => [ 'name' => 'DescribeModelQualityJobDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeModelQualityJobDefinitionRequest', ], 'output' => [ 'shape' => 'DescribeModelQualityJobDefinitionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeMonitoringSchedule' => [ 'name' => 'DescribeMonitoringSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeMonitoringScheduleRequest', ], 'output' => [ 'shape' => 'DescribeMonitoringScheduleResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeNotebookInstance' => [ 'name' => 'DescribeNotebookInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeNotebookInstanceInput', ], 'output' => [ 'shape' => 'DescribeNotebookInstanceOutput', ], ], 'DescribeNotebookInstanceLifecycleConfig' => [ 'name' => 'DescribeNotebookInstanceLifecycleConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeNotebookInstanceLifecycleConfigInput', ], 'output' => [ 'shape' => 'DescribeNotebookInstanceLifecycleConfigOutput', ], ], 'DescribePipeline' => [ 'name' => 'DescribePipeline', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePipelineRequest', ], 'output' => [ 'shape' => 'DescribePipelineResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribePipelineDefinitionForExecution' => [ 'name' => 'DescribePipelineDefinitionForExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePipelineDefinitionForExecutionRequest', ], 'output' => [ 'shape' => 'DescribePipelineDefinitionForExecutionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribePipelineExecution' => [ 'name' => 'DescribePipelineExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePipelineExecutionRequest', ], 'output' => [ 'shape' => 'DescribePipelineExecutionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeProcessingJob' => [ 'name' => 'DescribeProcessingJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeProcessingJobRequest', ], 'output' => [ 'shape' => 'DescribeProcessingJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeProject' => [ 'name' => 'DescribeProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeProjectInput', ], 'output' => [ 'shape' => 'DescribeProjectOutput', ], ], 'DescribeSubscribedWorkteam' => [ 'name' => 'DescribeSubscribedWorkteam', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSubscribedWorkteamRequest', ], 'output' => [ 'shape' => 'DescribeSubscribedWorkteamResponse', ], ], 'DescribeTrainingJob' => [ 'name' => 'DescribeTrainingJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTrainingJobRequest', ], 'output' => [ 'shape' => 'DescribeTrainingJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeTransformJob' => [ 'name' => 'DescribeTransformJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTransformJobRequest', ], 'output' => [ 'shape' => 'DescribeTransformJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeTrial' => [ 'name' => 'DescribeTrial', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTrialRequest', ], 'output' => [ 'shape' => 'DescribeTrialResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeTrialComponent' => [ 'name' => 'DescribeTrialComponent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTrialComponentRequest', ], 'output' => [ 'shape' => 'DescribeTrialComponentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeUserProfile' => [ 'name' => 'DescribeUserProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeUserProfileRequest', ], 'output' => [ 'shape' => 'DescribeUserProfileResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'DescribeWorkforce' => [ 'name' => 'DescribeWorkforce', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeWorkforceRequest', ], 'output' => [ 'shape' => 'DescribeWorkforceResponse', ], ], 'DescribeWorkteam' => [ 'name' => 'DescribeWorkteam', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeWorkteamRequest', ], 'output' => [ 'shape' => 'DescribeWorkteamResponse', ], ], 'DisableSagemakerServicecatalogPortfolio' => [ 'name' => 'DisableSagemakerServicecatalogPortfolio', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisableSagemakerServicecatalogPortfolioInput', ], 'output' => [ 'shape' => 'DisableSagemakerServicecatalogPortfolioOutput', ], ], 'DisassociateTrialComponent' => [ 'name' => 'DisassociateTrialComponent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateTrialComponentRequest', ], 'output' => [ 'shape' => 'DisassociateTrialComponentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'EnableSagemakerServicecatalogPortfolio' => [ 'name' => 'EnableSagemakerServicecatalogPortfolio', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'EnableSagemakerServicecatalogPortfolioInput', ], 'output' => [ 'shape' => 'EnableSagemakerServicecatalogPortfolioOutput', ], ], 'GetDeviceFleetReport' => [ 'name' => 'GetDeviceFleetReport', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDeviceFleetReportRequest', ], 'output' => [ 'shape' => 'GetDeviceFleetReportResponse', ], ], 'GetModelPackageGroupPolicy' => [ 'name' => 'GetModelPackageGroupPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetModelPackageGroupPolicyInput', ], 'output' => [ 'shape' => 'GetModelPackageGroupPolicyOutput', ], ], 'GetSagemakerServicecatalogPortfolioStatus' => [ 'name' => 'GetSagemakerServicecatalogPortfolioStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSagemakerServicecatalogPortfolioStatusInput', ], 'output' => [ 'shape' => 'GetSagemakerServicecatalogPortfolioStatusOutput', ], ], 'GetSearchSuggestions' => [ 'name' => 'GetSearchSuggestions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSearchSuggestionsRequest', ], 'output' => [ 'shape' => 'GetSearchSuggestionsResponse', ], ], 'ListActions' => [ 'name' => 'ListActions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListActionsRequest', ], 'output' => [ 'shape' => 'ListActionsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'ListAlgorithms' => [ 'name' => 'ListAlgorithms', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAlgorithmsInput', ], 'output' => [ 'shape' => 'ListAlgorithmsOutput', ], ], 'ListAppImageConfigs' => [ 'name' => 'ListAppImageConfigs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAppImageConfigsRequest', ], 'output' => [ 'shape' => 'ListAppImageConfigsResponse', ], ], 'ListApps' => [ 'name' => 'ListApps', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAppsRequest', ], 'output' => [ 'shape' => 'ListAppsResponse', ], ], 'ListArtifacts' => [ 'name' => 'ListArtifacts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListArtifactsRequest', ], 'output' => [ 'shape' => 'ListArtifactsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'ListAssociations' => [ 'name' => 'ListAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAssociationsRequest', ], 'output' => [ 'shape' => 'ListAssociationsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'ListAutoMLJobs' => [ 'name' => 'ListAutoMLJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAutoMLJobsRequest', ], 'output' => [ 'shape' => 'ListAutoMLJobsResponse', ], ], 'ListCandidatesForAutoMLJob' => [ 'name' => 'ListCandidatesForAutoMLJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCandidatesForAutoMLJobRequest', ], 'output' => [ 'shape' => 'ListCandidatesForAutoMLJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'ListCodeRepositories' => [ 'name' => 'ListCodeRepositories', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCodeRepositoriesInput', ], 'output' => [ 'shape' => 'ListCodeRepositoriesOutput', ], ], 'ListCompilationJobs' => [ 'name' => 'ListCompilationJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCompilationJobsRequest', ], 'output' => [ 'shape' => 'ListCompilationJobsResponse', ], ], 'ListContexts' => [ 'name' => 'ListContexts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListContextsRequest', ], 'output' => [ 'shape' => 'ListContextsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'ListDataQualityJobDefinitions' => [ 'name' => 'ListDataQualityJobDefinitions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDataQualityJobDefinitionsRequest', ], 'output' => [ 'shape' => 'ListDataQualityJobDefinitionsResponse', ], ], 'ListDeviceFleets' => [ 'name' => 'ListDeviceFleets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDeviceFleetsRequest', ], 'output' => [ 'shape' => 'ListDeviceFleetsResponse', ], ], 'ListDevices' => [ 'name' => 'ListDevices', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDevicesRequest', ], 'output' => [ 'shape' => 'ListDevicesResponse', ], ], 'ListDomains' => [ 'name' => 'ListDomains', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDomainsRequest', ], 'output' => [ 'shape' => 'ListDomainsResponse', ], ], 'ListEdgePackagingJobs' => [ 'name' => 'ListEdgePackagingJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEdgePackagingJobsRequest', ], 'output' => [ 'shape' => 'ListEdgePackagingJobsResponse', ], ], 'ListEndpointConfigs' => [ 'name' => 'ListEndpointConfigs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEndpointConfigsInput', ], 'output' => [ 'shape' => 'ListEndpointConfigsOutput', ], ], 'ListEndpoints' => [ 'name' => 'ListEndpoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEndpointsInput', ], 'output' => [ 'shape' => 'ListEndpointsOutput', ], ], 'ListExperiments' => [ 'name' => 'ListExperiments', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListExperimentsRequest', ], 'output' => [ 'shape' => 'ListExperimentsResponse', ], ], 'ListFeatureGroups' => [ 'name' => 'ListFeatureGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFeatureGroupsRequest', ], 'output' => [ 'shape' => 'ListFeatureGroupsResponse', ], ], 'ListFlowDefinitions' => [ 'name' => 'ListFlowDefinitions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFlowDefinitionsRequest', ], 'output' => [ 'shape' => 'ListFlowDefinitionsResponse', ], ], 'ListHumanTaskUis' => [ 'name' => 'ListHumanTaskUis', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListHumanTaskUisRequest', ], 'output' => [ 'shape' => 'ListHumanTaskUisResponse', ], ], 'ListHyperParameterTuningJobs' => [ 'name' => 'ListHyperParameterTuningJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListHyperParameterTuningJobsRequest', ], 'output' => [ 'shape' => 'ListHyperParameterTuningJobsResponse', ], ], 'ListImageVersions' => [ 'name' => 'ListImageVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListImageVersionsRequest', ], 'output' => [ 'shape' => 'ListImageVersionsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'ListImages' => [ 'name' => 'ListImages', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListImagesRequest', ], 'output' => [ 'shape' => 'ListImagesResponse', ], ], 'ListLabelingJobs' => [ 'name' => 'ListLabelingJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListLabelingJobsRequest', ], 'output' => [ 'shape' => 'ListLabelingJobsResponse', ], ], 'ListLabelingJobsForWorkteam' => [ 'name' => 'ListLabelingJobsForWorkteam', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListLabelingJobsForWorkteamRequest', ], 'output' => [ 'shape' => 'ListLabelingJobsForWorkteamResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'ListModelBiasJobDefinitions' => [ 'name' => 'ListModelBiasJobDefinitions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListModelBiasJobDefinitionsRequest', ], 'output' => [ 'shape' => 'ListModelBiasJobDefinitionsResponse', ], ], 'ListModelExplainabilityJobDefinitions' => [ 'name' => 'ListModelExplainabilityJobDefinitions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListModelExplainabilityJobDefinitionsRequest', ], 'output' => [ 'shape' => 'ListModelExplainabilityJobDefinitionsResponse', ], ], 'ListModelPackageGroups' => [ 'name' => 'ListModelPackageGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListModelPackageGroupsInput', ], 'output' => [ 'shape' => 'ListModelPackageGroupsOutput', ], ], 'ListModelPackages' => [ 'name' => 'ListModelPackages', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListModelPackagesInput', ], 'output' => [ 'shape' => 'ListModelPackagesOutput', ], ], 'ListModelQualityJobDefinitions' => [ 'name' => 'ListModelQualityJobDefinitions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListModelQualityJobDefinitionsRequest', ], 'output' => [ 'shape' => 'ListModelQualityJobDefinitionsResponse', ], ], 'ListModels' => [ 'name' => 'ListModels', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListModelsInput', ], 'output' => [ 'shape' => 'ListModelsOutput', ], ], 'ListMonitoringExecutions' => [ 'name' => 'ListMonitoringExecutions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListMonitoringExecutionsRequest', ], 'output' => [ 'shape' => 'ListMonitoringExecutionsResponse', ], ], 'ListMonitoringSchedules' => [ 'name' => 'ListMonitoringSchedules', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListMonitoringSchedulesRequest', ], 'output' => [ 'shape' => 'ListMonitoringSchedulesResponse', ], ], 'ListNotebookInstanceLifecycleConfigs' => [ 'name' => 'ListNotebookInstanceLifecycleConfigs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListNotebookInstanceLifecycleConfigsInput', ], 'output' => [ 'shape' => 'ListNotebookInstanceLifecycleConfigsOutput', ], ], 'ListNotebookInstances' => [ 'name' => 'ListNotebookInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListNotebookInstancesInput', ], 'output' => [ 'shape' => 'ListNotebookInstancesOutput', ], ], 'ListPipelineExecutionSteps' => [ 'name' => 'ListPipelineExecutionSteps', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPipelineExecutionStepsRequest', ], 'output' => [ 'shape' => 'ListPipelineExecutionStepsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'ListPipelineExecutions' => [ 'name' => 'ListPipelineExecutions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPipelineExecutionsRequest', ], 'output' => [ 'shape' => 'ListPipelineExecutionsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'ListPipelineParametersForExecution' => [ 'name' => 'ListPipelineParametersForExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPipelineParametersForExecutionRequest', ], 'output' => [ 'shape' => 'ListPipelineParametersForExecutionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'ListPipelines' => [ 'name' => 'ListPipelines', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPipelinesRequest', ], 'output' => [ 'shape' => 'ListPipelinesResponse', ], ], 'ListProcessingJobs' => [ 'name' => 'ListProcessingJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListProcessingJobsRequest', ], 'output' => [ 'shape' => 'ListProcessingJobsResponse', ], ], 'ListProjects' => [ 'name' => 'ListProjects', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListProjectsInput', ], 'output' => [ 'shape' => 'ListProjectsOutput', ], ], 'ListSubscribedWorkteams' => [ 'name' => 'ListSubscribedWorkteams', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSubscribedWorkteamsRequest', ], 'output' => [ 'shape' => 'ListSubscribedWorkteamsResponse', ], ], 'ListTags' => [ 'name' => 'ListTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsInput', ], 'output' => [ 'shape' => 'ListTagsOutput', ], ], 'ListTrainingJobs' => [ 'name' => 'ListTrainingJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTrainingJobsRequest', ], 'output' => [ 'shape' => 'ListTrainingJobsResponse', ], ], 'ListTrainingJobsForHyperParameterTuningJob' => [ 'name' => 'ListTrainingJobsForHyperParameterTuningJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTrainingJobsForHyperParameterTuningJobRequest', ], 'output' => [ 'shape' => 'ListTrainingJobsForHyperParameterTuningJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'ListTransformJobs' => [ 'name' => 'ListTransformJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTransformJobsRequest', ], 'output' => [ 'shape' => 'ListTransformJobsResponse', ], ], 'ListTrialComponents' => [ 'name' => 'ListTrialComponents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTrialComponentsRequest', ], 'output' => [ 'shape' => 'ListTrialComponentsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'ListTrials' => [ 'name' => 'ListTrials', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTrialsRequest', ], 'output' => [ 'shape' => 'ListTrialsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'ListUserProfiles' => [ 'name' => 'ListUserProfiles', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListUserProfilesRequest', ], 'output' => [ 'shape' => 'ListUserProfilesResponse', ], ], 'ListWorkforces' => [ 'name' => 'ListWorkforces', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListWorkforcesRequest', ], 'output' => [ 'shape' => 'ListWorkforcesResponse', ], ], 'ListWorkteams' => [ 'name' => 'ListWorkteams', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListWorkteamsRequest', ], 'output' => [ 'shape' => 'ListWorkteamsResponse', ], ], 'PutModelPackageGroupPolicy' => [ 'name' => 'PutModelPackageGroupPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutModelPackageGroupPolicyInput', ], 'output' => [ 'shape' => 'PutModelPackageGroupPolicyOutput', ], ], 'RegisterDevices' => [ 'name' => 'RegisterDevices', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterDevicesRequest', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'RenderUiTemplate' => [ 'name' => 'RenderUiTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RenderUiTemplateRequest', ], 'output' => [ 'shape' => 'RenderUiTemplateResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'Search' => [ 'name' => 'Search', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SearchRequest', ], 'output' => [ 'shape' => 'SearchResponse', ], ], 'SendPipelineExecutionStepFailure' => [ 'name' => 'SendPipelineExecutionStepFailure', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SendPipelineExecutionStepFailureRequest', ], 'output' => [ 'shape' => 'SendPipelineExecutionStepFailureResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'SendPipelineExecutionStepSuccess' => [ 'name' => 'SendPipelineExecutionStepSuccess', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SendPipelineExecutionStepSuccessRequest', ], 'output' => [ 'shape' => 'SendPipelineExecutionStepSuccessResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'StartMonitoringSchedule' => [ 'name' => 'StartMonitoringSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartMonitoringScheduleRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'StartNotebookInstance' => [ 'name' => 'StartNotebookInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartNotebookInstanceInput', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'StartPipelineExecution' => [ 'name' => 'StartPipelineExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartPipelineExecutionRequest', ], 'output' => [ 'shape' => 'StartPipelineExecutionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'StopAutoMLJob' => [ 'name' => 'StopAutoMLJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopAutoMLJobRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'StopCompilationJob' => [ 'name' => 'StopCompilationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopCompilationJobRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'StopEdgePackagingJob' => [ 'name' => 'StopEdgePackagingJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopEdgePackagingJobRequest', ], ], 'StopHyperParameterTuningJob' => [ 'name' => 'StopHyperParameterTuningJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopHyperParameterTuningJobRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'StopLabelingJob' => [ 'name' => 'StopLabelingJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopLabelingJobRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'StopMonitoringSchedule' => [ 'name' => 'StopMonitoringSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopMonitoringScheduleRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'StopNotebookInstance' => [ 'name' => 'StopNotebookInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopNotebookInstanceInput', ], ], 'StopPipelineExecution' => [ 'name' => 'StopPipelineExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopPipelineExecutionRequest', ], 'output' => [ 'shape' => 'StopPipelineExecutionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'StopProcessingJob' => [ 'name' => 'StopProcessingJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopProcessingJobRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'StopTrainingJob' => [ 'name' => 'StopTrainingJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopTrainingJobRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'StopTransformJob' => [ 'name' => 'StopTransformJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopTransformJobRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'UpdateAction' => [ 'name' => 'UpdateAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateActionRequest', ], 'output' => [ 'shape' => 'UpdateActionResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFound', ], ], ], 'UpdateAppImageConfig' => [ 'name' => 'UpdateAppImageConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateAppImageConfigRequest', ], 'output' => [ 'shape' => 'UpdateAppImageConfigResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'UpdateArtifact' => [ 'name' => 'UpdateArtifact', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateArtifactRequest', ], 'output' => [ 'shape' => 'UpdateArtifactResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFound', ], ], ], 'UpdateCodeRepository' => [ 'name' => 'UpdateCodeRepository', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateCodeRepositoryInput', ], 'output' => [ 'shape' => 'UpdateCodeRepositoryOutput', ], ], 'UpdateContext' => [ 'name' => 'UpdateContext', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateContextRequest', ], 'output' => [ 'shape' => 'UpdateContextResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFound', ], ], ], 'UpdateDeviceFleet' => [ 'name' => 'UpdateDeviceFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDeviceFleetRequest', ], 'errors' => [ [ 'shape' => 'ResourceInUse', ], ], ], 'UpdateDevices' => [ 'name' => 'UpdateDevices', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDevicesRequest', ], ], 'UpdateDomain' => [ 'name' => 'UpdateDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDomainRequest', ], 'output' => [ 'shape' => 'UpdateDomainResponse', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], [ 'shape' => 'ResourceInUse', ], [ 'shape' => 'ResourceNotFound', ], ], ], 'UpdateEndpoint' => [ 'name' => 'UpdateEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateEndpointInput', ], 'output' => [ 'shape' => 'UpdateEndpointOutput', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'UpdateEndpointWeightsAndCapacities' => [ 'name' => 'UpdateEndpointWeightsAndCapacities', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateEndpointWeightsAndCapacitiesInput', ], 'output' => [ 'shape' => 'UpdateEndpointWeightsAndCapacitiesOutput', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'UpdateExperiment' => [ 'name' => 'UpdateExperiment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateExperimentRequest', ], 'output' => [ 'shape' => 'UpdateExperimentResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFound', ], ], ], 'UpdateImage' => [ 'name' => 'UpdateImage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateImageRequest', ], 'output' => [ 'shape' => 'UpdateImageResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUse', ], [ 'shape' => 'ResourceNotFound', ], ], ], 'UpdateModelPackage' => [ 'name' => 'UpdateModelPackage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateModelPackageInput', ], 'output' => [ 'shape' => 'UpdateModelPackageOutput', ], ], 'UpdateMonitoringSchedule' => [ 'name' => 'UpdateMonitoringSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateMonitoringScheduleRequest', ], 'output' => [ 'shape' => 'UpdateMonitoringScheduleResponse', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], [ 'shape' => 'ResourceNotFound', ], ], ], 'UpdateNotebookInstance' => [ 'name' => 'UpdateNotebookInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateNotebookInstanceInput', ], 'output' => [ 'shape' => 'UpdateNotebookInstanceOutput', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'UpdateNotebookInstanceLifecycleConfig' => [ 'name' => 'UpdateNotebookInstanceLifecycleConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateNotebookInstanceLifecycleConfigInput', ], 'output' => [ 'shape' => 'UpdateNotebookInstanceLifecycleConfigOutput', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], ], ], 'UpdatePipeline' => [ 'name' => 'UpdatePipeline', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePipelineRequest', ], 'output' => [ 'shape' => 'UpdatePipelineResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'UpdatePipelineExecution' => [ 'name' => 'UpdatePipelineExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePipelineExecutionRequest', ], 'output' => [ 'shape' => 'UpdatePipelineExecutionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'UpdateTrainingJob' => [ 'name' => 'UpdateTrainingJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateTrainingJobRequest', ], 'output' => [ 'shape' => 'UpdateTrainingJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFound', ], ], ], 'UpdateTrial' => [ 'name' => 'UpdateTrial', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateTrialRequest', ], 'output' => [ 'shape' => 'UpdateTrialResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFound', ], ], ], 'UpdateTrialComponent' => [ 'name' => 'UpdateTrialComponent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateTrialComponentRequest', ], 'output' => [ 'shape' => 'UpdateTrialComponentResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFound', ], ], ], 'UpdateUserProfile' => [ 'name' => 'UpdateUserProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateUserProfileRequest', ], 'output' => [ 'shape' => 'UpdateUserProfileResponse', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], [ 'shape' => 'ResourceInUse', ], [ 'shape' => 'ResourceNotFound', ], ], ], 'UpdateWorkforce' => [ 'name' => 'UpdateWorkforce', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateWorkforceRequest', ], 'output' => [ 'shape' => 'UpdateWorkforceResponse', ], ], 'UpdateWorkteam' => [ 'name' => 'UpdateWorkteam', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateWorkteamRequest', ], 'output' => [ 'shape' => 'UpdateWorkteamResponse', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceeded', ], ], ], ], 'shapes' => [ 'Accept' => [ 'type' => 'string', 'max' => 256, 'pattern' => '.*', ], 'AccountId' => [ 'type' => 'string', 'pattern' => '^\\d+$', ], 'ActionArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:action/.*', ], 'ActionSource' => [ 'type' => 'structure', 'required' => [ 'SourceUri', ], 'members' => [ 'SourceUri' => [ 'shape' => 'String2048', ], 'SourceType' => [ 'shape' => 'String256', ], 'SourceId' => [ 'shape' => 'String256', ], ], ], 'ActionStatus' => [ 'type' => 'string', 'enum' => [ 'Unknown', 'InProgress', 'Completed', 'Failed', 'Stopping', 'Stopped', ], ], 'ActionSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ActionSummary', ], ], 'ActionSummary' => [ 'type' => 'structure', 'members' => [ 'ActionArn' => [ 'shape' => 'ActionArn', ], 'ActionName' => [ 'shape' => 'ExperimentEntityName', ], 'Source' => [ 'shape' => 'ActionSource', ], 'ActionType' => [ 'shape' => 'String64', ], 'Status' => [ 'shape' => 'ActionStatus', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'AddAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'SourceArn', 'DestinationArn', ], 'members' => [ 'SourceArn' => [ 'shape' => 'AssociationEntityArn', ], 'DestinationArn' => [ 'shape' => 'AssociationEntityArn', ], 'AssociationType' => [ 'shape' => 'AssociationEdgeType', ], ], ], 'AddAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'SourceArn' => [ 'shape' => 'AssociationEntityArn', ], 'DestinationArn' => [ 'shape' => 'AssociationEntityArn', ], ], ], 'AddTagsInput' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'AddTagsOutput' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'AdditionalCodeRepositoryNamesOrUrls' => [ 'type' => 'list', 'member' => [ 'shape' => 'CodeRepositoryNameOrUrl', ], 'max' => 3, ], 'AgentVersion' => [ 'type' => 'structure', 'required' => [ 'Version', 'AgentCount', ], 'members' => [ 'Version' => [ 'shape' => 'EdgeVersion', ], 'AgentCount' => [ 'shape' => 'Long', ], ], ], 'AgentVersions' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentVersion', ], ], 'Alarm' => [ 'type' => 'structure', 'members' => [ 'AlarmName' => [ 'shape' => 'AlarmName', ], ], ], 'AlarmList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Alarm', ], 'max' => 10, 'min' => 1, ], 'AlarmName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(?!\\s*$).+', ], 'AlgorithmArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:algorithm/.*', ], 'AlgorithmImage' => [ 'type' => 'string', 'max' => 255, 'pattern' => '.*', ], 'AlgorithmSortBy' => [ 'type' => 'string', 'enum' => [ 'Name', 'CreationTime', ], ], 'AlgorithmSpecification' => [ 'type' => 'structure', 'required' => [ 'TrainingInputMode', ], 'members' => [ 'TrainingImage' => [ 'shape' => 'AlgorithmImage', ], 'AlgorithmName' => [ 'shape' => 'ArnOrName', ], 'TrainingInputMode' => [ 'shape' => 'TrainingInputMode', ], 'MetricDefinitions' => [ 'shape' => 'MetricDefinitionList', ], 'EnableSageMakerMetricsTimeSeries' => [ 'shape' => 'Boolean', ], ], ], 'AlgorithmStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'InProgress', 'Completed', 'Failed', 'Deleting', ], ], 'AlgorithmStatusDetails' => [ 'type' => 'structure', 'members' => [ 'ValidationStatuses' => [ 'shape' => 'AlgorithmStatusItemList', ], 'ImageScanStatuses' => [ 'shape' => 'AlgorithmStatusItemList', ], ], ], 'AlgorithmStatusItem' => [ 'type' => 'structure', 'required' => [ 'Name', 'Status', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Status' => [ 'shape' => 'DetailedAlgorithmStatus', ], 'FailureReason' => [ 'shape' => 'String', ], ], ], 'AlgorithmStatusItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AlgorithmStatusItem', ], ], 'AlgorithmSummary' => [ 'type' => 'structure', 'required' => [ 'AlgorithmName', 'AlgorithmArn', 'CreationTime', 'AlgorithmStatus', ], 'members' => [ 'AlgorithmName' => [ 'shape' => 'EntityName', ], 'AlgorithmArn' => [ 'shape' => 'AlgorithmArn', ], 'AlgorithmDescription' => [ 'shape' => 'EntityDescription', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'AlgorithmStatus' => [ 'shape' => 'AlgorithmStatus', ], ], ], 'AlgorithmSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AlgorithmSummary', ], ], 'AlgorithmValidationProfile' => [ 'type' => 'structure', 'required' => [ 'ProfileName', 'TrainingJobDefinition', ], 'members' => [ 'ProfileName' => [ 'shape' => 'EntityName', ], 'TrainingJobDefinition' => [ 'shape' => 'TrainingJobDefinition', ], 'TransformJobDefinition' => [ 'shape' => 'TransformJobDefinition', ], ], ], 'AlgorithmValidationProfiles' => [ 'type' => 'list', 'member' => [ 'shape' => 'AlgorithmValidationProfile', ], 'max' => 1, 'min' => 1, ], 'AlgorithmValidationSpecification' => [ 'type' => 'structure', 'required' => [ 'ValidationRole', 'ValidationProfiles', ], 'members' => [ 'ValidationRole' => [ 'shape' => 'RoleArn', ], 'ValidationProfiles' => [ 'shape' => 'AlgorithmValidationProfiles', ], ], ], 'AnnotationConsolidationConfig' => [ 'type' => 'structure', 'required' => [ 'AnnotationConsolidationLambdaArn', ], 'members' => [ 'AnnotationConsolidationLambdaArn' => [ 'shape' => 'LambdaFunctionArn', ], ], ], 'AppArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:app/.*', ], 'AppDetails' => [ 'type' => 'structure', 'members' => [ 'DomainId' => [ 'shape' => 'DomainId', ], 'UserProfileName' => [ 'shape' => 'UserProfileName', ], 'AppType' => [ 'shape' => 'AppType', ], 'AppName' => [ 'shape' => 'AppName', ], 'Status' => [ 'shape' => 'AppStatus', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], ], ], 'AppImageConfigArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:app-image-config/.*', ], 'AppImageConfigDetails' => [ 'type' => 'structure', 'members' => [ 'AppImageConfigArn' => [ 'shape' => 'AppImageConfigArn', ], 'AppImageConfigName' => [ 'shape' => 'AppImageConfigName', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'KernelGatewayImageConfig' => [ 'shape' => 'KernelGatewayImageConfig', ], ], ], 'AppImageConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppImageConfigDetails', ], ], 'AppImageConfigName' => [ 'type' => 'string', 'max' => 63, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,62}', ], 'AppImageConfigSortKey' => [ 'type' => 'string', 'enum' => [ 'CreationTime', 'LastModifiedTime', 'Name', ], ], 'AppInstanceType' => [ 'type' => 'string', 'enum' => [ 'system', 'ml.t3.micro', 'ml.t3.small', 'ml.t3.medium', 'ml.t3.large', 'ml.t3.xlarge', 'ml.t3.2xlarge', 'ml.m5.large', 'ml.m5.xlarge', 'ml.m5.2xlarge', 'ml.m5.4xlarge', 'ml.m5.8xlarge', 'ml.m5.12xlarge', 'ml.m5.16xlarge', 'ml.m5.24xlarge', 'ml.c5.large', 'ml.c5.xlarge', 'ml.c5.2xlarge', 'ml.c5.4xlarge', 'ml.c5.9xlarge', 'ml.c5.12xlarge', 'ml.c5.18xlarge', 'ml.c5.24xlarge', 'ml.p3.2xlarge', 'ml.p3.8xlarge', 'ml.p3.16xlarge', 'ml.g4dn.xlarge', 'ml.g4dn.2xlarge', 'ml.g4dn.4xlarge', 'ml.g4dn.8xlarge', 'ml.g4dn.12xlarge', 'ml.g4dn.16xlarge', ], ], 'AppList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppDetails', ], ], 'AppManaged' => [ 'type' => 'boolean', ], 'AppName' => [ 'type' => 'string', 'max' => 63, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,62}', ], 'AppNetworkAccessType' => [ 'type' => 'string', 'enum' => [ 'PublicInternetOnly', 'VpcOnly', ], ], 'AppSortKey' => [ 'type' => 'string', 'enum' => [ 'CreationTime', ], ], 'AppSpecification' => [ 'type' => 'structure', 'required' => [ 'ImageUri', ], 'members' => [ 'ImageUri' => [ 'shape' => 'ImageUri', ], 'ContainerEntrypoint' => [ 'shape' => 'ContainerEntrypoint', ], 'ContainerArguments' => [ 'shape' => 'ContainerArguments', ], ], ], 'AppStatus' => [ 'type' => 'string', 'enum' => [ 'Deleted', 'Deleting', 'Failed', 'InService', 'Pending', ], ], 'AppType' => [ 'type' => 'string', 'enum' => [ 'JupyterServer', 'KernelGateway', 'TensorBoard', ], ], 'ApprovalDescription' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '.*', ], 'ArnOrName' => [ 'type' => 'string', 'max' => 170, 'min' => 1, 'pattern' => '(arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:[a-z\\-]*\\/)?([a-zA-Z0-9]([a-zA-Z0-9-]){0,62})(?<!-)$', ], 'ArtifactArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:artifact/.*', ], 'ArtifactDigest' => [ 'type' => 'string', 'pattern' => '^[\\w:]+$', ], 'ArtifactSource' => [ 'type' => 'structure', 'required' => [ 'SourceUri', ], 'members' => [ 'SourceUri' => [ 'shape' => 'String2048', ], 'SourceTypes' => [ 'shape' => 'ArtifactSourceTypes', ], ], ], 'ArtifactSourceIdType' => [ 'type' => 'string', 'enum' => [ 'MD5Hash', 'S3ETag', 'S3Version', 'Custom', ], ], 'ArtifactSourceType' => [ 'type' => 'structure', 'required' => [ 'SourceIdType', 'Value', ], 'members' => [ 'SourceIdType' => [ 'shape' => 'ArtifactSourceIdType', ], 'Value' => [ 'shape' => 'String256', ], ], ], 'ArtifactSourceTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ArtifactSourceType', ], ], 'ArtifactSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ArtifactSummary', ], ], 'ArtifactSummary' => [ 'type' => 'structure', 'members' => [ 'ArtifactArn' => [ 'shape' => 'ArtifactArn', ], 'ArtifactName' => [ 'shape' => 'ExperimentEntityName', ], 'Source' => [ 'shape' => 'ArtifactSource', ], 'ArtifactType' => [ 'shape' => 'String256', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'AssemblyType' => [ 'type' => 'string', 'enum' => [ 'None', 'Line', ], ], 'AssociateTrialComponentRequest' => [ 'type' => 'structure', 'required' => [ 'TrialComponentName', 'TrialName', ], 'members' => [ 'TrialComponentName' => [ 'shape' => 'ExperimentEntityName', ], 'TrialName' => [ 'shape' => 'ExperimentEntityName', ], ], ], 'AssociateTrialComponentResponse' => [ 'type' => 'structure', 'members' => [ 'TrialComponentArn' => [ 'shape' => 'TrialComponentArn', ], 'TrialArn' => [ 'shape' => 'TrialArn', ], ], ], 'AssociationEdgeType' => [ 'type' => 'string', 'enum' => [ 'ContributedTo', 'AssociatedWith', 'DerivedFrom', 'Produced', ], ], 'AssociationEntityArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:(experiment|experiment-trial-component|artifact|action|context)/.*', ], 'AssociationSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociationSummary', ], ], 'AssociationSummary' => [ 'type' => 'structure', 'members' => [ 'SourceArn' => [ 'shape' => 'AssociationEntityArn', ], 'DestinationArn' => [ 'shape' => 'AssociationEntityArn', ], 'SourceType' => [ 'shape' => 'String256', ], 'DestinationType' => [ 'shape' => 'String256', ], 'AssociationType' => [ 'shape' => 'AssociationEdgeType', ], 'SourceName' => [ 'shape' => 'ExperimentEntityName', ], 'DestinationName' => [ 'shape' => 'ExperimentEntityName', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'CreatedBy' => [ 'shape' => 'UserContext', ], ], ], 'AthenaCatalog' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'AthenaDatabase' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '.*', ], 'AthenaDatasetDefinition' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'Database', 'QueryString', 'OutputS3Uri', 'OutputFormat', ], 'members' => [ 'Catalog' => [ 'shape' => 'AthenaCatalog', ], 'Database' => [ 'shape' => 'AthenaDatabase', ], 'QueryString' => [ 'shape' => 'AthenaQueryString', ], 'WorkGroup' => [ 'shape' => 'AthenaWorkGroup', ], 'OutputS3Uri' => [ 'shape' => 'S3Uri', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'OutputFormat' => [ 'shape' => 'AthenaResultFormat', ], 'OutputCompression' => [ 'shape' => 'AthenaResultCompressionType', ], ], ], 'AthenaQueryString' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '[\\s\\S]+', ], 'AthenaResultCompressionType' => [ 'type' => 'string', 'enum' => [ 'GZIP', 'SNAPPY', 'ZLIB', ], ], 'AthenaResultFormat' => [ 'type' => 'string', 'enum' => [ 'PARQUET', 'ORC', 'AVRO', 'JSON', 'TEXTFILE', ], ], 'AthenaWorkGroup' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9._-]+', ], 'AttributeName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '.+', ], 'AttributeNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeName', ], 'max' => 16, ], 'AuthMode' => [ 'type' => 'string', 'enum' => [ 'SSO', 'IAM', ], ], 'AutoGenerateEndpointName' => [ 'type' => 'boolean', ], 'AutoMLCandidate' => [ 'type' => 'structure', 'required' => [ 'CandidateName', 'ObjectiveStatus', 'CandidateSteps', 'CandidateStatus', 'CreationTime', 'LastModifiedTime', ], 'members' => [ 'CandidateName' => [ 'shape' => 'CandidateName', ], 'FinalAutoMLJobObjectiveMetric' => [ 'shape' => 'FinalAutoMLJobObjectiveMetric', ], 'ObjectiveStatus' => [ 'shape' => 'ObjectiveStatus', ], 'CandidateSteps' => [ 'shape' => 'CandidateSteps', ], 'CandidateStatus' => [ 'shape' => 'CandidateStatus', ], 'InferenceContainers' => [ 'shape' => 'AutoMLContainerDefinitions', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'FailureReason' => [ 'shape' => 'AutoMLFailureReason', ], 'CandidateProperties' => [ 'shape' => 'CandidateProperties', ], ], ], 'AutoMLCandidateStep' => [ 'type' => 'structure', 'required' => [ 'CandidateStepType', 'CandidateStepArn', 'CandidateStepName', ], 'members' => [ 'CandidateStepType' => [ 'shape' => 'CandidateStepType', ], 'CandidateStepArn' => [ 'shape' => 'CandidateStepArn', ], 'CandidateStepName' => [ 'shape' => 'CandidateStepName', ], ], ], 'AutoMLCandidates' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoMLCandidate', ], ], 'AutoMLChannel' => [ 'type' => 'structure', 'required' => [ 'DataSource', 'TargetAttributeName', ], 'members' => [ 'DataSource' => [ 'shape' => 'AutoMLDataSource', ], 'CompressionType' => [ 'shape' => 'CompressionType', ], 'TargetAttributeName' => [ 'shape' => 'TargetAttributeName', ], ], ], 'AutoMLContainerDefinition' => [ 'type' => 'structure', 'required' => [ 'Image', 'ModelDataUrl', ], 'members' => [ 'Image' => [ 'shape' => 'ContainerImage', ], 'ModelDataUrl' => [ 'shape' => 'Url', ], 'Environment' => [ 'shape' => 'EnvironmentMap', ], ], ], 'AutoMLContainerDefinitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoMLContainerDefinition', ], 'max' => 5, ], 'AutoMLDataSource' => [ 'type' => 'structure', 'required' => [ 'S3DataSource', ], 'members' => [ 'S3DataSource' => [ 'shape' => 'AutoMLS3DataSource', ], ], ], 'AutoMLFailureReason' => [ 'type' => 'string', 'max' => 1024, ], 'AutoMLInputDataConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoMLChannel', ], 'max' => 20, 'min' => 1, ], 'AutoMLJobArn' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:automl-job/.*', ], 'AutoMLJobArtifacts' => [ 'type' => 'structure', 'members' => [ 'CandidateDefinitionNotebookLocation' => [ 'shape' => 'CandidateDefinitionNotebookLocation', ], 'DataExplorationNotebookLocation' => [ 'shape' => 'DataExplorationNotebookLocation', ], ], ], 'AutoMLJobCompletionCriteria' => [ 'type' => 'structure', 'members' => [ 'MaxCandidates' => [ 'shape' => 'MaxCandidates', ], 'MaxRuntimePerTrainingJobInSeconds' => [ 'shape' => 'MaxRuntimePerTrainingJobInSeconds', ], 'MaxAutoMLJobRuntimeInSeconds' => [ 'shape' => 'MaxAutoMLJobRuntimeInSeconds', ], ], ], 'AutoMLJobConfig' => [ 'type' => 'structure', 'members' => [ 'CompletionCriteria' => [ 'shape' => 'AutoMLJobCompletionCriteria', ], 'SecurityConfig' => [ 'shape' => 'AutoMLSecurityConfig', ], ], ], 'AutoMLJobName' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,31}', ], 'AutoMLJobObjective' => [ 'type' => 'structure', 'required' => [ 'MetricName', ], 'members' => [ 'MetricName' => [ 'shape' => 'AutoMLMetricEnum', ], ], ], 'AutoMLJobObjectiveType' => [ 'type' => 'string', 'enum' => [ 'Maximize', 'Minimize', ], ], 'AutoMLJobSecondaryStatus' => [ 'type' => 'string', 'enum' => [ 'Starting', 'AnalyzingData', 'FeatureEngineering', 'ModelTuning', 'MaxCandidatesReached', 'Failed', 'Stopped', 'MaxAutoMLJobRuntimeReached', 'Stopping', 'CandidateDefinitionsGenerated', 'GeneratingExplainabilityReport', 'Completed', 'ExplainabilityError', 'DeployingModel', 'ModelDeploymentError', ], ], 'AutoMLJobStatus' => [ 'type' => 'string', 'enum' => [ 'Completed', 'InProgress', 'Failed', 'Stopped', 'Stopping', ], ], 'AutoMLJobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoMLJobSummary', ], ], 'AutoMLJobSummary' => [ 'type' => 'structure', 'required' => [ 'AutoMLJobName', 'AutoMLJobArn', 'AutoMLJobStatus', 'AutoMLJobSecondaryStatus', 'CreationTime', 'LastModifiedTime', ], 'members' => [ 'AutoMLJobName' => [ 'shape' => 'AutoMLJobName', ], 'AutoMLJobArn' => [ 'shape' => 'AutoMLJobArn', ], 'AutoMLJobStatus' => [ 'shape' => 'AutoMLJobStatus', ], 'AutoMLJobSecondaryStatus' => [ 'shape' => 'AutoMLJobSecondaryStatus', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'FailureReason' => [ 'shape' => 'AutoMLFailureReason', ], 'PartialFailureReasons' => [ 'shape' => 'AutoMLPartialFailureReasons', ], ], ], 'AutoMLMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'AutoMLMetricEnum' => [ 'type' => 'string', 'enum' => [ 'Accuracy', 'MSE', 'F1', 'F1macro', 'AUC', ], ], 'AutoMLNameContains' => [ 'type' => 'string', 'max' => 63, 'pattern' => '[a-zA-Z0-9\\-]+', ], 'AutoMLOutputDataConfig' => [ 'type' => 'structure', 'required' => [ 'S3OutputPath', ], 'members' => [ 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'S3OutputPath' => [ 'shape' => 'S3Uri', ], ], ], 'AutoMLPartialFailureReason' => [ 'type' => 'structure', 'members' => [ 'PartialFailureMessage' => [ 'shape' => 'AutoMLFailureReason', ], ], ], 'AutoMLPartialFailureReasons' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoMLPartialFailureReason', ], 'max' => 5, 'min' => 1, ], 'AutoMLS3DataSource' => [ 'type' => 'structure', 'required' => [ 'S3DataType', 'S3Uri', ], 'members' => [ 'S3DataType' => [ 'shape' => 'AutoMLS3DataType', ], 'S3Uri' => [ 'shape' => 'S3Uri', ], ], ], 'AutoMLS3DataType' => [ 'type' => 'string', 'enum' => [ 'ManifestFile', 'S3Prefix', ], ], 'AutoMLSecurityConfig' => [ 'type' => 'structure', 'members' => [ 'VolumeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'EnableInterContainerTrafficEncryption' => [ 'shape' => 'Boolean', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], ], ], 'AutoMLSortBy' => [ 'type' => 'string', 'enum' => [ 'Name', 'CreationTime', 'Status', ], ], 'AutoMLSortOrder' => [ 'type' => 'string', 'enum' => [ 'Ascending', 'Descending', ], ], 'AutoRollbackConfig' => [ 'type' => 'structure', 'members' => [ 'Alarms' => [ 'shape' => 'AlarmList', ], ], ], 'AwsManagedHumanLoopRequestSource' => [ 'type' => 'string', 'enum' => [ 'AWS/Rekognition/DetectModerationLabels/Image/V3', 'AWS/Textract/AnalyzeDocument/Forms/V1', ], ], 'BatchStrategy' => [ 'type' => 'string', 'enum' => [ 'MultiRecord', 'SingleRecord', ], ], 'Bias' => [ 'type' => 'structure', 'members' => [ 'Report' => [ 'shape' => 'MetricsSource', ], ], ], 'BillableTimeInSeconds' => [ 'type' => 'integer', 'min' => 1, ], 'BlockedReason' => [ 'type' => 'string', 'max' => 1024, ], 'BlueGreenUpdatePolicy' => [ 'type' => 'structure', 'required' => [ 'TrafficRoutingConfiguration', ], 'members' => [ 'TrafficRoutingConfiguration' => [ 'shape' => 'TrafficRoutingConfig', ], 'TerminationWaitInSeconds' => [ 'shape' => 'TerminationWaitInSeconds', ], 'MaximumExecutionTimeoutInSeconds' => [ 'shape' => 'MaximumExecutionTimeoutInSeconds', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'BooleanOperator' => [ 'type' => 'string', 'enum' => [ 'And', 'Or', ], ], 'Branch' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[^ ~^:?*\\[]+', ], 'CacheHitResult' => [ 'type' => 'structure', 'members' => [ 'SourcePipelineExecutionArn' => [ 'shape' => 'PipelineExecutionArn', ], ], ], 'CallbackStepMetadata' => [ 'type' => 'structure', 'members' => [ 'CallbackToken' => [ 'shape' => 'CallbackToken', ], 'SqsQueueUrl' => [ 'shape' => 'String256', ], 'OutputParameters' => [ 'shape' => 'OutputParameterList', ], ], ], 'CallbackToken' => [ 'type' => 'string', 'max' => 10, 'min' => 10, 'pattern' => '^[a-zA-Z0-9]+$', ], 'CandidateArtifactLocations' => [ 'type' => 'structure', 'required' => [ 'Explainability', ], 'members' => [ 'Explainability' => [ 'shape' => 'ExplainabilityLocation', ], ], ], 'CandidateDefinitionNotebookLocation' => [ 'type' => 'string', 'min' => 1, ], 'CandidateName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'CandidateProperties' => [ 'type' => 'structure', 'members' => [ 'CandidateArtifactLocations' => [ 'shape' => 'CandidateArtifactLocations', ], ], ], 'CandidateSortBy' => [ 'type' => 'string', 'enum' => [ 'CreationTime', 'Status', 'FinalObjectiveMetricValue', ], ], 'CandidateStatus' => [ 'type' => 'string', 'enum' => [ 'Completed', 'InProgress', 'Failed', 'Stopped', 'Stopping', ], ], 'CandidateStepArn' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:.*/.*', ], 'CandidateStepName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'CandidateStepType' => [ 'type' => 'string', 'enum' => [ 'AWS::SageMaker::TrainingJob', 'AWS::SageMaker::TransformJob', 'AWS::SageMaker::ProcessingJob', ], ], 'CandidateSteps' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoMLCandidateStep', ], ], 'CapacitySize' => [ 'type' => 'structure', 'required' => [ 'Type', 'Value', ], 'members' => [ 'Type' => [ 'shape' => 'CapacitySizeType', ], 'Value' => [ 'shape' => 'CapacitySizeValue', ], ], ], 'CapacitySizeType' => [ 'type' => 'string', 'enum' => [ 'INSTANCE_COUNT', 'CAPACITY_PERCENT', ], ], 'CapacitySizeValue' => [ 'type' => 'integer', 'min' => 1, ], 'CaptureContentTypeHeader' => [ 'type' => 'structure', 'members' => [ 'CsvContentTypes' => [ 'shape' => 'CsvContentTypes', ], 'JsonContentTypes' => [ 'shape' => 'JsonContentTypes', ], ], ], 'CaptureMode' => [ 'type' => 'string', 'enum' => [ 'Input', 'Output', ], ], 'CaptureOption' => [ 'type' => 'structure', 'required' => [ 'CaptureMode', ], 'members' => [ 'CaptureMode' => [ 'shape' => 'CaptureMode', ], ], ], 'CaptureOptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CaptureOption', ], 'max' => 2, 'min' => 1, ], 'CaptureStatus' => [ 'type' => 'string', 'enum' => [ 'Started', 'Stopped', ], ], 'Catalog' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'CategoricalParameterRange' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'ParameterKey', ], 'Values' => [ 'shape' => 'ParameterValues', ], ], ], 'CategoricalParameterRangeSpecification' => [ 'type' => 'structure', 'required' => [ 'Values', ], 'members' => [ 'Values' => [ 'shape' => 'ParameterValues', ], ], ], 'CategoricalParameterRanges' => [ 'type' => 'list', 'member' => [ 'shape' => 'CategoricalParameterRange', ], 'max' => 20, 'min' => 0, ], 'Cents' => [ 'type' => 'integer', 'max' => 99, 'min' => 0, ], 'CertifyForMarketplace' => [ 'type' => 'boolean', ], 'Channel' => [ 'type' => 'structure', 'required' => [ 'ChannelName', 'DataSource', ], 'members' => [ 'ChannelName' => [ 'shape' => 'ChannelName', ], 'DataSource' => [ 'shape' => 'DataSource', ], 'ContentType' => [ 'shape' => 'ContentType', ], 'CompressionType' => [ 'shape' => 'CompressionType', ], 'RecordWrapperType' => [ 'shape' => 'RecordWrapper', ], 'InputMode' => [ 'shape' => 'TrainingInputMode', ], 'ShuffleConfig' => [ 'shape' => 'ShuffleConfig', ], ], ], 'ChannelName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[A-Za-z0-9\\.\\-_]+', ], 'ChannelSpecification' => [ 'type' => 'structure', 'required' => [ 'Name', 'SupportedContentTypes', 'SupportedInputModes', ], 'members' => [ 'Name' => [ 'shape' => 'ChannelName', ], 'Description' => [ 'shape' => 'EntityDescription', ], 'IsRequired' => [ 'shape' => 'Boolean', ], 'SupportedContentTypes' => [ 'shape' => 'ContentTypes', ], 'SupportedCompressionTypes' => [ 'shape' => 'CompressionTypes', ], 'SupportedInputModes' => [ 'shape' => 'InputModes', ], ], ], 'ChannelSpecifications' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelSpecification', ], 'max' => 8, 'min' => 1, ], 'CheckpointConfig' => [ 'type' => 'structure', 'required' => [ 'S3Uri', ], 'members' => [ 'S3Uri' => [ 'shape' => 'S3Uri', ], 'LocalPath' => [ 'shape' => 'DirectoryPath', ], ], ], 'Cidr' => [ 'type' => 'string', 'max' => 64, 'min' => 4, 'pattern' => '(^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\\/(3[0-2]|[1-2][0-9]|[0-9]))$)|(^s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]d|1dd|[1-9]?d)(.(25[0-5]|2[0-4]d|1dd|[1-9]?d)){3}))|:)))(%.+)?s*(\\/(12[0-8]|1[0-1][0-9]|[1-9][0-9]|[0-9]))$)', ], 'Cidrs' => [ 'type' => 'list', 'member' => [ 'shape' => 'Cidr', ], ], 'ClientId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[ -~]+', ], 'ClientSecret' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[ -~]+', 'sensitive' => true, ], 'ClientToken' => [ 'type' => 'string', 'max' => 36, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-]+$', ], 'CodeRepositoryArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:code-repository/.*', ], 'CodeRepositoryContains' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '[a-zA-Z0-9-]+', ], 'CodeRepositoryNameContains' => [ 'type' => 'string', 'max' => 63, 'pattern' => '[a-zA-Z0-9-]+', ], 'CodeRepositoryNameOrUrl' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^https://([^/]+)/?(.*)$|^[a-zA-Z0-9](-*[a-zA-Z0-9])*', ], 'CodeRepositorySortBy' => [ 'type' => 'string', 'enum' => [ 'Name', 'CreationTime', 'LastModifiedTime', ], ], 'CodeRepositorySortOrder' => [ 'type' => 'string', 'enum' => [ 'Ascending', 'Descending', ], ], 'CodeRepositorySummary' => [ 'type' => 'structure', 'required' => [ 'CodeRepositoryName', 'CodeRepositoryArn', 'CreationTime', 'LastModifiedTime', ], 'members' => [ 'CodeRepositoryName' => [ 'shape' => 'EntityName', ], 'CodeRepositoryArn' => [ 'shape' => 'CodeRepositoryArn', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'LastModifiedTime' => [ 'shape' => 'LastModifiedTime', ], 'GitConfig' => [ 'shape' => 'GitConfig', ], ], ], 'CodeRepositorySummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CodeRepositorySummary', ], ], 'CognitoConfig' => [ 'type' => 'structure', 'required' => [ 'UserPool', 'ClientId', ], 'members' => [ 'UserPool' => [ 'shape' => 'CognitoUserPool', ], 'ClientId' => [ 'shape' => 'ClientId', ], ], ], 'CognitoMemberDefinition' => [ 'type' => 'structure', 'required' => [ 'UserPool', 'UserGroup', 'ClientId', ], 'members' => [ 'UserPool' => [ 'shape' => 'CognitoUserPool', ], 'UserGroup' => [ 'shape' => 'CognitoUserGroup', ], 'ClientId' => [ 'shape' => 'ClientId', ], ], ], 'CognitoUserGroup' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}]+', ], 'CognitoUserPool' => [ 'type' => 'string', 'max' => 55, 'min' => 1, 'pattern' => '[\\w-]+_[0-9a-zA-Z]+', ], 'CollectionConfiguration' => [ 'type' => 'structure', 'members' => [ 'CollectionName' => [ 'shape' => 'CollectionName', ], 'CollectionParameters' => [ 'shape' => 'CollectionParameters', ], ], ], 'CollectionConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'CollectionConfiguration', ], 'max' => 20, 'min' => 0, ], 'CollectionName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '.*', ], 'CollectionParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'ConfigKey', ], 'value' => [ 'shape' => 'ConfigValue', ], 'max' => 20, 'min' => 0, ], 'CompilationJobArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:compilation-job/.*', ], 'CompilationJobStatus' => [ 'type' => 'string', 'enum' => [ 'INPROGRESS', 'COMPLETED', 'FAILED', 'STARTING', 'STOPPING', 'STOPPED', ], ], 'CompilationJobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'CompilationJobSummary', ], ], 'CompilationJobSummary' => [ 'type' => 'structure', 'required' => [ 'CompilationJobName', 'CompilationJobArn', 'CreationTime', 'CompilationJobStatus', ], 'members' => [ 'CompilationJobName' => [ 'shape' => 'EntityName', ], 'CompilationJobArn' => [ 'shape' => 'CompilationJobArn', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'CompilationStartTime' => [ 'shape' => 'Timestamp', ], 'CompilationEndTime' => [ 'shape' => 'Timestamp', ], 'CompilationTargetDevice' => [ 'shape' => 'TargetDevice', ], 'CompilationTargetPlatformOs' => [ 'shape' => 'TargetPlatformOs', ], 'CompilationTargetPlatformArch' => [ 'shape' => 'TargetPlatformArch', ], 'CompilationTargetPlatformAccelerator' => [ 'shape' => 'TargetPlatformAccelerator', ], 'LastModifiedTime' => [ 'shape' => 'LastModifiedTime', ], 'CompilationJobStatus' => [ 'shape' => 'CompilationJobStatus', ], ], ], 'CompilerOptions' => [ 'type' => 'string', 'max' => 1024, 'min' => 3, 'pattern' => '.*', ], 'CompressionType' => [ 'type' => 'string', 'enum' => [ 'None', 'Gzip', ], ], 'CompressionTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'CompressionType', ], ], 'ConditionOutcome' => [ 'type' => 'string', 'enum' => [ 'True', 'False', ], ], 'ConditionStepMetadata' => [ 'type' => 'structure', 'members' => [ 'Outcome' => [ 'shape' => 'ConditionOutcome', ], ], ], 'ConfigKey' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '.*', ], 'ConfigValue' => [ 'type' => 'string', 'max' => 256, 'pattern' => '.*', ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'FailureReason', ], ], 'exception' => true, ], 'ContainerArgument' => [ 'type' => 'string', 'max' => 256, 'pattern' => '.*', ], 'ContainerArguments' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerArgument', ], 'max' => 100, 'min' => 1, ], 'ContainerDefinition' => [ 'type' => 'structure', 'members' => [ 'ContainerHostname' => [ 'shape' => 'ContainerHostname', ], 'Image' => [ 'shape' => 'ContainerImage', ], 'ImageConfig' => [ 'shape' => 'ImageConfig', ], 'Mode' => [ 'shape' => 'ContainerMode', ], 'ModelDataUrl' => [ 'shape' => 'Url', ], 'Environment' => [ 'shape' => 'EnvironmentMap', ], 'ModelPackageName' => [ 'shape' => 'VersionedArnOrName', ], 'MultiModelConfig' => [ 'shape' => 'MultiModelConfig', ], ], ], 'ContainerDefinitionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerDefinition', ], 'max' => 5, ], 'ContainerEntrypoint' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerEntrypointString', ], 'max' => 100, 'min' => 1, ], 'ContainerEntrypointString' => [ 'type' => 'string', 'max' => 256, 'pattern' => '.*', ], 'ContainerHostname' => [ 'type' => 'string', 'max' => 63, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,62}', ], 'ContainerImage' => [ 'type' => 'string', 'max' => 255, 'pattern' => '[\\S]+', ], 'ContainerMode' => [ 'type' => 'string', 'enum' => [ 'SingleModel', 'MultiModel', ], ], 'ContentClassifier' => [ 'type' => 'string', 'enum' => [ 'FreeOfPersonallyIdentifiableInformation', 'FreeOfAdultContent', ], ], 'ContentClassifiers' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContentClassifier', ], 'max' => 256, ], 'ContentDigest' => [ 'type' => 'string', 'max' => 72, 'pattern' => '^[Ss][Hh][Aa]256:[0-9a-fA-F]{64}$', ], 'ContentType' => [ 'type' => 'string', 'max' => 256, 'pattern' => '.*', ], 'ContentTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContentType', ], ], 'ContextArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:context/.*', ], 'ContextSource' => [ 'type' => 'structure', 'required' => [ 'SourceUri', ], 'members' => [ 'SourceUri' => [ 'shape' => 'String2048', ], 'SourceType' => [ 'shape' => 'String256', ], 'SourceId' => [ 'shape' => 'String256', ], ], ], 'ContextSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContextSummary', ], ], 'ContextSummary' => [ 'type' => 'structure', 'members' => [ 'ContextArn' => [ 'shape' => 'ContextArn', ], 'ContextName' => [ 'shape' => 'ExperimentEntityName', ], 'Source' => [ 'shape' => 'ContextSource', ], 'ContextType' => [ 'shape' => 'String256', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'ContinuousParameterRange' => [ 'type' => 'structure', 'required' => [ 'Name', 'MinValue', 'MaxValue', ], 'members' => [ 'Name' => [ 'shape' => 'ParameterKey', ], 'MinValue' => [ 'shape' => 'ParameterValue', ], 'MaxValue' => [ 'shape' => 'ParameterValue', ], 'ScalingType' => [ 'shape' => 'HyperParameterScalingType', ], ], ], 'ContinuousParameterRangeSpecification' => [ 'type' => 'structure', 'required' => [ 'MinValue', 'MaxValue', ], 'members' => [ 'MinValue' => [ 'shape' => 'ParameterValue', ], 'MaxValue' => [ 'shape' => 'ParameterValue', ], ], ], 'ContinuousParameterRanges' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContinuousParameterRange', ], 'max' => 20, 'min' => 0, ], 'CreateActionRequest' => [ 'type' => 'structure', 'required' => [ 'ActionName', 'Source', 'ActionType', ], 'members' => [ 'ActionName' => [ 'shape' => 'ExperimentEntityName', ], 'Source' => [ 'shape' => 'ActionSource', ], 'ActionType' => [ 'shape' => 'String256', ], 'Description' => [ 'shape' => 'ExperimentDescription', ], 'Status' => [ 'shape' => 'ActionStatus', ], 'Properties' => [ 'shape' => 'LineageEntityParameters', ], 'MetadataProperties' => [ 'shape' => 'MetadataProperties', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateActionResponse' => [ 'type' => 'structure', 'members' => [ 'ActionArn' => [ 'shape' => 'ActionArn', ], ], ], 'CreateAlgorithmInput' => [ 'type' => 'structure', 'required' => [ 'AlgorithmName', 'TrainingSpecification', ], 'members' => [ 'AlgorithmName' => [ 'shape' => 'EntityName', ], 'AlgorithmDescription' => [ 'shape' => 'EntityDescription', ], 'TrainingSpecification' => [ 'shape' => 'TrainingSpecification', ], 'InferenceSpecification' => [ 'shape' => 'InferenceSpecification', ], 'ValidationSpecification' => [ 'shape' => 'AlgorithmValidationSpecification', ], 'CertifyForMarketplace' => [ 'shape' => 'CertifyForMarketplace', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateAlgorithmOutput' => [ 'type' => 'structure', 'required' => [ 'AlgorithmArn', ], 'members' => [ 'AlgorithmArn' => [ 'shape' => 'AlgorithmArn', ], ], ], 'CreateAppImageConfigRequest' => [ 'type' => 'structure', 'required' => [ 'AppImageConfigName', ], 'members' => [ 'AppImageConfigName' => [ 'shape' => 'AppImageConfigName', ], 'Tags' => [ 'shape' => 'TagList', ], 'KernelGatewayImageConfig' => [ 'shape' => 'KernelGatewayImageConfig', ], ], ], 'CreateAppImageConfigResponse' => [ 'type' => 'structure', 'members' => [ 'AppImageConfigArn' => [ 'shape' => 'AppImageConfigArn', ], ], ], 'CreateAppRequest' => [ 'type' => 'structure', 'required' => [ 'DomainId', 'UserProfileName', 'AppType', 'AppName', ], 'members' => [ 'DomainId' => [ 'shape' => 'DomainId', ], 'UserProfileName' => [ 'shape' => 'UserProfileName', ], 'AppType' => [ 'shape' => 'AppType', ], 'AppName' => [ 'shape' => 'AppName', ], 'Tags' => [ 'shape' => 'TagList', ], 'ResourceSpec' => [ 'shape' => 'ResourceSpec', ], ], ], 'CreateAppResponse' => [ 'type' => 'structure', 'members' => [ 'AppArn' => [ 'shape' => 'AppArn', ], ], ], 'CreateArtifactRequest' => [ 'type' => 'structure', 'required' => [ 'Source', 'ArtifactType', ], 'members' => [ 'ArtifactName' => [ 'shape' => 'ExperimentEntityName', ], 'Source' => [ 'shape' => 'ArtifactSource', ], 'ArtifactType' => [ 'shape' => 'String256', ], 'Properties' => [ 'shape' => 'LineageEntityParameters', ], 'MetadataProperties' => [ 'shape' => 'MetadataProperties', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateArtifactResponse' => [ 'type' => 'structure', 'members' => [ 'ArtifactArn' => [ 'shape' => 'ArtifactArn', ], ], ], 'CreateAutoMLJobRequest' => [ 'type' => 'structure', 'required' => [ 'AutoMLJobName', 'InputDataConfig', 'OutputDataConfig', 'RoleArn', ], 'members' => [ 'AutoMLJobName' => [ 'shape' => 'AutoMLJobName', ], 'InputDataConfig' => [ 'shape' => 'AutoMLInputDataConfig', ], 'OutputDataConfig' => [ 'shape' => 'AutoMLOutputDataConfig', ], 'ProblemType' => [ 'shape' => 'ProblemType', ], 'AutoMLJobObjective' => [ 'shape' => 'AutoMLJobObjective', ], 'AutoMLJobConfig' => [ 'shape' => 'AutoMLJobConfig', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'GenerateCandidateDefinitionsOnly' => [ 'shape' => 'GenerateCandidateDefinitionsOnly', ], 'Tags' => [ 'shape' => 'TagList', ], 'ModelDeployConfig' => [ 'shape' => 'ModelDeployConfig', ], ], ], 'CreateAutoMLJobResponse' => [ 'type' => 'structure', 'required' => [ 'AutoMLJobArn', ], 'members' => [ 'AutoMLJobArn' => [ 'shape' => 'AutoMLJobArn', ], ], ], 'CreateCodeRepositoryInput' => [ 'type' => 'structure', 'required' => [ 'CodeRepositoryName', 'GitConfig', ], 'members' => [ 'CodeRepositoryName' => [ 'shape' => 'EntityName', ], 'GitConfig' => [ 'shape' => 'GitConfig', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateCodeRepositoryOutput' => [ 'type' => 'structure', 'required' => [ 'CodeRepositoryArn', ], 'members' => [ 'CodeRepositoryArn' => [ 'shape' => 'CodeRepositoryArn', ], ], ], 'CreateCompilationJobRequest' => [ 'type' => 'structure', 'required' => [ 'CompilationJobName', 'RoleArn', 'InputConfig', 'OutputConfig', 'StoppingCondition', ], 'members' => [ 'CompilationJobName' => [ 'shape' => 'EntityName', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'InputConfig' => [ 'shape' => 'InputConfig', ], 'OutputConfig' => [ 'shape' => 'OutputConfig', ], 'VpcConfig' => [ 'shape' => 'NeoVpcConfig', ], 'StoppingCondition' => [ 'shape' => 'StoppingCondition', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateCompilationJobResponse' => [ 'type' => 'structure', 'required' => [ 'CompilationJobArn', ], 'members' => [ 'CompilationJobArn' => [ 'shape' => 'CompilationJobArn', ], ], ], 'CreateContextRequest' => [ 'type' => 'structure', 'required' => [ 'ContextName', 'Source', 'ContextType', ], 'members' => [ 'ContextName' => [ 'shape' => 'ExperimentEntityName', ], 'Source' => [ 'shape' => 'ContextSource', ], 'ContextType' => [ 'shape' => 'String256', ], 'Description' => [ 'shape' => 'ExperimentDescription', ], 'Properties' => [ 'shape' => 'LineageEntityParameters', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateContextResponse' => [ 'type' => 'structure', 'members' => [ 'ContextArn' => [ 'shape' => 'ContextArn', ], ], ], 'CreateDataQualityJobDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'JobDefinitionName', 'DataQualityAppSpecification', 'DataQualityJobInput', 'DataQualityJobOutputConfig', 'JobResources', 'RoleArn', ], 'members' => [ 'JobDefinitionName' => [ 'shape' => 'MonitoringJobDefinitionName', ], 'DataQualityBaselineConfig' => [ 'shape' => 'DataQualityBaselineConfig', ], 'DataQualityAppSpecification' => [ 'shape' => 'DataQualityAppSpecification', ], 'DataQualityJobInput' => [ 'shape' => 'DataQualityJobInput', ], 'DataQualityJobOutputConfig' => [ 'shape' => 'MonitoringOutputConfig', ], 'JobResources' => [ 'shape' => 'MonitoringResources', ], 'NetworkConfig' => [ 'shape' => 'MonitoringNetworkConfig', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'StoppingCondition' => [ 'shape' => 'MonitoringStoppingCondition', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDataQualityJobDefinitionResponse' => [ 'type' => 'structure', 'required' => [ 'JobDefinitionArn', ], 'members' => [ 'JobDefinitionArn' => [ 'shape' => 'MonitoringJobDefinitionArn', ], ], ], 'CreateDeviceFleetRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceFleetName', 'OutputConfig', ], 'members' => [ 'DeviceFleetName' => [ 'shape' => 'EntityName', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Description' => [ 'shape' => 'DeviceFleetDescription', ], 'OutputConfig' => [ 'shape' => 'EdgeOutputConfig', ], 'Tags' => [ 'shape' => 'TagList', ], 'EnableIotRoleAlias' => [ 'shape' => 'EnableIotRoleAlias', ], ], ], 'CreateDomainRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'AuthMode', 'DefaultUserSettings', 'SubnetIds', 'VpcId', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', ], 'AuthMode' => [ 'shape' => 'AuthMode', ], 'DefaultUserSettings' => [ 'shape' => 'UserSettings', ], 'SubnetIds' => [ 'shape' => 'Subnets', ], 'VpcId' => [ 'shape' => 'VpcId', ], 'Tags' => [ 'shape' => 'TagList', ], 'AppNetworkAccessType' => [ 'shape' => 'AppNetworkAccessType', ], 'HomeEfsFileSystemKmsKeyId' => [ 'shape' => 'KmsKeyId', 'deprecated' => true, 'deprecatedMessage' => 'This property is deprecated, use KmsKeyId instead.', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'CreateDomainResponse' => [ 'type' => 'structure', 'members' => [ 'DomainArn' => [ 'shape' => 'DomainArn', ], 'Url' => [ 'shape' => 'String1024', ], ], ], 'CreateEdgePackagingJobRequest' => [ 'type' => 'structure', 'required' => [ 'EdgePackagingJobName', 'CompilationJobName', 'ModelName', 'ModelVersion', 'RoleArn', 'OutputConfig', ], 'members' => [ 'EdgePackagingJobName' => [ 'shape' => 'EntityName', ], 'CompilationJobName' => [ 'shape' => 'EntityName', ], 'ModelName' => [ 'shape' => 'EntityName', ], 'ModelVersion' => [ 'shape' => 'EdgeVersion', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'OutputConfig' => [ 'shape' => 'EdgeOutputConfig', ], 'ResourceKey' => [ 'shape' => 'KmsKeyId', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateEndpointConfigInput' => [ 'type' => 'structure', 'required' => [ 'EndpointConfigName', 'ProductionVariants', ], 'members' => [ 'EndpointConfigName' => [ 'shape' => 'EndpointConfigName', ], 'ProductionVariants' => [ 'shape' => 'ProductionVariantList', ], 'DataCaptureConfig' => [ 'shape' => 'DataCaptureConfig', ], 'Tags' => [ 'shape' => 'TagList', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'CreateEndpointConfigOutput' => [ 'type' => 'structure', 'required' => [ 'EndpointConfigArn', ], 'members' => [ 'EndpointConfigArn' => [ 'shape' => 'EndpointConfigArn', ], ], ], 'CreateEndpointInput' => [ 'type' => 'structure', 'required' => [ 'EndpointName', 'EndpointConfigName', ], 'members' => [ 'EndpointName' => [ 'shape' => 'EndpointName', ], 'EndpointConfigName' => [ 'shape' => 'EndpointConfigName', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateEndpointOutput' => [ 'type' => 'structure', 'required' => [ 'EndpointArn', ], 'members' => [ 'EndpointArn' => [ 'shape' => 'EndpointArn', ], ], ], 'CreateExperimentRequest' => [ 'type' => 'structure', 'required' => [ 'ExperimentName', ], 'members' => [ 'ExperimentName' => [ 'shape' => 'ExperimentEntityName', ], 'DisplayName' => [ 'shape' => 'ExperimentEntityName', ], 'Description' => [ 'shape' => 'ExperimentDescription', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateExperimentResponse' => [ 'type' => 'structure', 'members' => [ 'ExperimentArn' => [ 'shape' => 'ExperimentArn', ], ], ], 'CreateFeatureGroupRequest' => [ 'type' => 'structure', 'required' => [ 'FeatureGroupName', 'RecordIdentifierFeatureName', 'EventTimeFeatureName', 'FeatureDefinitions', ], 'members' => [ 'FeatureGroupName' => [ 'shape' => 'FeatureGroupName', ], 'RecordIdentifierFeatureName' => [ 'shape' => 'FeatureName', ], 'EventTimeFeatureName' => [ 'shape' => 'FeatureName', ], 'FeatureDefinitions' => [ 'shape' => 'FeatureDefinitions', ], 'OnlineStoreConfig' => [ 'shape' => 'OnlineStoreConfig', ], 'OfflineStoreConfig' => [ 'shape' => 'OfflineStoreConfig', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Description' => [ 'shape' => 'Description', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateFeatureGroupResponse' => [ 'type' => 'structure', 'required' => [ 'FeatureGroupArn', ], 'members' => [ 'FeatureGroupArn' => [ 'shape' => 'FeatureGroupArn', ], ], ], 'CreateFlowDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'FlowDefinitionName', 'HumanLoopConfig', 'OutputConfig', 'RoleArn', ], 'members' => [ 'FlowDefinitionName' => [ 'shape' => 'FlowDefinitionName', ], 'HumanLoopRequestSource' => [ 'shape' => 'HumanLoopRequestSource', ], 'HumanLoopActivationConfig' => [ 'shape' => 'HumanLoopActivationConfig', ], 'HumanLoopConfig' => [ 'shape' => 'HumanLoopConfig', ], 'OutputConfig' => [ 'shape' => 'FlowDefinitionOutputConfig', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateFlowDefinitionResponse' => [ 'type' => 'structure', 'required' => [ 'FlowDefinitionArn', ], 'members' => [ 'FlowDefinitionArn' => [ 'shape' => 'FlowDefinitionArn', ], ], ], 'CreateHumanTaskUiRequest' => [ 'type' => 'structure', 'required' => [ 'HumanTaskUiName', 'UiTemplate', ], 'members' => [ 'HumanTaskUiName' => [ 'shape' => 'HumanTaskUiName', ], 'UiTemplate' => [ 'shape' => 'UiTemplate', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateHumanTaskUiResponse' => [ 'type' => 'structure', 'required' => [ 'HumanTaskUiArn', ], 'members' => [ 'HumanTaskUiArn' => [ 'shape' => 'HumanTaskUiArn', ], ], ], 'CreateHyperParameterTuningJobRequest' => [ 'type' => 'structure', 'required' => [ 'HyperParameterTuningJobName', 'HyperParameterTuningJobConfig', ], 'members' => [ 'HyperParameterTuningJobName' => [ 'shape' => 'HyperParameterTuningJobName', ], 'HyperParameterTuningJobConfig' => [ 'shape' => 'HyperParameterTuningJobConfig', ], 'TrainingJobDefinition' => [ 'shape' => 'HyperParameterTrainingJobDefinition', ], 'TrainingJobDefinitions' => [ 'shape' => 'HyperParameterTrainingJobDefinitions', ], 'WarmStartConfig' => [ 'shape' => 'HyperParameterTuningJobWarmStartConfig', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateHyperParameterTuningJobResponse' => [ 'type' => 'structure', 'required' => [ 'HyperParameterTuningJobArn', ], 'members' => [ 'HyperParameterTuningJobArn' => [ 'shape' => 'HyperParameterTuningJobArn', ], ], ], 'CreateImageRequest' => [ 'type' => 'structure', 'required' => [ 'ImageName', 'RoleArn', ], 'members' => [ 'Description' => [ 'shape' => 'ImageDescription', ], 'DisplayName' => [ 'shape' => 'ImageDisplayName', ], 'ImageName' => [ 'shape' => 'ImageName', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateImageResponse' => [ 'type' => 'structure', 'members' => [ 'ImageArn' => [ 'shape' => 'ImageArn', ], ], ], 'CreateImageVersionRequest' => [ 'type' => 'structure', 'required' => [ 'BaseImage', 'ClientToken', 'ImageName', ], 'members' => [ 'BaseImage' => [ 'shape' => 'ImageBaseImage', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'ImageName' => [ 'shape' => 'ImageName', ], ], ], 'CreateImageVersionResponse' => [ 'type' => 'structure', 'members' => [ 'ImageVersionArn' => [ 'shape' => 'ImageVersionArn', ], ], ], 'CreateLabelingJobRequest' => [ 'type' => 'structure', 'required' => [ 'LabelingJobName', 'LabelAttributeName', 'InputConfig', 'OutputConfig', 'RoleArn', 'HumanTaskConfig', ], 'members' => [ 'LabelingJobName' => [ 'shape' => 'LabelingJobName', ], 'LabelAttributeName' => [ 'shape' => 'LabelAttributeName', ], 'InputConfig' => [ 'shape' => 'LabelingJobInputConfig', ], 'OutputConfig' => [ 'shape' => 'LabelingJobOutputConfig', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'LabelCategoryConfigS3Uri' => [ 'shape' => 'S3Uri', ], 'StoppingConditions' => [ 'shape' => 'LabelingJobStoppingConditions', ], 'LabelingJobAlgorithmsConfig' => [ 'shape' => 'LabelingJobAlgorithmsConfig', ], 'HumanTaskConfig' => [ 'shape' => 'HumanTaskConfig', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateLabelingJobResponse' => [ 'type' => 'structure', 'required' => [ 'LabelingJobArn', ], 'members' => [ 'LabelingJobArn' => [ 'shape' => 'LabelingJobArn', ], ], ], 'CreateModelBiasJobDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'JobDefinitionName', 'ModelBiasAppSpecification', 'ModelBiasJobInput', 'ModelBiasJobOutputConfig', 'JobResources', 'RoleArn', ], 'members' => [ 'JobDefinitionName' => [ 'shape' => 'MonitoringJobDefinitionName', ], 'ModelBiasBaselineConfig' => [ 'shape' => 'ModelBiasBaselineConfig', ], 'ModelBiasAppSpecification' => [ 'shape' => 'ModelBiasAppSpecification', ], 'ModelBiasJobInput' => [ 'shape' => 'ModelBiasJobInput', ], 'ModelBiasJobOutputConfig' => [ 'shape' => 'MonitoringOutputConfig', ], 'JobResources' => [ 'shape' => 'MonitoringResources', ], 'NetworkConfig' => [ 'shape' => 'MonitoringNetworkConfig', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'StoppingCondition' => [ 'shape' => 'MonitoringStoppingCondition', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateModelBiasJobDefinitionResponse' => [ 'type' => 'structure', 'required' => [ 'JobDefinitionArn', ], 'members' => [ 'JobDefinitionArn' => [ 'shape' => 'MonitoringJobDefinitionArn', ], ], ], 'CreateModelExplainabilityJobDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'JobDefinitionName', 'ModelExplainabilityAppSpecification', 'ModelExplainabilityJobInput', 'ModelExplainabilityJobOutputConfig', 'JobResources', 'RoleArn', ], 'members' => [ 'JobDefinitionName' => [ 'shape' => 'MonitoringJobDefinitionName', ], 'ModelExplainabilityBaselineConfig' => [ 'shape' => 'ModelExplainabilityBaselineConfig', ], 'ModelExplainabilityAppSpecification' => [ 'shape' => 'ModelExplainabilityAppSpecification', ], 'ModelExplainabilityJobInput' => [ 'shape' => 'ModelExplainabilityJobInput', ], 'ModelExplainabilityJobOutputConfig' => [ 'shape' => 'MonitoringOutputConfig', ], 'JobResources' => [ 'shape' => 'MonitoringResources', ], 'NetworkConfig' => [ 'shape' => 'MonitoringNetworkConfig', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'StoppingCondition' => [ 'shape' => 'MonitoringStoppingCondition', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateModelExplainabilityJobDefinitionResponse' => [ 'type' => 'structure', 'required' => [ 'JobDefinitionArn', ], 'members' => [ 'JobDefinitionArn' => [ 'shape' => 'MonitoringJobDefinitionArn', ], ], ], 'CreateModelInput' => [ 'type' => 'structure', 'required' => [ 'ModelName', 'ExecutionRoleArn', ], 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'PrimaryContainer' => [ 'shape' => 'ContainerDefinition', ], 'Containers' => [ 'shape' => 'ContainerDefinitionList', ], 'InferenceExecutionConfig' => [ 'shape' => 'InferenceExecutionConfig', ], 'ExecutionRoleArn' => [ 'shape' => 'RoleArn', ], 'Tags' => [ 'shape' => 'TagList', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'EnableNetworkIsolation' => [ 'shape' => 'Boolean', ], ], ], 'CreateModelOutput' => [ 'type' => 'structure', 'required' => [ 'ModelArn', ], 'members' => [ 'ModelArn' => [ 'shape' => 'ModelArn', ], ], ], 'CreateModelPackageGroupInput' => [ 'type' => 'structure', 'required' => [ 'ModelPackageGroupName', ], 'members' => [ 'ModelPackageGroupName' => [ 'shape' => 'EntityName', ], 'ModelPackageGroupDescription' => [ 'shape' => 'EntityDescription', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateModelPackageGroupOutput' => [ 'type' => 'structure', 'required' => [ 'ModelPackageGroupArn', ], 'members' => [ 'ModelPackageGroupArn' => [ 'shape' => 'ModelPackageGroupArn', ], ], ], 'CreateModelPackageInput' => [ 'type' => 'structure', 'members' => [ 'ModelPackageName' => [ 'shape' => 'EntityName', ], 'ModelPackageGroupName' => [ 'shape' => 'EntityName', ], 'ModelPackageDescription' => [ 'shape' => 'EntityDescription', ], 'InferenceSpecification' => [ 'shape' => 'InferenceSpecification', ], 'ValidationSpecification' => [ 'shape' => 'ModelPackageValidationSpecification', ], 'SourceAlgorithmSpecification' => [ 'shape' => 'SourceAlgorithmSpecification', ], 'CertifyForMarketplace' => [ 'shape' => 'CertifyForMarketplace', ], 'Tags' => [ 'shape' => 'TagList', ], 'ModelApprovalStatus' => [ 'shape' => 'ModelApprovalStatus', ], 'MetadataProperties' => [ 'shape' => 'MetadataProperties', ], 'ModelMetrics' => [ 'shape' => 'ModelMetrics', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateModelPackageOutput' => [ 'type' => 'structure', 'required' => [ 'ModelPackageArn', ], 'members' => [ 'ModelPackageArn' => [ 'shape' => 'ModelPackageArn', ], ], ], 'CreateModelQualityJobDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'JobDefinitionName', 'ModelQualityAppSpecification', 'ModelQualityJobInput', 'ModelQualityJobOutputConfig', 'JobResources', 'RoleArn', ], 'members' => [ 'JobDefinitionName' => [ 'shape' => 'MonitoringJobDefinitionName', ], 'ModelQualityBaselineConfig' => [ 'shape' => 'ModelQualityBaselineConfig', ], 'ModelQualityAppSpecification' => [ 'shape' => 'ModelQualityAppSpecification', ], 'ModelQualityJobInput' => [ 'shape' => 'ModelQualityJobInput', ], 'ModelQualityJobOutputConfig' => [ 'shape' => 'MonitoringOutputConfig', ], 'JobResources' => [ 'shape' => 'MonitoringResources', ], 'NetworkConfig' => [ 'shape' => 'MonitoringNetworkConfig', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'StoppingCondition' => [ 'shape' => 'MonitoringStoppingCondition', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateModelQualityJobDefinitionResponse' => [ 'type' => 'structure', 'required' => [ 'JobDefinitionArn', ], 'members' => [ 'JobDefinitionArn' => [ 'shape' => 'MonitoringJobDefinitionArn', ], ], ], 'CreateMonitoringScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'MonitoringScheduleName', 'MonitoringScheduleConfig', ], 'members' => [ 'MonitoringScheduleName' => [ 'shape' => 'MonitoringScheduleName', ], 'MonitoringScheduleConfig' => [ 'shape' => 'MonitoringScheduleConfig', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateMonitoringScheduleResponse' => [ 'type' => 'structure', 'required' => [ 'MonitoringScheduleArn', ], 'members' => [ 'MonitoringScheduleArn' => [ 'shape' => 'MonitoringScheduleArn', ], ], ], 'CreateNotebookInstanceInput' => [ 'type' => 'structure', 'required' => [ 'NotebookInstanceName', 'InstanceType', 'RoleArn', ], 'members' => [ 'NotebookInstanceName' => [ 'shape' => 'NotebookInstanceName', ], 'InstanceType' => [ 'shape' => 'InstanceType', ], 'SubnetId' => [ 'shape' => 'SubnetId', ], 'SecurityGroupIds' => [ 'shape' => 'SecurityGroupIds', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'Tags' => [ 'shape' => 'TagList', ], 'LifecycleConfigName' => [ 'shape' => 'NotebookInstanceLifecycleConfigName', ], 'DirectInternetAccess' => [ 'shape' => 'DirectInternetAccess', ], 'VolumeSizeInGB' => [ 'shape' => 'NotebookInstanceVolumeSizeInGB', ], 'AcceleratorTypes' => [ 'shape' => 'NotebookInstanceAcceleratorTypes', ], 'DefaultCodeRepository' => [ 'shape' => 'CodeRepositoryNameOrUrl', ], 'AdditionalCodeRepositories' => [ 'shape' => 'AdditionalCodeRepositoryNamesOrUrls', ], 'RootAccess' => [ 'shape' => 'RootAccess', ], ], ], 'CreateNotebookInstanceLifecycleConfigInput' => [ 'type' => 'structure', 'required' => [ 'NotebookInstanceLifecycleConfigName', ], 'members' => [ 'NotebookInstanceLifecycleConfigName' => [ 'shape' => 'NotebookInstanceLifecycleConfigName', ], 'OnCreate' => [ 'shape' => 'NotebookInstanceLifecycleConfigList', ], 'OnStart' => [ 'shape' => 'NotebookInstanceLifecycleConfigList', ], ], ], 'CreateNotebookInstanceLifecycleConfigOutput' => [ 'type' => 'structure', 'members' => [ 'NotebookInstanceLifecycleConfigArn' => [ 'shape' => 'NotebookInstanceLifecycleConfigArn', ], ], ], 'CreateNotebookInstanceOutput' => [ 'type' => 'structure', 'members' => [ 'NotebookInstanceArn' => [ 'shape' => 'NotebookInstanceArn', ], ], ], 'CreatePipelineRequest' => [ 'type' => 'structure', 'required' => [ 'PipelineName', 'PipelineDefinition', 'ClientRequestToken', 'RoleArn', ], 'members' => [ 'PipelineName' => [ 'shape' => 'PipelineName', ], 'PipelineDisplayName' => [ 'shape' => 'PipelineName', ], 'PipelineDefinition' => [ 'shape' => 'PipelineDefinition', ], 'PipelineDescription' => [ 'shape' => 'PipelineDescription', ], 'ClientRequestToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreatePipelineResponse' => [ 'type' => 'structure', 'members' => [ 'PipelineArn' => [ 'shape' => 'PipelineArn', ], ], ], 'CreatePresignedDomainUrlRequest' => [ 'type' => 'structure', 'required' => [ 'DomainId', 'UserProfileName', ], 'members' => [ 'DomainId' => [ 'shape' => 'DomainId', ], 'UserProfileName' => [ 'shape' => 'UserProfileName', ], 'SessionExpirationDurationInSeconds' => [ 'shape' => 'SessionExpirationDurationInSeconds', ], 'ExpiresInSeconds' => [ 'shape' => 'ExpiresInSeconds', ], ], ], 'CreatePresignedDomainUrlResponse' => [ 'type' => 'structure', 'members' => [ 'AuthorizedUrl' => [ 'shape' => 'PresignedDomainUrl', ], ], ], 'CreatePresignedNotebookInstanceUrlInput' => [ 'type' => 'structure', 'required' => [ 'NotebookInstanceName', ], 'members' => [ 'NotebookInstanceName' => [ 'shape' => 'NotebookInstanceName', ], 'SessionExpirationDurationInSeconds' => [ 'shape' => 'SessionExpirationDurationInSeconds', ], ], ], 'CreatePresignedNotebookInstanceUrlOutput' => [ 'type' => 'structure', 'members' => [ 'AuthorizedUrl' => [ 'shape' => 'NotebookInstanceUrl', ], ], ], 'CreateProcessingJobRequest' => [ 'type' => 'structure', 'required' => [ 'ProcessingJobName', 'ProcessingResources', 'AppSpecification', 'RoleArn', ], 'members' => [ 'ProcessingInputs' => [ 'shape' => 'ProcessingInputs', ], 'ProcessingOutputConfig' => [ 'shape' => 'ProcessingOutputConfig', ], 'ProcessingJobName' => [ 'shape' => 'ProcessingJobName', ], 'ProcessingResources' => [ 'shape' => 'ProcessingResources', ], 'StoppingCondition' => [ 'shape' => 'ProcessingStoppingCondition', ], 'AppSpecification' => [ 'shape' => 'AppSpecification', ], 'Environment' => [ 'shape' => 'ProcessingEnvironmentMap', ], 'NetworkConfig' => [ 'shape' => 'NetworkConfig', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Tags' => [ 'shape' => 'TagList', ], 'ExperimentConfig' => [ 'shape' => 'ExperimentConfig', ], ], ], 'CreateProcessingJobResponse' => [ 'type' => 'structure', 'required' => [ 'ProcessingJobArn', ], 'members' => [ 'ProcessingJobArn' => [ 'shape' => 'ProcessingJobArn', ], ], ], 'CreateProjectInput' => [ 'type' => 'structure', 'required' => [ 'ProjectName', 'ServiceCatalogProvisioningDetails', ], 'members' => [ 'ProjectName' => [ 'shape' => 'ProjectEntityName', ], 'ProjectDescription' => [ 'shape' => 'EntityDescription', ], 'ServiceCatalogProvisioningDetails' => [ 'shape' => 'ServiceCatalogProvisioningDetails', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateProjectOutput' => [ 'type' => 'structure', 'required' => [ 'ProjectArn', 'ProjectId', ], 'members' => [ 'ProjectArn' => [ 'shape' => 'ProjectArn', ], 'ProjectId' => [ 'shape' => 'ProjectId', ], ], ], 'CreateTrainingJobRequest' => [ 'type' => 'structure', 'required' => [ 'TrainingJobName', 'AlgorithmSpecification', 'RoleArn', 'OutputDataConfig', 'ResourceConfig', 'StoppingCondition', ], 'members' => [ 'TrainingJobName' => [ 'shape' => 'TrainingJobName', ], 'HyperParameters' => [ 'shape' => 'HyperParameters', ], 'AlgorithmSpecification' => [ 'shape' => 'AlgorithmSpecification', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'OutputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'ResourceConfig' => [ 'shape' => 'ResourceConfig', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'StoppingCondition' => [ 'shape' => 'StoppingCondition', ], 'Tags' => [ 'shape' => 'TagList', ], 'EnableNetworkIsolation' => [ 'shape' => 'Boolean', ], 'EnableInterContainerTrafficEncryption' => [ 'shape' => 'Boolean', ], 'EnableManagedSpotTraining' => [ 'shape' => 'Boolean', ], 'CheckpointConfig' => [ 'shape' => 'CheckpointConfig', ], 'DebugHookConfig' => [ 'shape' => 'DebugHookConfig', ], 'DebugRuleConfigurations' => [ 'shape' => 'DebugRuleConfigurations', ], 'TensorBoardOutputConfig' => [ 'shape' => 'TensorBoardOutputConfig', ], 'ExperimentConfig' => [ 'shape' => 'ExperimentConfig', ], 'ProfilerConfig' => [ 'shape' => 'ProfilerConfig', ], 'ProfilerRuleConfigurations' => [ 'shape' => 'ProfilerRuleConfigurations', ], 'Environment' => [ 'shape' => 'TrainingEnvironmentMap', ], 'RetryStrategy' => [ 'shape' => 'RetryStrategy', ], ], ], 'CreateTrainingJobResponse' => [ 'type' => 'structure', 'required' => [ 'TrainingJobArn', ], 'members' => [ 'TrainingJobArn' => [ 'shape' => 'TrainingJobArn', ], ], ], 'CreateTransformJobRequest' => [ 'type' => 'structure', 'required' => [ 'TransformJobName', 'ModelName', 'TransformInput', 'TransformOutput', 'TransformResources', ], 'members' => [ 'TransformJobName' => [ 'shape' => 'TransformJobName', ], 'ModelName' => [ 'shape' => 'ModelName', ], 'MaxConcurrentTransforms' => [ 'shape' => 'MaxConcurrentTransforms', ], 'ModelClientConfig' => [ 'shape' => 'ModelClientConfig', ], 'MaxPayloadInMB' => [ 'shape' => 'MaxPayloadInMB', ], 'BatchStrategy' => [ 'shape' => 'BatchStrategy', ], 'Environment' => [ 'shape' => 'TransformEnvironmentMap', ], 'TransformInput' => [ 'shape' => 'TransformInput', ], 'TransformOutput' => [ 'shape' => 'TransformOutput', ], 'TransformResources' => [ 'shape' => 'TransformResources', ], 'DataProcessing' => [ 'shape' => 'DataProcessing', ], 'Tags' => [ 'shape' => 'TagList', ], 'ExperimentConfig' => [ 'shape' => 'ExperimentConfig', ], ], ], 'CreateTransformJobResponse' => [ 'type' => 'structure', 'required' => [ 'TransformJobArn', ], 'members' => [ 'TransformJobArn' => [ 'shape' => 'TransformJobArn', ], ], ], 'CreateTrialComponentRequest' => [ 'type' => 'structure', 'required' => [ 'TrialComponentName', ], 'members' => [ 'TrialComponentName' => [ 'shape' => 'ExperimentEntityName', ], 'DisplayName' => [ 'shape' => 'ExperimentEntityName', ], 'Status' => [ 'shape' => 'TrialComponentStatus', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'Parameters' => [ 'shape' => 'TrialComponentParameters', ], 'InputArtifacts' => [ 'shape' => 'TrialComponentArtifacts', ], 'OutputArtifacts' => [ 'shape' => 'TrialComponentArtifacts', ], 'MetadataProperties' => [ 'shape' => 'MetadataProperties', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateTrialComponentResponse' => [ 'type' => 'structure', 'members' => [ 'TrialComponentArn' => [ 'shape' => 'TrialComponentArn', ], ], ], 'CreateTrialRequest' => [ 'type' => 'structure', 'required' => [ 'TrialName', 'ExperimentName', ], 'members' => [ 'TrialName' => [ 'shape' => 'ExperimentEntityName', ], 'DisplayName' => [ 'shape' => 'ExperimentEntityName', ], 'ExperimentName' => [ 'shape' => 'ExperimentEntityName', ], 'MetadataProperties' => [ 'shape' => 'MetadataProperties', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateTrialResponse' => [ 'type' => 'structure', 'members' => [ 'TrialArn' => [ 'shape' => 'TrialArn', ], ], ], 'CreateUserProfileRequest' => [ 'type' => 'structure', 'required' => [ 'DomainId', 'UserProfileName', ], 'members' => [ 'DomainId' => [ 'shape' => 'DomainId', ], 'UserProfileName' => [ 'shape' => 'UserProfileName', ], 'SingleSignOnUserIdentifier' => [ 'shape' => 'SingleSignOnUserIdentifier', ], 'SingleSignOnUserValue' => [ 'shape' => 'String256', ], 'Tags' => [ 'shape' => 'TagList', ], 'UserSettings' => [ 'shape' => 'UserSettings', ], ], ], 'CreateUserProfileResponse' => [ 'type' => 'structure', 'members' => [ 'UserProfileArn' => [ 'shape' => 'UserProfileArn', ], ], ], 'CreateWorkforceRequest' => [ 'type' => 'structure', 'required' => [ 'WorkforceName', ], 'members' => [ 'CognitoConfig' => [ 'shape' => 'CognitoConfig', ], 'OidcConfig' => [ 'shape' => 'OidcConfig', ], 'SourceIpConfig' => [ 'shape' => 'SourceIpConfig', ], 'WorkforceName' => [ 'shape' => 'WorkforceName', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateWorkforceResponse' => [ 'type' => 'structure', 'required' => [ 'WorkforceArn', ], 'members' => [ 'WorkforceArn' => [ 'shape' => 'WorkforceArn', ], ], ], 'CreateWorkteamRequest' => [ 'type' => 'structure', 'required' => [ 'WorkteamName', 'MemberDefinitions', 'Description', ], 'members' => [ 'WorkteamName' => [ 'shape' => 'WorkteamName', ], 'WorkforceName' => [ 'shape' => 'WorkforceName', ], 'MemberDefinitions' => [ 'shape' => 'MemberDefinitions', ], 'Description' => [ 'shape' => 'String200', ], 'NotificationConfiguration' => [ 'shape' => 'NotificationConfiguration', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateWorkteamResponse' => [ 'type' => 'structure', 'members' => [ 'WorkteamArn' => [ 'shape' => 'WorkteamArn', ], ], ], 'CreationTime' => [ 'type' => 'timestamp', ], 'CsvContentType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9])*\\/[a-zA-Z0-9](-*[a-zA-Z0-9.])*', ], 'CsvContentTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'CsvContentType', ], 'max' => 10, 'min' => 1, ], 'CustomImage' => [ 'type' => 'structure', 'required' => [ 'ImageName', 'AppImageConfigName', ], 'members' => [ 'ImageName' => [ 'shape' => 'ImageName', ], 'ImageVersionNumber' => [ 'shape' => 'ImageVersionNumber', 'box' => true, ], 'AppImageConfigName' => [ 'shape' => 'AppImageConfigName', ], ], ], 'CustomImages' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomImage', ], 'max' => 30, ], 'DataCaptureConfig' => [ 'type' => 'structure', 'required' => [ 'InitialSamplingPercentage', 'DestinationS3Uri', 'CaptureOptions', ], 'members' => [ 'EnableCapture' => [ 'shape' => 'EnableCapture', ], 'InitialSamplingPercentage' => [ 'shape' => 'SamplingPercentage', ], 'DestinationS3Uri' => [ 'shape' => 'DestinationS3Uri', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'CaptureOptions' => [ 'shape' => 'CaptureOptionList', ], 'CaptureContentTypeHeader' => [ 'shape' => 'CaptureContentTypeHeader', ], ], ], 'DataCaptureConfigSummary' => [ 'type' => 'structure', 'required' => [ 'EnableCapture', 'CaptureStatus', 'CurrentSamplingPercentage', 'DestinationS3Uri', 'KmsKeyId', ], 'members' => [ 'EnableCapture' => [ 'shape' => 'EnableCapture', ], 'CaptureStatus' => [ 'shape' => 'CaptureStatus', ], 'CurrentSamplingPercentage' => [ 'shape' => 'SamplingPercentage', ], 'DestinationS3Uri' => [ 'shape' => 'DestinationS3Uri', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'DataCatalogConfig' => [ 'type' => 'structure', 'required' => [ 'TableName', 'Catalog', 'Database', ], 'members' => [ 'TableName' => [ 'shape' => 'TableName', ], 'Catalog' => [ 'shape' => 'Catalog', ], 'Database' => [ 'shape' => 'Database', ], ], ], 'DataDistributionType' => [ 'type' => 'string', 'enum' => [ 'FullyReplicated', 'ShardedByS3Key', ], ], 'DataExplorationNotebookLocation' => [ 'type' => 'string', 'min' => 1, ], 'DataInputConfig' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\S\\s]+', ], 'DataProcessing' => [ 'type' => 'structure', 'members' => [ 'InputFilter' => [ 'shape' => 'JsonPath', ], 'OutputFilter' => [ 'shape' => 'JsonPath', ], 'JoinSource' => [ 'shape' => 'JoinSource', ], ], ], 'DataQualityAppSpecification' => [ 'type' => 'structure', 'required' => [ 'ImageUri', ], 'members' => [ 'ImageUri' => [ 'shape' => 'ImageUri', ], 'ContainerEntrypoint' => [ 'shape' => 'ContainerEntrypoint', ], 'ContainerArguments' => [ 'shape' => 'MonitoringContainerArguments', ], 'RecordPreprocessorSourceUri' => [ 'shape' => 'S3Uri', ], 'PostAnalyticsProcessorSourceUri' => [ 'shape' => 'S3Uri', ], 'Environment' => [ 'shape' => 'MonitoringEnvironmentMap', ], ], ], 'DataQualityBaselineConfig' => [ 'type' => 'structure', 'members' => [ 'BaseliningJobName' => [ 'shape' => 'ProcessingJobName', ], 'ConstraintsResource' => [ 'shape' => 'MonitoringConstraintsResource', ], 'StatisticsResource' => [ 'shape' => 'MonitoringStatisticsResource', ], ], ], 'DataQualityJobInput' => [ 'type' => 'structure', 'required' => [ 'EndpointInput', ], 'members' => [ 'EndpointInput' => [ 'shape' => 'EndpointInput', ], ], ], 'DataSource' => [ 'type' => 'structure', 'members' => [ 'S3DataSource' => [ 'shape' => 'S3DataSource', ], 'FileSystemDataSource' => [ 'shape' => 'FileSystemDataSource', ], ], ], 'Database' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'DatasetDefinition' => [ 'type' => 'structure', 'members' => [ 'AthenaDatasetDefinition' => [ 'shape' => 'AthenaDatasetDefinition', ], 'RedshiftDatasetDefinition' => [ 'shape' => 'RedshiftDatasetDefinition', ], 'LocalPath' => [ 'shape' => 'ProcessingLocalPath', ], 'DataDistributionType' => [ 'shape' => 'DataDistributionType', ], 'InputMode' => [ 'shape' => 'InputMode', ], ], ], 'DebugHookConfig' => [ 'type' => 'structure', 'required' => [ 'S3OutputPath', ], 'members' => [ 'LocalPath' => [ 'shape' => 'DirectoryPath', ], 'S3OutputPath' => [ 'shape' => 'S3Uri', ], 'HookParameters' => [ 'shape' => 'HookParameters', ], 'CollectionConfigurations' => [ 'shape' => 'CollectionConfigurations', ], ], ], 'DebugRuleConfiguration' => [ 'type' => 'structure', 'required' => [ 'RuleConfigurationName', 'RuleEvaluatorImage', ], 'members' => [ 'RuleConfigurationName' => [ 'shape' => 'RuleConfigurationName', ], 'LocalPath' => [ 'shape' => 'DirectoryPath', ], 'S3OutputPath' => [ 'shape' => 'S3Uri', ], 'RuleEvaluatorImage' => [ 'shape' => 'AlgorithmImage', ], 'InstanceType' => [ 'shape' => 'ProcessingInstanceType', ], 'VolumeSizeInGB' => [ 'shape' => 'OptionalVolumeSizeInGB', ], 'RuleParameters' => [ 'shape' => 'RuleParameters', ], ], ], 'DebugRuleConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'DebugRuleConfiguration', ], 'max' => 20, 'min' => 0, ], 'DebugRuleEvaluationStatus' => [ 'type' => 'structure', 'members' => [ 'RuleConfigurationName' => [ 'shape' => 'RuleConfigurationName', ], 'RuleEvaluationJobArn' => [ 'shape' => 'ProcessingJobArn', ], 'RuleEvaluationStatus' => [ 'shape' => 'RuleEvaluationStatus', ], 'StatusDetails' => [ 'shape' => 'StatusDetails', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'DebugRuleEvaluationStatuses' => [ 'type' => 'list', 'member' => [ 'shape' => 'DebugRuleEvaluationStatus', ], 'max' => 20, 'min' => 0, ], 'DefaultGid' => [ 'type' => 'integer', 'max' => 65535, 'min' => 0, ], 'DefaultUid' => [ 'type' => 'integer', 'max' => 65535, 'min' => 0, ], 'DeleteActionRequest' => [ 'type' => 'structure', 'required' => [ 'ActionName', ], 'members' => [ 'ActionName' => [ 'shape' => 'ExperimentEntityName', ], ], ], 'DeleteActionResponse' => [ 'type' => 'structure', 'members' => [ 'ActionArn' => [ 'shape' => 'ActionArn', ], ], ], 'DeleteAlgorithmInput' => [ 'type' => 'structure', 'required' => [ 'AlgorithmName', ], 'members' => [ 'AlgorithmName' => [ 'shape' => 'EntityName', ], ], ], 'DeleteAppImageConfigRequest' => [ 'type' => 'structure', 'required' => [ 'AppImageConfigName', ], 'members' => [ 'AppImageConfigName' => [ 'shape' => 'AppImageConfigName', ], ], ], 'DeleteAppRequest' => [ 'type' => 'structure', 'required' => [ 'DomainId', 'UserProfileName', 'AppType', 'AppName', ], 'members' => [ 'DomainId' => [ 'shape' => 'DomainId', ], 'UserProfileName' => [ 'shape' => 'UserProfileName', ], 'AppType' => [ 'shape' => 'AppType', ], 'AppName' => [ 'shape' => 'AppName', ], ], ], 'DeleteArtifactRequest' => [ 'type' => 'structure', 'members' => [ 'ArtifactArn' => [ 'shape' => 'ArtifactArn', ], 'Source' => [ 'shape' => 'ArtifactSource', ], ], ], 'DeleteArtifactResponse' => [ 'type' => 'structure', 'members' => [ 'ArtifactArn' => [ 'shape' => 'ArtifactArn', ], ], ], 'DeleteAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'SourceArn', 'DestinationArn', ], 'members' => [ 'SourceArn' => [ 'shape' => 'AssociationEntityArn', ], 'DestinationArn' => [ 'shape' => 'AssociationEntityArn', ], ], ], 'DeleteAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'SourceArn' => [ 'shape' => 'AssociationEntityArn', ], 'DestinationArn' => [ 'shape' => 'AssociationEntityArn', ], ], ], 'DeleteCodeRepositoryInput' => [ 'type' => 'structure', 'required' => [ 'CodeRepositoryName', ], 'members' => [ 'CodeRepositoryName' => [ 'shape' => 'EntityName', ], ], ], 'DeleteContextRequest' => [ 'type' => 'structure', 'required' => [ 'ContextName', ], 'members' => [ 'ContextName' => [ 'shape' => 'ExperimentEntityName', ], ], ], 'DeleteContextResponse' => [ 'type' => 'structure', 'members' => [ 'ContextArn' => [ 'shape' => 'ContextArn', ], ], ], 'DeleteDataQualityJobDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'JobDefinitionName', ], 'members' => [ 'JobDefinitionName' => [ 'shape' => 'MonitoringJobDefinitionName', ], ], ], 'DeleteDeviceFleetRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceFleetName', ], 'members' => [ 'DeviceFleetName' => [ 'shape' => 'EntityName', ], ], ], 'DeleteDomainRequest' => [ 'type' => 'structure', 'required' => [ 'DomainId', ], 'members' => [ 'DomainId' => [ 'shape' => 'DomainId', ], 'RetentionPolicy' => [ 'shape' => 'RetentionPolicy', ], ], ], 'DeleteEndpointConfigInput' => [ 'type' => 'structure', 'required' => [ 'EndpointConfigName', ], 'members' => [ 'EndpointConfigName' => [ 'shape' => 'EndpointConfigName', ], ], ], 'DeleteEndpointInput' => [ 'type' => 'structure', 'required' => [ 'EndpointName', ], 'members' => [ 'EndpointName' => [ 'shape' => 'EndpointName', ], ], ], 'DeleteExperimentRequest' => [ 'type' => 'structure', 'required' => [ 'ExperimentName', ], 'members' => [ 'ExperimentName' => [ 'shape' => 'ExperimentEntityName', ], ], ], 'DeleteExperimentResponse' => [ 'type' => 'structure', 'members' => [ 'ExperimentArn' => [ 'shape' => 'ExperimentArn', ], ], ], 'DeleteFeatureGroupRequest' => [ 'type' => 'structure', 'required' => [ 'FeatureGroupName', ], 'members' => [ 'FeatureGroupName' => [ 'shape' => 'FeatureGroupName', ], ], ], 'DeleteFlowDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'FlowDefinitionName', ], 'members' => [ 'FlowDefinitionName' => [ 'shape' => 'FlowDefinitionName', ], ], ], 'DeleteFlowDefinitionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteHumanTaskUiRequest' => [ 'type' => 'structure', 'required' => [ 'HumanTaskUiName', ], 'members' => [ 'HumanTaskUiName' => [ 'shape' => 'HumanTaskUiName', ], ], ], 'DeleteHumanTaskUiResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteImageRequest' => [ 'type' => 'structure', 'required' => [ 'ImageName', ], 'members' => [ 'ImageName' => [ 'shape' => 'ImageName', ], ], ], 'DeleteImageResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteImageVersionRequest' => [ 'type' => 'structure', 'required' => [ 'ImageName', 'Version', ], 'members' => [ 'ImageName' => [ 'shape' => 'ImageName', ], 'Version' => [ 'shape' => 'ImageVersionNumber', ], ], ], 'DeleteImageVersionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteModelBiasJobDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'JobDefinitionName', ], 'members' => [ 'JobDefinitionName' => [ 'shape' => 'MonitoringJobDefinitionName', ], ], ], 'DeleteModelExplainabilityJobDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'JobDefinitionName', ], 'members' => [ 'JobDefinitionName' => [ 'shape' => 'MonitoringJobDefinitionName', ], ], ], 'DeleteModelInput' => [ 'type' => 'structure', 'required' => [ 'ModelName', ], 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], ], ], 'DeleteModelPackageGroupInput' => [ 'type' => 'structure', 'required' => [ 'ModelPackageGroupName', ], 'members' => [ 'ModelPackageGroupName' => [ 'shape' => 'ArnOrName', ], ], ], 'DeleteModelPackageGroupPolicyInput' => [ 'type' => 'structure', 'required' => [ 'ModelPackageGroupName', ], 'members' => [ 'ModelPackageGroupName' => [ 'shape' => 'EntityName', ], ], ], 'DeleteModelPackageInput' => [ 'type' => 'structure', 'required' => [ 'ModelPackageName', ], 'members' => [ 'ModelPackageName' => [ 'shape' => 'VersionedArnOrName', ], ], ], 'DeleteModelQualityJobDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'JobDefinitionName', ], 'members' => [ 'JobDefinitionName' => [ 'shape' => 'MonitoringJobDefinitionName', ], ], ], 'DeleteMonitoringScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'MonitoringScheduleName', ], 'members' => [ 'MonitoringScheduleName' => [ 'shape' => 'MonitoringScheduleName', ], ], ], 'DeleteNotebookInstanceInput' => [ 'type' => 'structure', 'required' => [ 'NotebookInstanceName', ], 'members' => [ 'NotebookInstanceName' => [ 'shape' => 'NotebookInstanceName', ], ], ], 'DeleteNotebookInstanceLifecycleConfigInput' => [ 'type' => 'structure', 'required' => [ 'NotebookInstanceLifecycleConfigName', ], 'members' => [ 'NotebookInstanceLifecycleConfigName' => [ 'shape' => 'NotebookInstanceLifecycleConfigName', ], ], ], 'DeletePipelineRequest' => [ 'type' => 'structure', 'required' => [ 'PipelineName', 'ClientRequestToken', ], 'members' => [ 'PipelineName' => [ 'shape' => 'PipelineName', ], 'ClientRequestToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], ], ], 'DeletePipelineResponse' => [ 'type' => 'structure', 'members' => [ 'PipelineArn' => [ 'shape' => 'PipelineArn', ], ], ], 'DeleteProjectInput' => [ 'type' => 'structure', 'required' => [ 'ProjectName', ], 'members' => [ 'ProjectName' => [ 'shape' => 'ProjectEntityName', ], ], ], 'DeleteTagsInput' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'DeleteTagsOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTrialComponentRequest' => [ 'type' => 'structure', 'required' => [ 'TrialComponentName', ], 'members' => [ 'TrialComponentName' => [ 'shape' => 'ExperimentEntityName', ], ], ], 'DeleteTrialComponentResponse' => [ 'type' => 'structure', 'members' => [ 'TrialComponentArn' => [ 'shape' => 'TrialComponentArn', ], ], ], 'DeleteTrialRequest' => [ 'type' => 'structure', 'required' => [ 'TrialName', ], 'members' => [ 'TrialName' => [ 'shape' => 'ExperimentEntityName', ], ], ], 'DeleteTrialResponse' => [ 'type' => 'structure', 'members' => [ 'TrialArn' => [ 'shape' => 'TrialArn', ], ], ], 'DeleteUserProfileRequest' => [ 'type' => 'structure', 'required' => [ 'DomainId', 'UserProfileName', ], 'members' => [ 'DomainId' => [ 'shape' => 'DomainId', ], 'UserProfileName' => [ 'shape' => 'UserProfileName', ], ], ], 'DeleteWorkforceRequest' => [ 'type' => 'structure', 'required' => [ 'WorkforceName', ], 'members' => [ 'WorkforceName' => [ 'shape' => 'WorkforceName', ], ], ], 'DeleteWorkforceResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteWorkteamRequest' => [ 'type' => 'structure', 'required' => [ 'WorkteamName', ], 'members' => [ 'WorkteamName' => [ 'shape' => 'WorkteamName', ], ], ], 'DeleteWorkteamResponse' => [ 'type' => 'structure', 'required' => [ 'Success', ], 'members' => [ 'Success' => [ 'shape' => 'Success', ], ], ], 'DeployedImage' => [ 'type' => 'structure', 'members' => [ 'SpecifiedImage' => [ 'shape' => 'ContainerImage', ], 'ResolvedImage' => [ 'shape' => 'ContainerImage', ], 'ResolutionTime' => [ 'shape' => 'Timestamp', ], ], ], 'DeployedImages' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeployedImage', ], ], 'DeploymentConfig' => [ 'type' => 'structure', 'required' => [ 'BlueGreenUpdatePolicy', ], 'members' => [ 'BlueGreenUpdatePolicy' => [ 'shape' => 'BlueGreenUpdatePolicy', ], 'AutoRollbackConfiguration' => [ 'shape' => 'AutoRollbackConfig', ], ], ], 'DeregisterDevicesRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceFleetName', 'DeviceNames', ], 'members' => [ 'DeviceFleetName' => [ 'shape' => 'EntityName', ], 'DeviceNames' => [ 'shape' => 'DeviceNames', ], ], ], 'DescribeActionRequest' => [ 'type' => 'structure', 'required' => [ 'ActionName', ], 'members' => [ 'ActionName' => [ 'shape' => 'ExperimentEntityName', ], ], ], 'DescribeActionResponse' => [ 'type' => 'structure', 'members' => [ 'ActionName' => [ 'shape' => 'ExperimentEntityName', ], 'ActionArn' => [ 'shape' => 'ActionArn', ], 'Source' => [ 'shape' => 'ActionSource', ], 'ActionType' => [ 'shape' => 'String256', ], 'Description' => [ 'shape' => 'ExperimentDescription', ], 'Status' => [ 'shape' => 'ActionStatus', ], 'Properties' => [ 'shape' => 'LineageEntityParameters', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'CreatedBy' => [ 'shape' => 'UserContext', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedBy' => [ 'shape' => 'UserContext', ], 'MetadataProperties' => [ 'shape' => 'MetadataProperties', ], ], ], 'DescribeAlgorithmInput' => [ 'type' => 'structure', 'required' => [ 'AlgorithmName', ], 'members' => [ 'AlgorithmName' => [ 'shape' => 'ArnOrName', ], ], ], 'DescribeAlgorithmOutput' => [ 'type' => 'structure', 'required' => [ 'AlgorithmName', 'AlgorithmArn', 'CreationTime', 'TrainingSpecification', 'AlgorithmStatus', 'AlgorithmStatusDetails', ], 'members' => [ 'AlgorithmName' => [ 'shape' => 'EntityName', ], 'AlgorithmArn' => [ 'shape' => 'AlgorithmArn', ], 'AlgorithmDescription' => [ 'shape' => 'EntityDescription', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'TrainingSpecification' => [ 'shape' => 'TrainingSpecification', ], 'InferenceSpecification' => [ 'shape' => 'InferenceSpecification', ], 'ValidationSpecification' => [ 'shape' => 'AlgorithmValidationSpecification', ], 'AlgorithmStatus' => [ 'shape' => 'AlgorithmStatus', ], 'AlgorithmStatusDetails' => [ 'shape' => 'AlgorithmStatusDetails', ], 'ProductId' => [ 'shape' => 'ProductId', ], 'CertifyForMarketplace' => [ 'shape' => 'CertifyForMarketplace', ], ], ], 'DescribeAppImageConfigRequest' => [ 'type' => 'structure', 'required' => [ 'AppImageConfigName', ], 'members' => [ 'AppImageConfigName' => [ 'shape' => 'AppImageConfigName', ], ], ], 'DescribeAppImageConfigResponse' => [ 'type' => 'structure', 'members' => [ 'AppImageConfigArn' => [ 'shape' => 'AppImageConfigArn', ], 'AppImageConfigName' => [ 'shape' => 'AppImageConfigName', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'KernelGatewayImageConfig' => [ 'shape' => 'KernelGatewayImageConfig', ], ], ], 'DescribeAppRequest' => [ 'type' => 'structure', 'required' => [ 'DomainId', 'UserProfileName', 'AppType', 'AppName', ], 'members' => [ 'DomainId' => [ 'shape' => 'DomainId', ], 'UserProfileName' => [ 'shape' => 'UserProfileName', ], 'AppType' => [ 'shape' => 'AppType', ], 'AppName' => [ 'shape' => 'AppName', ], ], ], 'DescribeAppResponse' => [ 'type' => 'structure', 'members' => [ 'AppArn' => [ 'shape' => 'AppArn', ], 'AppType' => [ 'shape' => 'AppType', ], 'AppName' => [ 'shape' => 'AppName', ], 'DomainId' => [ 'shape' => 'DomainId', ], 'UserProfileName' => [ 'shape' => 'UserProfileName', ], 'Status' => [ 'shape' => 'AppStatus', ], 'LastHealthCheckTimestamp' => [ 'shape' => 'Timestamp', ], 'LastUserActivityTimestamp' => [ 'shape' => 'Timestamp', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'ResourceSpec' => [ 'shape' => 'ResourceSpec', ], ], ], 'DescribeArtifactRequest' => [ 'type' => 'structure', 'required' => [ 'ArtifactArn', ], 'members' => [ 'ArtifactArn' => [ 'shape' => 'ArtifactArn', ], ], ], 'DescribeArtifactResponse' => [ 'type' => 'structure', 'members' => [ 'ArtifactName' => [ 'shape' => 'ExperimentEntityName', ], 'ArtifactArn' => [ 'shape' => 'ArtifactArn', ], 'Source' => [ 'shape' => 'ArtifactSource', ], 'ArtifactType' => [ 'shape' => 'String256', ], 'Properties' => [ 'shape' => 'LineageEntityParameters', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'CreatedBy' => [ 'shape' => 'UserContext', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedBy' => [ 'shape' => 'UserContext', ], 'MetadataProperties' => [ 'shape' => 'MetadataProperties', ], ], ], 'DescribeAutoMLJobRequest' => [ 'type' => 'structure', 'required' => [ 'AutoMLJobName', ], 'members' => [ 'AutoMLJobName' => [ 'shape' => 'AutoMLJobName', ], ], ], 'DescribeAutoMLJobResponse' => [ 'type' => 'structure', 'required' => [ 'AutoMLJobName', 'AutoMLJobArn', 'InputDataConfig', 'OutputDataConfig', 'RoleArn', 'CreationTime', 'LastModifiedTime', 'AutoMLJobStatus', 'AutoMLJobSecondaryStatus', ], 'members' => [ 'AutoMLJobName' => [ 'shape' => 'AutoMLJobName', ], 'AutoMLJobArn' => [ 'shape' => 'AutoMLJobArn', ], 'InputDataConfig' => [ 'shape' => 'AutoMLInputDataConfig', ], 'OutputDataConfig' => [ 'shape' => 'AutoMLOutputDataConfig', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'AutoMLJobObjective' => [ 'shape' => 'AutoMLJobObjective', ], 'ProblemType' => [ 'shape' => 'ProblemType', ], 'AutoMLJobConfig' => [ 'shape' => 'AutoMLJobConfig', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'FailureReason' => [ 'shape' => 'AutoMLFailureReason', ], 'PartialFailureReasons' => [ 'shape' => 'AutoMLPartialFailureReasons', ], 'BestCandidate' => [ 'shape' => 'AutoMLCandidate', ], 'AutoMLJobStatus' => [ 'shape' => 'AutoMLJobStatus', ], 'AutoMLJobSecondaryStatus' => [ 'shape' => 'AutoMLJobSecondaryStatus', ], 'GenerateCandidateDefinitionsOnly' => [ 'shape' => 'GenerateCandidateDefinitionsOnly', ], 'AutoMLJobArtifacts' => [ 'shape' => 'AutoMLJobArtifacts', ], 'ResolvedAttributes' => [ 'shape' => 'ResolvedAttributes', ], 'ModelDeployConfig' => [ 'shape' => 'ModelDeployConfig', ], 'ModelDeployResult' => [ 'shape' => 'ModelDeployResult', ], ], ], 'DescribeCodeRepositoryInput' => [ 'type' => 'structure', 'required' => [ 'CodeRepositoryName', ], 'members' => [ 'CodeRepositoryName' => [ 'shape' => 'EntityName', ], ], ], 'DescribeCodeRepositoryOutput' => [ 'type' => 'structure', 'required' => [ 'CodeRepositoryName', 'CodeRepositoryArn', 'CreationTime', 'LastModifiedTime', ], 'members' => [ 'CodeRepositoryName' => [ 'shape' => 'EntityName', ], 'CodeRepositoryArn' => [ 'shape' => 'CodeRepositoryArn', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'LastModifiedTime' => [ 'shape' => 'LastModifiedTime', ], 'GitConfig' => [ 'shape' => 'GitConfig', ], ], ], 'DescribeCompilationJobRequest' => [ 'type' => 'structure', 'required' => [ 'CompilationJobName', ], 'members' => [ 'CompilationJobName' => [ 'shape' => 'EntityName', ], ], ], 'DescribeCompilationJobResponse' => [ 'type' => 'structure', 'required' => [ 'CompilationJobName', 'CompilationJobArn', 'CompilationJobStatus', 'StoppingCondition', 'CreationTime', 'LastModifiedTime', 'FailureReason', 'ModelArtifacts', 'RoleArn', 'InputConfig', 'OutputConfig', ], 'members' => [ 'CompilationJobName' => [ 'shape' => 'EntityName', ], 'CompilationJobArn' => [ 'shape' => 'CompilationJobArn', ], 'CompilationJobStatus' => [ 'shape' => 'CompilationJobStatus', ], 'CompilationStartTime' => [ 'shape' => 'Timestamp', ], 'CompilationEndTime' => [ 'shape' => 'Timestamp', ], 'StoppingCondition' => [ 'shape' => 'StoppingCondition', ], 'InferenceImage' => [ 'shape' => 'InferenceImage', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'LastModifiedTime' => [ 'shape' => 'LastModifiedTime', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'ModelArtifacts' => [ 'shape' => 'ModelArtifacts', ], 'ModelDigests' => [ 'shape' => 'ModelDigests', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'InputConfig' => [ 'shape' => 'InputConfig', ], 'OutputConfig' => [ 'shape' => 'OutputConfig', ], 'VpcConfig' => [ 'shape' => 'NeoVpcConfig', ], ], ], 'DescribeContextRequest' => [ 'type' => 'structure', 'required' => [ 'ContextName', ], 'members' => [ 'ContextName' => [ 'shape' => 'ExperimentEntityName', ], ], ], 'DescribeContextResponse' => [ 'type' => 'structure', 'members' => [ 'ContextName' => [ 'shape' => 'ExperimentEntityName', ], 'ContextArn' => [ 'shape' => 'ContextArn', ], 'Source' => [ 'shape' => 'ContextSource', ], 'ContextType' => [ 'shape' => 'String256', ], 'Description' => [ 'shape' => 'ExperimentDescription', ], 'Properties' => [ 'shape' => 'LineageEntityParameters', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'CreatedBy' => [ 'shape' => 'UserContext', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedBy' => [ 'shape' => 'UserContext', ], ], ], 'DescribeDataQualityJobDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'JobDefinitionName', ], 'members' => [ 'JobDefinitionName' => [ 'shape' => 'MonitoringJobDefinitionName', ], ], ], 'DescribeDataQualityJobDefinitionResponse' => [ 'type' => 'structure', 'required' => [ 'JobDefinitionArn', 'JobDefinitionName', 'CreationTime', 'DataQualityAppSpecification', 'DataQualityJobInput', 'DataQualityJobOutputConfig', 'JobResources', 'RoleArn', ], 'members' => [ 'JobDefinitionArn' => [ 'shape' => 'MonitoringJobDefinitionArn', ], 'JobDefinitionName' => [ 'shape' => 'MonitoringJobDefinitionName', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'DataQualityBaselineConfig' => [ 'shape' => 'DataQualityBaselineConfig', ], 'DataQualityAppSpecification' => [ 'shape' => 'DataQualityAppSpecification', ], 'DataQualityJobInput' => [ 'shape' => 'DataQualityJobInput', ], 'DataQualityJobOutputConfig' => [ 'shape' => 'MonitoringOutputConfig', ], 'JobResources' => [ 'shape' => 'MonitoringResources', ], 'NetworkConfig' => [ 'shape' => 'MonitoringNetworkConfig', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'StoppingCondition' => [ 'shape' => 'MonitoringStoppingCondition', ], ], ], 'DescribeDeviceFleetRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceFleetName', ], 'members' => [ 'DeviceFleetName' => [ 'shape' => 'EntityName', ], ], ], 'DescribeDeviceFleetResponse' => [ 'type' => 'structure', 'required' => [ 'DeviceFleetName', 'DeviceFleetArn', 'OutputConfig', 'CreationTime', 'LastModifiedTime', ], 'members' => [ 'DeviceFleetName' => [ 'shape' => 'EntityName', ], 'DeviceFleetArn' => [ 'shape' => 'DeviceFleetArn', ], 'OutputConfig' => [ 'shape' => 'EdgeOutputConfig', ], 'Description' => [ 'shape' => 'DeviceFleetDescription', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'IotRoleAlias' => [ 'shape' => 'IotRoleAlias', ], ], ], 'DescribeDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceName', 'DeviceFleetName', ], 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'DeviceName' => [ 'shape' => 'EntityName', ], 'DeviceFleetName' => [ 'shape' => 'EntityName', ], ], ], 'DescribeDeviceResponse' => [ 'type' => 'structure', 'required' => [ 'DeviceName', 'DeviceFleetName', 'RegistrationTime', ], 'members' => [ 'DeviceArn' => [ 'shape' => 'DeviceArn', ], 'DeviceName' => [ 'shape' => 'EntityName', ], 'Description' => [ 'shape' => 'DeviceDescription', ], 'DeviceFleetName' => [ 'shape' => 'EntityName', ], 'IotThingName' => [ 'shape' => 'ThingName', ], 'RegistrationTime' => [ 'shape' => 'Timestamp', ], 'LatestHeartbeat' => [ 'shape' => 'Timestamp', ], 'Models' => [ 'shape' => 'EdgeModels', ], 'MaxModels' => [ 'shape' => 'Integer', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeDomainRequest' => [ 'type' => 'structure', 'required' => [ 'DomainId', ], 'members' => [ 'DomainId' => [ 'shape' => 'DomainId', ], ], ], 'DescribeDomainResponse' => [ 'type' => 'structure', 'members' => [ 'DomainArn' => [ 'shape' => 'DomainArn', ], 'DomainId' => [ 'shape' => 'DomainId', ], 'DomainName' => [ 'shape' => 'DomainName', ], 'HomeEfsFileSystemId' => [ 'shape' => 'ResourceId', ], 'SingleSignOnManagedApplicationInstanceId' => [ 'shape' => 'String256', ], 'Status' => [ 'shape' => 'DomainStatus', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'LastModifiedTime' => [ 'shape' => 'LastModifiedTime', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'AuthMode' => [ 'shape' => 'AuthMode', ], 'DefaultUserSettings' => [ 'shape' => 'UserSettings', ], 'AppNetworkAccessType' => [ 'shape' => 'AppNetworkAccessType', ], 'HomeEfsFileSystemKmsKeyId' => [ 'shape' => 'KmsKeyId', 'deprecated' => true, 'deprecatedMessage' => 'This property is deprecated, use KmsKeyId instead.', ], 'SubnetIds' => [ 'shape' => 'Subnets', ], 'Url' => [ 'shape' => 'String1024', ], 'VpcId' => [ 'shape' => 'VpcId', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'DescribeEdgePackagingJobRequest' => [ 'type' => 'structure', 'required' => [ 'EdgePackagingJobName', ], 'members' => [ 'EdgePackagingJobName' => [ 'shape' => 'EntityName', ], ], ], 'DescribeEdgePackagingJobResponse' => [ 'type' => 'structure', 'required' => [ 'EdgePackagingJobArn', 'EdgePackagingJobName', 'EdgePackagingJobStatus', ], 'members' => [ 'EdgePackagingJobArn' => [ 'shape' => 'EdgePackagingJobArn', ], 'EdgePackagingJobName' => [ 'shape' => 'EntityName', ], 'CompilationJobName' => [ 'shape' => 'EntityName', ], 'ModelName' => [ 'shape' => 'EntityName', ], 'ModelVersion' => [ 'shape' => 'EdgeVersion', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'OutputConfig' => [ 'shape' => 'EdgeOutputConfig', ], 'ResourceKey' => [ 'shape' => 'KmsKeyId', ], 'EdgePackagingJobStatus' => [ 'shape' => 'EdgePackagingJobStatus', ], 'EdgePackagingJobStatusMessage' => [ 'shape' => 'String', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'ModelArtifact' => [ 'shape' => 'S3Uri', ], 'ModelSignature' => [ 'shape' => 'String', ], 'PresetDeploymentOutput' => [ 'shape' => 'EdgePresetDeploymentOutput', ], ], ], 'DescribeEndpointConfigInput' => [ 'type' => 'structure', 'required' => [ 'EndpointConfigName', ], 'members' => [ 'EndpointConfigName' => [ 'shape' => 'EndpointConfigName', ], ], ], 'DescribeEndpointConfigOutput' => [ 'type' => 'structure', 'required' => [ 'EndpointConfigName', 'EndpointConfigArn', 'ProductionVariants', 'CreationTime', ], 'members' => [ 'EndpointConfigName' => [ 'shape' => 'EndpointConfigName', ], 'EndpointConfigArn' => [ 'shape' => 'EndpointConfigArn', ], 'ProductionVariants' => [ 'shape' => 'ProductionVariantList', ], 'DataCaptureConfig' => [ 'shape' => 'DataCaptureConfig', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeEndpointInput' => [ 'type' => 'structure', 'required' => [ 'EndpointName', ], 'members' => [ 'EndpointName' => [ 'shape' => 'EndpointName', ], ], ], 'DescribeEndpointOutput' => [ 'type' => 'structure', 'required' => [ 'EndpointName', 'EndpointArn', 'EndpointConfigName', 'EndpointStatus', 'CreationTime', 'LastModifiedTime', ], 'members' => [ 'EndpointName' => [ 'shape' => 'EndpointName', ], 'EndpointArn' => [ 'shape' => 'EndpointArn', ], 'EndpointConfigName' => [ 'shape' => 'EndpointConfigName', ], 'ProductionVariants' => [ 'shape' => 'ProductionVariantSummaryList', ], 'DataCaptureConfig' => [ 'shape' => 'DataCaptureConfigSummary', ], 'EndpointStatus' => [ 'shape' => 'EndpointStatus', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastDeploymentConfig' => [ 'shape' => 'DeploymentConfig', ], ], ], 'DescribeExperimentRequest' => [ 'type' => 'structure', 'required' => [ 'ExperimentName', ], 'members' => [ 'ExperimentName' => [ 'shape' => 'ExperimentEntityName', ], ], ], 'DescribeExperimentResponse' => [ 'type' => 'structure', 'members' => [ 'ExperimentName' => [ 'shape' => 'ExperimentEntityName', ], 'ExperimentArn' => [ 'shape' => 'ExperimentArn', ], 'DisplayName' => [ 'shape' => 'ExperimentEntityName', ], 'Source' => [ 'shape' => 'ExperimentSource', ], 'Description' => [ 'shape' => 'ExperimentDescription', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'CreatedBy' => [ 'shape' => 'UserContext', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedBy' => [ 'shape' => 'UserContext', ], ], ], 'DescribeFeatureGroupRequest' => [ 'type' => 'structure', 'required' => [ 'FeatureGroupName', ], 'members' => [ 'FeatureGroupName' => [ 'shape' => 'FeatureGroupName', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeFeatureGroupResponse' => [ 'type' => 'structure', 'required' => [ 'FeatureGroupArn', 'FeatureGroupName', 'RecordIdentifierFeatureName', 'EventTimeFeatureName', 'FeatureDefinitions', 'CreationTime', 'NextToken', ], 'members' => [ 'FeatureGroupArn' => [ 'shape' => 'FeatureGroupArn', ], 'FeatureGroupName' => [ 'shape' => 'FeatureGroupName', ], 'RecordIdentifierFeatureName' => [ 'shape' => 'FeatureName', ], 'EventTimeFeatureName' => [ 'shape' => 'FeatureName', ], 'FeatureDefinitions' => [ 'shape' => 'FeatureDefinitions', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'OnlineStoreConfig' => [ 'shape' => 'OnlineStoreConfig', ], 'OfflineStoreConfig' => [ 'shape' => 'OfflineStoreConfig', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'FeatureGroupStatus' => [ 'shape' => 'FeatureGroupStatus', ], 'OfflineStoreStatus' => [ 'shape' => 'OfflineStoreStatus', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'Description' => [ 'shape' => 'Description', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeFlowDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'FlowDefinitionName', ], 'members' => [ 'FlowDefinitionName' => [ 'shape' => 'FlowDefinitionName', ], ], ], 'DescribeFlowDefinitionResponse' => [ 'type' => 'structure', 'required' => [ 'FlowDefinitionArn', 'FlowDefinitionName', 'FlowDefinitionStatus', 'CreationTime', 'HumanLoopConfig', 'OutputConfig', 'RoleArn', ], 'members' => [ 'FlowDefinitionArn' => [ 'shape' => 'FlowDefinitionArn', ], 'FlowDefinitionName' => [ 'shape' => 'FlowDefinitionName', ], 'FlowDefinitionStatus' => [ 'shape' => 'FlowDefinitionStatus', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'HumanLoopRequestSource' => [ 'shape' => 'HumanLoopRequestSource', ], 'HumanLoopActivationConfig' => [ 'shape' => 'HumanLoopActivationConfig', ], 'HumanLoopConfig' => [ 'shape' => 'HumanLoopConfig', ], 'OutputConfig' => [ 'shape' => 'FlowDefinitionOutputConfig', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], ], ], 'DescribeHumanTaskUiRequest' => [ 'type' => 'structure', 'required' => [ 'HumanTaskUiName', ], 'members' => [ 'HumanTaskUiName' => [ 'shape' => 'HumanTaskUiName', ], ], ], 'DescribeHumanTaskUiResponse' => [ 'type' => 'structure', 'required' => [ 'HumanTaskUiArn', 'HumanTaskUiName', 'CreationTime', 'UiTemplate', ], 'members' => [ 'HumanTaskUiArn' => [ 'shape' => 'HumanTaskUiArn', ], 'HumanTaskUiName' => [ 'shape' => 'HumanTaskUiName', ], 'HumanTaskUiStatus' => [ 'shape' => 'HumanTaskUiStatus', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'UiTemplate' => [ 'shape' => 'UiTemplateInfo', ], ], ], 'DescribeHyperParameterTuningJobRequest' => [ 'type' => 'structure', 'required' => [ 'HyperParameterTuningJobName', ], 'members' => [ 'HyperParameterTuningJobName' => [ 'shape' => 'HyperParameterTuningJobName', ], ], ], 'DescribeHyperParameterTuningJobResponse' => [ 'type' => 'structure', 'required' => [ 'HyperParameterTuningJobName', 'HyperParameterTuningJobArn', 'HyperParameterTuningJobConfig', 'HyperParameterTuningJobStatus', 'CreationTime', 'TrainingJobStatusCounters', 'ObjectiveStatusCounters', ], 'members' => [ 'HyperParameterTuningJobName' => [ 'shape' => 'HyperParameterTuningJobName', ], 'HyperParameterTuningJobArn' => [ 'shape' => 'HyperParameterTuningJobArn', ], 'HyperParameterTuningJobConfig' => [ 'shape' => 'HyperParameterTuningJobConfig', ], 'TrainingJobDefinition' => [ 'shape' => 'HyperParameterTrainingJobDefinition', ], 'TrainingJobDefinitions' => [ 'shape' => 'HyperParameterTrainingJobDefinitions', ], 'HyperParameterTuningJobStatus' => [ 'shape' => 'HyperParameterTuningJobStatus', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'HyperParameterTuningEndTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'TrainingJobStatusCounters' => [ 'shape' => 'TrainingJobStatusCounters', ], 'ObjectiveStatusCounters' => [ 'shape' => 'ObjectiveStatusCounters', ], 'BestTrainingJob' => [ 'shape' => 'HyperParameterTrainingJobSummary', ], 'OverallBestTrainingJob' => [ 'shape' => 'HyperParameterTrainingJobSummary', ], 'WarmStartConfig' => [ 'shape' => 'HyperParameterTuningJobWarmStartConfig', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], ], ], 'DescribeImageRequest' => [ 'type' => 'structure', 'required' => [ 'ImageName', ], 'members' => [ 'ImageName' => [ 'shape' => 'ImageName', ], ], ], 'DescribeImageResponse' => [ 'type' => 'structure', 'members' => [ 'CreationTime' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'ImageDescription', ], 'DisplayName' => [ 'shape' => 'ImageDisplayName', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'ImageArn' => [ 'shape' => 'ImageArn', ], 'ImageName' => [ 'shape' => 'ImageName', ], 'ImageStatus' => [ 'shape' => 'ImageStatus', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], ], ], 'DescribeImageVersionRequest' => [ 'type' => 'structure', 'required' => [ 'ImageName', ], 'members' => [ 'ImageName' => [ 'shape' => 'ImageName', ], 'Version' => [ 'shape' => 'ImageVersionNumber', ], ], ], 'DescribeImageVersionResponse' => [ 'type' => 'structure', 'members' => [ 'BaseImage' => [ 'shape' => 'ImageBaseImage', ], 'ContainerImage' => [ 'shape' => 'ImageContainerImage', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'ImageArn' => [ 'shape' => 'ImageArn', ], 'ImageVersionArn' => [ 'shape' => 'ImageVersionArn', ], 'ImageVersionStatus' => [ 'shape' => 'ImageVersionStatus', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'Version' => [ 'shape' => 'ImageVersionNumber', ], ], ], 'DescribeLabelingJobRequest' => [ 'type' => 'structure', 'required' => [ 'LabelingJobName', ], 'members' => [ 'LabelingJobName' => [ 'shape' => 'LabelingJobName', ], ], ], 'DescribeLabelingJobResponse' => [ 'type' => 'structure', 'required' => [ 'LabelingJobStatus', 'LabelCounters', 'CreationTime', 'LastModifiedTime', 'JobReferenceCode', 'LabelingJobName', 'LabelingJobArn', 'InputConfig', 'OutputConfig', 'RoleArn', 'HumanTaskConfig', ], 'members' => [ 'LabelingJobStatus' => [ 'shape' => 'LabelingJobStatus', ], 'LabelCounters' => [ 'shape' => 'LabelCounters', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'JobReferenceCode' => [ 'shape' => 'JobReferenceCode', ], 'LabelingJobName' => [ 'shape' => 'LabelingJobName', ], 'LabelingJobArn' => [ 'shape' => 'LabelingJobArn', ], 'LabelAttributeName' => [ 'shape' => 'LabelAttributeName', ], 'InputConfig' => [ 'shape' => 'LabelingJobInputConfig', ], 'OutputConfig' => [ 'shape' => 'LabelingJobOutputConfig', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'LabelCategoryConfigS3Uri' => [ 'shape' => 'S3Uri', ], 'StoppingConditions' => [ 'shape' => 'LabelingJobStoppingConditions', ], 'LabelingJobAlgorithmsConfig' => [ 'shape' => 'LabelingJobAlgorithmsConfig', ], 'HumanTaskConfig' => [ 'shape' => 'HumanTaskConfig', ], 'Tags' => [ 'shape' => 'TagList', ], 'LabelingJobOutput' => [ 'shape' => 'LabelingJobOutput', ], ], ], 'DescribeModelBiasJobDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'JobDefinitionName', ], 'members' => [ 'JobDefinitionName' => [ 'shape' => 'MonitoringJobDefinitionName', ], ], ], 'DescribeModelBiasJobDefinitionResponse' => [ 'type' => 'structure', 'required' => [ 'JobDefinitionArn', 'JobDefinitionName', 'CreationTime', 'ModelBiasAppSpecification', 'ModelBiasJobInput', 'ModelBiasJobOutputConfig', 'JobResources', 'RoleArn', ], 'members' => [ 'JobDefinitionArn' => [ 'shape' => 'MonitoringJobDefinitionArn', ], 'JobDefinitionName' => [ 'shape' => 'MonitoringJobDefinitionName', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'ModelBiasBaselineConfig' => [ 'shape' => 'ModelBiasBaselineConfig', ], 'ModelBiasAppSpecification' => [ 'shape' => 'ModelBiasAppSpecification', ], 'ModelBiasJobInput' => [ 'shape' => 'ModelBiasJobInput', ], 'ModelBiasJobOutputConfig' => [ 'shape' => 'MonitoringOutputConfig', ], 'JobResources' => [ 'shape' => 'MonitoringResources', ], 'NetworkConfig' => [ 'shape' => 'MonitoringNetworkConfig', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'StoppingCondition' => [ 'shape' => 'MonitoringStoppingCondition', ], ], ], 'DescribeModelExplainabilityJobDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'JobDefinitionName', ], 'members' => [ 'JobDefinitionName' => [ 'shape' => 'MonitoringJobDefinitionName', ], ], ], 'DescribeModelExplainabilityJobDefinitionResponse' => [ 'type' => 'structure', 'required' => [ 'JobDefinitionArn', 'JobDefinitionName', 'CreationTime', 'ModelExplainabilityAppSpecification', 'ModelExplainabilityJobInput', 'ModelExplainabilityJobOutputConfig', 'JobResources', 'RoleArn', ], 'members' => [ 'JobDefinitionArn' => [ 'shape' => 'MonitoringJobDefinitionArn', ], 'JobDefinitionName' => [ 'shape' => 'MonitoringJobDefinitionName', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'ModelExplainabilityBaselineConfig' => [ 'shape' => 'ModelExplainabilityBaselineConfig', ], 'ModelExplainabilityAppSpecification' => [ 'shape' => 'ModelExplainabilityAppSpecification', ], 'ModelExplainabilityJobInput' => [ 'shape' => 'ModelExplainabilityJobInput', ], 'ModelExplainabilityJobOutputConfig' => [ 'shape' => 'MonitoringOutputConfig', ], 'JobResources' => [ 'shape' => 'MonitoringResources', ], 'NetworkConfig' => [ 'shape' => 'MonitoringNetworkConfig', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'StoppingCondition' => [ 'shape' => 'MonitoringStoppingCondition', ], ], ], 'DescribeModelInput' => [ 'type' => 'structure', 'required' => [ 'ModelName', ], 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], ], ], 'DescribeModelOutput' => [ 'type' => 'structure', 'required' => [ 'ModelName', 'ExecutionRoleArn', 'CreationTime', 'ModelArn', ], 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'PrimaryContainer' => [ 'shape' => 'ContainerDefinition', ], 'Containers' => [ 'shape' => 'ContainerDefinitionList', ], 'InferenceExecutionConfig' => [ 'shape' => 'InferenceExecutionConfig', ], 'ExecutionRoleArn' => [ 'shape' => 'RoleArn', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'ModelArn' => [ 'shape' => 'ModelArn', ], 'EnableNetworkIsolation' => [ 'shape' => 'Boolean', ], ], ], 'DescribeModelPackageGroupInput' => [ 'type' => 'structure', 'required' => [ 'ModelPackageGroupName', ], 'members' => [ 'ModelPackageGroupName' => [ 'shape' => 'ArnOrName', ], ], ], 'DescribeModelPackageGroupOutput' => [ 'type' => 'structure', 'required' => [ 'ModelPackageGroupName', 'ModelPackageGroupArn', 'CreationTime', 'CreatedBy', 'ModelPackageGroupStatus', ], 'members' => [ 'ModelPackageGroupName' => [ 'shape' => 'EntityName', ], 'ModelPackageGroupArn' => [ 'shape' => 'ModelPackageGroupArn', ], 'ModelPackageGroupDescription' => [ 'shape' => 'EntityDescription', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'CreatedBy' => [ 'shape' => 'UserContext', ], 'ModelPackageGroupStatus' => [ 'shape' => 'ModelPackageGroupStatus', ], ], ], 'DescribeModelPackageInput' => [ 'type' => 'structure', 'required' => [ 'ModelPackageName', ], 'members' => [ 'ModelPackageName' => [ 'shape' => 'VersionedArnOrName', ], ], ], 'DescribeModelPackageOutput' => [ 'type' => 'structure', 'required' => [ 'ModelPackageName', 'ModelPackageArn', 'CreationTime', 'ModelPackageStatus', 'ModelPackageStatusDetails', ], 'members' => [ 'ModelPackageName' => [ 'shape' => 'EntityName', ], 'ModelPackageGroupName' => [ 'shape' => 'EntityName', ], 'ModelPackageVersion' => [ 'shape' => 'ModelPackageVersion', ], 'ModelPackageArn' => [ 'shape' => 'ModelPackageArn', ], 'ModelPackageDescription' => [ 'shape' => 'EntityDescription', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'InferenceSpecification' => [ 'shape' => 'InferenceSpecification', ], 'SourceAlgorithmSpecification' => [ 'shape' => 'SourceAlgorithmSpecification', ], 'ValidationSpecification' => [ 'shape' => 'ModelPackageValidationSpecification', ], 'ModelPackageStatus' => [ 'shape' => 'ModelPackageStatus', ], 'ModelPackageStatusDetails' => [ 'shape' => 'ModelPackageStatusDetails', ], 'CertifyForMarketplace' => [ 'shape' => 'CertifyForMarketplace', ], 'ModelApprovalStatus' => [ 'shape' => 'ModelApprovalStatus', ], 'CreatedBy' => [ 'shape' => 'UserContext', ], 'MetadataProperties' => [ 'shape' => 'MetadataProperties', ], 'ModelMetrics' => [ 'shape' => 'ModelMetrics', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedBy' => [ 'shape' => 'UserContext', ], 'ApprovalDescription' => [ 'shape' => 'ApprovalDescription', ], ], ], 'DescribeModelQualityJobDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'JobDefinitionName', ], 'members' => [ 'JobDefinitionName' => [ 'shape' => 'MonitoringJobDefinitionName', ], ], ], 'DescribeModelQualityJobDefinitionResponse' => [ 'type' => 'structure', 'required' => [ 'JobDefinitionArn', 'JobDefinitionName', 'CreationTime', 'ModelQualityAppSpecification', 'ModelQualityJobInput', 'ModelQualityJobOutputConfig', 'JobResources', 'RoleArn', ], 'members' => [ 'JobDefinitionArn' => [ 'shape' => 'MonitoringJobDefinitionArn', ], 'JobDefinitionName' => [ 'shape' => 'MonitoringJobDefinitionName', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'ModelQualityBaselineConfig' => [ 'shape' => 'ModelQualityBaselineConfig', ], 'ModelQualityAppSpecification' => [ 'shape' => 'ModelQualityAppSpecification', ], 'ModelQualityJobInput' => [ 'shape' => 'ModelQualityJobInput', ], 'ModelQualityJobOutputConfig' => [ 'shape' => 'MonitoringOutputConfig', ], 'JobResources' => [ 'shape' => 'MonitoringResources', ], 'NetworkConfig' => [ 'shape' => 'MonitoringNetworkConfig', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'StoppingCondition' => [ 'shape' => 'MonitoringStoppingCondition', ], ], ], 'DescribeMonitoringScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'MonitoringScheduleName', ], 'members' => [ 'MonitoringScheduleName' => [ 'shape' => 'MonitoringScheduleName', ], ], ], 'DescribeMonitoringScheduleResponse' => [ 'type' => 'structure', 'required' => [ 'MonitoringScheduleArn', 'MonitoringScheduleName', 'MonitoringScheduleStatus', 'CreationTime', 'LastModifiedTime', 'MonitoringScheduleConfig', ], 'members' => [ 'MonitoringScheduleArn' => [ 'shape' => 'MonitoringScheduleArn', ], 'MonitoringScheduleName' => [ 'shape' => 'MonitoringScheduleName', ], 'MonitoringScheduleStatus' => [ 'shape' => 'ScheduleStatus', ], 'MonitoringType' => [ 'shape' => 'MonitoringType', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'MonitoringScheduleConfig' => [ 'shape' => 'MonitoringScheduleConfig', ], 'EndpointName' => [ 'shape' => 'EndpointName', ], 'LastMonitoringExecutionSummary' => [ 'shape' => 'MonitoringExecutionSummary', ], ], ], 'DescribeNotebookInstanceInput' => [ 'type' => 'structure', 'required' => [ 'NotebookInstanceName', ], 'members' => [ 'NotebookInstanceName' => [ 'shape' => 'NotebookInstanceName', ], ], ], 'DescribeNotebookInstanceLifecycleConfigInput' => [ 'type' => 'structure', 'required' => [ 'NotebookInstanceLifecycleConfigName', ], 'members' => [ 'NotebookInstanceLifecycleConfigName' => [ 'shape' => 'NotebookInstanceLifecycleConfigName', ], ], ], 'DescribeNotebookInstanceLifecycleConfigOutput' => [ 'type' => 'structure', 'members' => [ 'NotebookInstanceLifecycleConfigArn' => [ 'shape' => 'NotebookInstanceLifecycleConfigArn', ], 'NotebookInstanceLifecycleConfigName' => [ 'shape' => 'NotebookInstanceLifecycleConfigName', ], 'OnCreate' => [ 'shape' => 'NotebookInstanceLifecycleConfigList', ], 'OnStart' => [ 'shape' => 'NotebookInstanceLifecycleConfigList', ], 'LastModifiedTime' => [ 'shape' => 'LastModifiedTime', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], ], ], 'DescribeNotebookInstanceOutput' => [ 'type' => 'structure', 'members' => [ 'NotebookInstanceArn' => [ 'shape' => 'NotebookInstanceArn', ], 'NotebookInstanceName' => [ 'shape' => 'NotebookInstanceName', ], 'NotebookInstanceStatus' => [ 'shape' => 'NotebookInstanceStatus', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'Url' => [ 'shape' => 'NotebookInstanceUrl', ], 'InstanceType' => [ 'shape' => 'InstanceType', ], 'SubnetId' => [ 'shape' => 'SubnetId', ], 'SecurityGroups' => [ 'shape' => 'SecurityGroupIds', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'NetworkInterfaceId' => [ 'shape' => 'NetworkInterfaceId', ], 'LastModifiedTime' => [ 'shape' => 'LastModifiedTime', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'NotebookInstanceLifecycleConfigName' => [ 'shape' => 'NotebookInstanceLifecycleConfigName', ], 'DirectInternetAccess' => [ 'shape' => 'DirectInternetAccess', ], 'VolumeSizeInGB' => [ 'shape' => 'NotebookInstanceVolumeSizeInGB', ], 'AcceleratorTypes' => [ 'shape' => 'NotebookInstanceAcceleratorTypes', ], 'DefaultCodeRepository' => [ 'shape' => 'CodeRepositoryNameOrUrl', ], 'AdditionalCodeRepositories' => [ 'shape' => 'AdditionalCodeRepositoryNamesOrUrls', ], 'RootAccess' => [ 'shape' => 'RootAccess', ], ], ], 'DescribePipelineDefinitionForExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'PipelineExecutionArn', ], 'members' => [ 'PipelineExecutionArn' => [ 'shape' => 'PipelineExecutionArn', ], ], ], 'DescribePipelineDefinitionForExecutionResponse' => [ 'type' => 'structure', 'members' => [ 'PipelineDefinition' => [ 'shape' => 'PipelineDefinition', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribePipelineExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'PipelineExecutionArn', ], 'members' => [ 'PipelineExecutionArn' => [ 'shape' => 'PipelineExecutionArn', ], ], ], 'DescribePipelineExecutionResponse' => [ 'type' => 'structure', 'members' => [ 'PipelineArn' => [ 'shape' => 'PipelineArn', ], 'PipelineExecutionArn' => [ 'shape' => 'PipelineExecutionArn', ], 'PipelineExecutionDisplayName' => [ 'shape' => 'PipelineExecutionName', ], 'PipelineExecutionStatus' => [ 'shape' => 'PipelineExecutionStatus', ], 'PipelineExecutionDescription' => [ 'shape' => 'PipelineExecutionDescription', ], 'PipelineExperimentConfig' => [ 'shape' => 'PipelineExperimentConfig', ], 'FailureReason' => [ 'shape' => 'PipelineExecutionFailureReason', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'CreatedBy' => [ 'shape' => 'UserContext', ], 'LastModifiedBy' => [ 'shape' => 'UserContext', ], ], ], 'DescribePipelineRequest' => [ 'type' => 'structure', 'required' => [ 'PipelineName', ], 'members' => [ 'PipelineName' => [ 'shape' => 'PipelineName', ], ], ], 'DescribePipelineResponse' => [ 'type' => 'structure', 'members' => [ 'PipelineArn' => [ 'shape' => 'PipelineArn', ], 'PipelineName' => [ 'shape' => 'PipelineName', ], 'PipelineDisplayName' => [ 'shape' => 'PipelineName', ], 'PipelineDefinition' => [ 'shape' => 'PipelineDefinition', ], 'PipelineDescription' => [ 'shape' => 'PipelineDescription', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'PipelineStatus' => [ 'shape' => 'PipelineStatus', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastRunTime' => [ 'shape' => 'Timestamp', ], 'CreatedBy' => [ 'shape' => 'UserContext', ], 'LastModifiedBy' => [ 'shape' => 'UserContext', ], ], ], 'DescribeProcessingJobRequest' => [ 'type' => 'structure', 'required' => [ 'ProcessingJobName', ], 'members' => [ 'ProcessingJobName' => [ 'shape' => 'ProcessingJobName', ], ], ], 'DescribeProcessingJobResponse' => [ 'type' => 'structure', 'required' => [ 'ProcessingJobName', 'ProcessingResources', 'AppSpecification', 'ProcessingJobArn', 'ProcessingJobStatus', 'CreationTime', ], 'members' => [ 'ProcessingInputs' => [ 'shape' => 'ProcessingInputs', ], 'ProcessingOutputConfig' => [ 'shape' => 'ProcessingOutputConfig', ], 'ProcessingJobName' => [ 'shape' => 'ProcessingJobName', ], 'ProcessingResources' => [ 'shape' => 'ProcessingResources', ], 'StoppingCondition' => [ 'shape' => 'ProcessingStoppingCondition', ], 'AppSpecification' => [ 'shape' => 'AppSpecification', ], 'Environment' => [ 'shape' => 'ProcessingEnvironmentMap', ], 'NetworkConfig' => [ 'shape' => 'NetworkConfig', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'ExperimentConfig' => [ 'shape' => 'ExperimentConfig', ], 'ProcessingJobArn' => [ 'shape' => 'ProcessingJobArn', ], 'ProcessingJobStatus' => [ 'shape' => 'ProcessingJobStatus', ], 'ExitMessage' => [ 'shape' => 'ExitMessage', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'ProcessingEndTime' => [ 'shape' => 'Timestamp', ], 'ProcessingStartTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'MonitoringScheduleArn' => [ 'shape' => 'MonitoringScheduleArn', ], 'AutoMLJobArn' => [ 'shape' => 'AutoMLJobArn', ], 'TrainingJobArn' => [ 'shape' => 'TrainingJobArn', ], ], ], 'DescribeProjectInput' => [ 'type' => 'structure', 'required' => [ 'ProjectName', ], 'members' => [ 'ProjectName' => [ 'shape' => 'ProjectEntityName', ], ], ], 'DescribeProjectOutput' => [ 'type' => 'structure', 'required' => [ 'ProjectArn', 'ProjectName', 'ProjectId', 'ServiceCatalogProvisioningDetails', 'ProjectStatus', 'CreationTime', ], 'members' => [ 'ProjectArn' => [ 'shape' => 'ProjectArn', ], 'ProjectName' => [ 'shape' => 'ProjectEntityName', ], 'ProjectId' => [ 'shape' => 'ProjectId', ], 'ProjectDescription' => [ 'shape' => 'EntityDescription', ], 'ServiceCatalogProvisioningDetails' => [ 'shape' => 'ServiceCatalogProvisioningDetails', ], 'ServiceCatalogProvisionedProductDetails' => [ 'shape' => 'ServiceCatalogProvisionedProductDetails', ], 'ProjectStatus' => [ 'shape' => 'ProjectStatus', ], 'CreatedBy' => [ 'shape' => 'UserContext', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeSubscribedWorkteamRequest' => [ 'type' => 'structure', 'required' => [ 'WorkteamArn', ], 'members' => [ 'WorkteamArn' => [ 'shape' => 'WorkteamArn', ], ], ], 'DescribeSubscribedWorkteamResponse' => [ 'type' => 'structure', 'required' => [ 'SubscribedWorkteam', ], 'members' => [ 'SubscribedWorkteam' => [ 'shape' => 'SubscribedWorkteam', ], ], ], 'DescribeTrainingJobRequest' => [ 'type' => 'structure', 'required' => [ 'TrainingJobName', ], 'members' => [ 'TrainingJobName' => [ 'shape' => 'TrainingJobName', ], ], ], 'DescribeTrainingJobResponse' => [ 'type' => 'structure', 'required' => [ 'TrainingJobName', 'TrainingJobArn', 'ModelArtifacts', 'TrainingJobStatus', 'SecondaryStatus', 'AlgorithmSpecification', 'ResourceConfig', 'StoppingCondition', 'CreationTime', ], 'members' => [ 'TrainingJobName' => [ 'shape' => 'TrainingJobName', ], 'TrainingJobArn' => [ 'shape' => 'TrainingJobArn', ], 'TuningJobArn' => [ 'shape' => 'HyperParameterTuningJobArn', ], 'LabelingJobArn' => [ 'shape' => 'LabelingJobArn', ], 'AutoMLJobArn' => [ 'shape' => 'AutoMLJobArn', ], 'ModelArtifacts' => [ 'shape' => 'ModelArtifacts', ], 'TrainingJobStatus' => [ 'shape' => 'TrainingJobStatus', ], 'SecondaryStatus' => [ 'shape' => 'SecondaryStatus', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'HyperParameters' => [ 'shape' => 'HyperParameters', ], 'AlgorithmSpecification' => [ 'shape' => 'AlgorithmSpecification', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'OutputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'ResourceConfig' => [ 'shape' => 'ResourceConfig', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'StoppingCondition' => [ 'shape' => 'StoppingCondition', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'TrainingStartTime' => [ 'shape' => 'Timestamp', ], 'TrainingEndTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'SecondaryStatusTransitions' => [ 'shape' => 'SecondaryStatusTransitions', ], 'FinalMetricDataList' => [ 'shape' => 'FinalMetricDataList', ], 'EnableNetworkIsolation' => [ 'shape' => 'Boolean', ], 'EnableInterContainerTrafficEncryption' => [ 'shape' => 'Boolean', ], 'EnableManagedSpotTraining' => [ 'shape' => 'Boolean', ], 'CheckpointConfig' => [ 'shape' => 'CheckpointConfig', ], 'TrainingTimeInSeconds' => [ 'shape' => 'TrainingTimeInSeconds', ], 'BillableTimeInSeconds' => [ 'shape' => 'BillableTimeInSeconds', ], 'DebugHookConfig' => [ 'shape' => 'DebugHookConfig', ], 'ExperimentConfig' => [ 'shape' => 'ExperimentConfig', ], 'DebugRuleConfigurations' => [ 'shape' => 'DebugRuleConfigurations', ], 'TensorBoardOutputConfig' => [ 'shape' => 'TensorBoardOutputConfig', ], 'DebugRuleEvaluationStatuses' => [ 'shape' => 'DebugRuleEvaluationStatuses', ], 'ProfilerConfig' => [ 'shape' => 'ProfilerConfig', ], 'ProfilerRuleConfigurations' => [ 'shape' => 'ProfilerRuleConfigurations', ], 'ProfilerRuleEvaluationStatuses' => [ 'shape' => 'ProfilerRuleEvaluationStatuses', ], 'ProfilingStatus' => [ 'shape' => 'ProfilingStatus', ], 'RetryStrategy' => [ 'shape' => 'RetryStrategy', ], 'Environment' => [ 'shape' => 'TrainingEnvironmentMap', ], ], ], 'DescribeTransformJobRequest' => [ 'type' => 'structure', 'required' => [ 'TransformJobName', ], 'members' => [ 'TransformJobName' => [ 'shape' => 'TransformJobName', ], ], ], 'DescribeTransformJobResponse' => [ 'type' => 'structure', 'required' => [ 'TransformJobName', 'TransformJobArn', 'TransformJobStatus', 'ModelName', 'TransformInput', 'TransformResources', 'CreationTime', ], 'members' => [ 'TransformJobName' => [ 'shape' => 'TransformJobName', ], 'TransformJobArn' => [ 'shape' => 'TransformJobArn', ], 'TransformJobStatus' => [ 'shape' => 'TransformJobStatus', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'ModelName' => [ 'shape' => 'ModelName', ], 'MaxConcurrentTransforms' => [ 'shape' => 'MaxConcurrentTransforms', ], 'ModelClientConfig' => [ 'shape' => 'ModelClientConfig', ], 'MaxPayloadInMB' => [ 'shape' => 'MaxPayloadInMB', ], 'BatchStrategy' => [ 'shape' => 'BatchStrategy', ], 'Environment' => [ 'shape' => 'TransformEnvironmentMap', ], 'TransformInput' => [ 'shape' => 'TransformInput', ], 'TransformOutput' => [ 'shape' => 'TransformOutput', ], 'TransformResources' => [ 'shape' => 'TransformResources', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'TransformStartTime' => [ 'shape' => 'Timestamp', ], 'TransformEndTime' => [ 'shape' => 'Timestamp', ], 'LabelingJobArn' => [ 'shape' => 'LabelingJobArn', ], 'AutoMLJobArn' => [ 'shape' => 'AutoMLJobArn', ], 'DataProcessing' => [ 'shape' => 'DataProcessing', ], 'ExperimentConfig' => [ 'shape' => 'ExperimentConfig', ], ], ], 'DescribeTrialComponentRequest' => [ 'type' => 'structure', 'required' => [ 'TrialComponentName', ], 'members' => [ 'TrialComponentName' => [ 'shape' => 'ExperimentEntityName', ], ], ], 'DescribeTrialComponentResponse' => [ 'type' => 'structure', 'members' => [ 'TrialComponentName' => [ 'shape' => 'ExperimentEntityName', ], 'TrialComponentArn' => [ 'shape' => 'TrialComponentArn', ], 'DisplayName' => [ 'shape' => 'ExperimentEntityName', ], 'Source' => [ 'shape' => 'TrialComponentSource', ], 'Status' => [ 'shape' => 'TrialComponentStatus', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'CreatedBy' => [ 'shape' => 'UserContext', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedBy' => [ 'shape' => 'UserContext', ], 'Parameters' => [ 'shape' => 'TrialComponentParameters', ], 'InputArtifacts' => [ 'shape' => 'TrialComponentArtifacts', ], 'OutputArtifacts' => [ 'shape' => 'TrialComponentArtifacts', ], 'MetadataProperties' => [ 'shape' => 'MetadataProperties', ], 'Metrics' => [ 'shape' => 'TrialComponentMetricSummaries', ], ], ], 'DescribeTrialRequest' => [ 'type' => 'structure', 'required' => [ 'TrialName', ], 'members' => [ 'TrialName' => [ 'shape' => 'ExperimentEntityName', ], ], ], 'DescribeTrialResponse' => [ 'type' => 'structure', 'members' => [ 'TrialName' => [ 'shape' => 'ExperimentEntityName', ], 'TrialArn' => [ 'shape' => 'TrialArn', ], 'DisplayName' => [ 'shape' => 'ExperimentEntityName', ], 'ExperimentName' => [ 'shape' => 'ExperimentEntityName', ], 'Source' => [ 'shape' => 'TrialSource', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'CreatedBy' => [ 'shape' => 'UserContext', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedBy' => [ 'shape' => 'UserContext', ], 'MetadataProperties' => [ 'shape' => 'MetadataProperties', ], ], ], 'DescribeUserProfileRequest' => [ 'type' => 'structure', 'required' => [ 'DomainId', 'UserProfileName', ], 'members' => [ 'DomainId' => [ 'shape' => 'DomainId', ], 'UserProfileName' => [ 'shape' => 'UserProfileName', ], ], ], 'DescribeUserProfileResponse' => [ 'type' => 'structure', 'members' => [ 'DomainId' => [ 'shape' => 'DomainId', ], 'UserProfileArn' => [ 'shape' => 'UserProfileArn', ], 'UserProfileName' => [ 'shape' => 'UserProfileName', ], 'HomeEfsFileSystemUid' => [ 'shape' => 'EfsUid', ], 'Status' => [ 'shape' => 'UserProfileStatus', ], 'LastModifiedTime' => [ 'shape' => 'LastModifiedTime', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'SingleSignOnUserIdentifier' => [ 'shape' => 'SingleSignOnUserIdentifier', ], 'SingleSignOnUserValue' => [ 'shape' => 'String256', ], 'UserSettings' => [ 'shape' => 'UserSettings', ], ], ], 'DescribeWorkforceRequest' => [ 'type' => 'structure', 'required' => [ 'WorkforceName', ], 'members' => [ 'WorkforceName' => [ 'shape' => 'WorkforceName', ], ], ], 'DescribeWorkforceResponse' => [ 'type' => 'structure', 'required' => [ 'Workforce', ], 'members' => [ 'Workforce' => [ 'shape' => 'Workforce', ], ], ], 'DescribeWorkteamRequest' => [ 'type' => 'structure', 'required' => [ 'WorkteamName', ], 'members' => [ 'WorkteamName' => [ 'shape' => 'WorkteamName', ], ], ], 'DescribeWorkteamResponse' => [ 'type' => 'structure', 'required' => [ 'Workteam', ], 'members' => [ 'Workteam' => [ 'shape' => 'Workteam', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 128, ], 'DesiredWeightAndCapacity' => [ 'type' => 'structure', 'required' => [ 'VariantName', ], 'members' => [ 'VariantName' => [ 'shape' => 'VariantName', ], 'DesiredWeight' => [ 'shape' => 'VariantWeight', ], 'DesiredInstanceCount' => [ 'shape' => 'TaskCount', ], ], ], 'DesiredWeightAndCapacityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DesiredWeightAndCapacity', ], 'min' => 1, ], 'DestinationS3Uri' => [ 'type' => 'string', 'max' => 512, 'pattern' => '^(https|s3)://([^/])/?(.*)$', ], 'DetailedAlgorithmStatus' => [ 'type' => 'string', 'enum' => [ 'NotStarted', 'InProgress', 'Completed', 'Failed', ], ], 'DetailedModelPackageStatus' => [ 'type' => 'string', 'enum' => [ 'NotStarted', 'InProgress', 'Completed', 'Failed', ], ], 'Device' => [ 'type' => 'structure', 'required' => [ 'DeviceName', ], 'members' => [ 'DeviceName' => [ 'shape' => 'DeviceName', ], 'Description' => [ 'shape' => 'DeviceDescription', ], 'IotThingName' => [ 'shape' => 'ThingName', ], ], ], 'DeviceArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:aws[a-z\\-]*:[a-z\\-]*:[a-z\\-]*:\\d{12}:[a-z\\-]*/?[a-zA-Z_0-9+=,.@\\-_/]+$', ], 'DeviceDescription' => [ 'type' => 'string', 'max' => 40, 'min' => 1, 'pattern' => '[\\S\\s]+', ], 'DeviceFleetArn' => [ 'type' => 'string', 'pattern' => '^arn:aws[a-z\\-]*:iam::\\d{12}:device-fleet/?[a-zA-Z_0-9+=,.@\\-_/]+$', ], 'DeviceFleetDescription' => [ 'type' => 'string', 'max' => 800, 'min' => 1, 'pattern' => '[\\S\\s]+', ], 'DeviceFleetSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeviceFleetSummary', ], ], 'DeviceFleetSummary' => [ 'type' => 'structure', 'required' => [ 'DeviceFleetArn', 'DeviceFleetName', ], 'members' => [ 'DeviceFleetArn' => [ 'shape' => 'DeviceFleetArn', ], 'DeviceFleetName' => [ 'shape' => 'EntityName', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'DeviceName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,62}$', ], 'DeviceNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeviceName', ], ], 'DeviceStats' => [ 'type' => 'structure', 'required' => [ 'ConnectedDeviceCount', 'RegisteredDeviceCount', ], 'members' => [ 'ConnectedDeviceCount' => [ 'shape' => 'Long', ], 'RegisteredDeviceCount' => [ 'shape' => 'Long', ], ], ], 'DeviceSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeviceSummary', ], ], 'DeviceSummary' => [ 'type' => 'structure', 'required' => [ 'DeviceName', 'DeviceArn', ], 'members' => [ 'DeviceName' => [ 'shape' => 'EntityName', ], 'DeviceArn' => [ 'shape' => 'DeviceArn', ], 'Description' => [ 'shape' => 'DeviceDescription', ], 'DeviceFleetName' => [ 'shape' => 'EntityName', ], 'IotThingName' => [ 'shape' => 'ThingName', ], 'RegistrationTime' => [ 'shape' => 'Timestamp', ], 'LatestHeartbeat' => [ 'shape' => 'Timestamp', ], 'Models' => [ 'shape' => 'EdgeModelSummaries', ], ], ], 'Devices' => [ 'type' => 'list', 'member' => [ 'shape' => 'Device', ], ], 'DirectInternetAccess' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'DirectoryPath' => [ 'type' => 'string', 'max' => 4096, 'pattern' => '.*', ], 'DisableProfiler' => [ 'type' => 'boolean', ], 'DisableSagemakerServicecatalogPortfolioInput' => [ 'type' => 'structure', 'members' => [], ], 'DisableSagemakerServicecatalogPortfolioOutput' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateAdditionalCodeRepositories' => [ 'type' => 'boolean', ], 'DisassociateDefaultCodeRepository' => [ 'type' => 'boolean', ], 'DisassociateNotebookInstanceAcceleratorTypes' => [ 'type' => 'boolean', ], 'DisassociateNotebookInstanceLifecycleConfig' => [ 'type' => 'boolean', ], 'DisassociateTrialComponentRequest' => [ 'type' => 'structure', 'required' => [ 'TrialComponentName', 'TrialName', ], 'members' => [ 'TrialComponentName' => [ 'shape' => 'ExperimentEntityName', ], 'TrialName' => [ 'shape' => 'ExperimentEntityName', ], ], ], 'DisassociateTrialComponentResponse' => [ 'type' => 'structure', 'members' => [ 'TrialComponentArn' => [ 'shape' => 'TrialComponentArn', ], 'TrialArn' => [ 'shape' => 'TrialArn', ], ], ], 'Dollars' => [ 'type' => 'integer', 'max' => 2, 'min' => 0, ], 'DomainArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:domain/.*', ], 'DomainDetails' => [ 'type' => 'structure', 'members' => [ 'DomainArn' => [ 'shape' => 'DomainArn', ], 'DomainId' => [ 'shape' => 'DomainId', ], 'DomainName' => [ 'shape' => 'DomainName', ], 'Status' => [ 'shape' => 'DomainStatus', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'LastModifiedTime' => [ 'shape' => 'LastModifiedTime', ], 'Url' => [ 'shape' => 'String1024', ], ], ], 'DomainId' => [ 'type' => 'string', 'max' => 63, ], 'DomainList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainDetails', ], ], 'DomainName' => [ 'type' => 'string', 'max' => 63, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,62}', ], 'DomainStatus' => [ 'type' => 'string', 'enum' => [ 'Deleting', 'Failed', 'InService', 'Pending', 'Updating', 'Update_Failed', 'Delete_Failed', ], ], 'DoubleParameterValue' => [ 'type' => 'double', ], 'EdgeModel' => [ 'type' => 'structure', 'required' => [ 'ModelName', 'ModelVersion', ], 'members' => [ 'ModelName' => [ 'shape' => 'EntityName', ], 'ModelVersion' => [ 'shape' => 'EdgeVersion', ], 'LatestSampleTime' => [ 'shape' => 'Timestamp', ], 'LatestInference' => [ 'shape' => 'Timestamp', ], ], ], 'EdgeModelStat' => [ 'type' => 'structure', 'required' => [ 'ModelName', 'ModelVersion', 'OfflineDeviceCount', 'ConnectedDeviceCount', 'ActiveDeviceCount', 'SamplingDeviceCount', ], 'members' => [ 'ModelName' => [ 'shape' => 'EntityName', ], 'ModelVersion' => [ 'shape' => 'EdgeVersion', ], 'OfflineDeviceCount' => [ 'shape' => 'Long', ], 'ConnectedDeviceCount' => [ 'shape' => 'Long', ], 'ActiveDeviceCount' => [ 'shape' => 'Long', ], 'SamplingDeviceCount' => [ 'shape' => 'Long', ], ], ], 'EdgeModelStats' => [ 'type' => 'list', 'member' => [ 'shape' => 'EdgeModelStat', ], ], 'EdgeModelSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'EdgeModelSummary', ], ], 'EdgeModelSummary' => [ 'type' => 'structure', 'required' => [ 'ModelName', 'ModelVersion', ], 'members' => [ 'ModelName' => [ 'shape' => 'EntityName', ], 'ModelVersion' => [ 'shape' => 'EdgeVersion', ], ], ], 'EdgeModels' => [ 'type' => 'list', 'member' => [ 'shape' => 'EdgeModel', ], ], 'EdgeOutputConfig' => [ 'type' => 'structure', 'required' => [ 'S3OutputLocation', ], 'members' => [ 'S3OutputLocation' => [ 'shape' => 'S3Uri', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'PresetDeploymentType' => [ 'shape' => 'EdgePresetDeploymentType', ], 'PresetDeploymentConfig' => [ 'shape' => 'String', ], ], ], 'EdgePackagingJobArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:aws[a-z\\-]*:sagemaker:[a-z\\-]*:\\d{12}:edge-packaging-job/?[a-zA-Z_0-9+=,.@\\-_/]+$', ], 'EdgePackagingJobStatus' => [ 'type' => 'string', 'enum' => [ 'STARTING', 'INPROGRESS', 'COMPLETED', 'FAILED', 'STOPPING', 'STOPPED', ], ], 'EdgePackagingJobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'EdgePackagingJobSummary', ], ], 'EdgePackagingJobSummary' => [ 'type' => 'structure', 'required' => [ 'EdgePackagingJobArn', 'EdgePackagingJobName', 'EdgePackagingJobStatus', ], 'members' => [ 'EdgePackagingJobArn' => [ 'shape' => 'EdgePackagingJobArn', ], 'EdgePackagingJobName' => [ 'shape' => 'EntityName', ], 'EdgePackagingJobStatus' => [ 'shape' => 'EdgePackagingJobStatus', ], 'CompilationJobName' => [ 'shape' => 'EntityName', ], 'ModelName' => [ 'shape' => 'EntityName', ], 'ModelVersion' => [ 'shape' => 'EdgeVersion', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'EdgePresetDeploymentArtifact' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, ], 'EdgePresetDeploymentOutput' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Type' => [ 'shape' => 'EdgePresetDeploymentType', ], 'Artifact' => [ 'shape' => 'EdgePresetDeploymentArtifact', ], 'Status' => [ 'shape' => 'EdgePresetDeploymentStatus', ], 'StatusMessage' => [ 'shape' => 'String', ], ], ], 'EdgePresetDeploymentStatus' => [ 'type' => 'string', 'enum' => [ 'COMPLETED', 'FAILED', ], ], 'EdgePresetDeploymentType' => [ 'type' => 'string', 'enum' => [ 'GreengrassV2Component', ], ], 'EdgeVersion' => [ 'type' => 'string', 'max' => 30, 'min' => 1, 'pattern' => '[a-zA-Z0-9\\ \\_\\.]+', ], 'EfsUid' => [ 'type' => 'string', 'max' => 10, 'pattern' => '\\d+', ], 'EnableCapture' => [ 'type' => 'boolean', ], 'EnableIotRoleAlias' => [ 'type' => 'boolean', ], 'EnableSagemakerServicecatalogPortfolioInput' => [ 'type' => 'structure', 'members' => [], ], 'EnableSagemakerServicecatalogPortfolioOutput' => [ 'type' => 'structure', 'members' => [], ], 'Endpoint' => [ 'type' => 'structure', 'required' => [ 'EndpointName', 'EndpointArn', 'EndpointConfigName', 'EndpointStatus', 'CreationTime', 'LastModifiedTime', ], 'members' => [ 'EndpointName' => [ 'shape' => 'EndpointName', ], 'EndpointArn' => [ 'shape' => 'EndpointArn', ], 'EndpointConfigName' => [ 'shape' => 'EndpointConfigName', ], 'ProductionVariants' => [ 'shape' => 'ProductionVariantSummaryList', ], 'DataCaptureConfig' => [ 'shape' => 'DataCaptureConfigSummary', ], 'EndpointStatus' => [ 'shape' => 'EndpointStatus', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'MonitoringSchedules' => [ 'shape' => 'MonitoringScheduleList', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'EndpointArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:endpoint/.*', ], 'EndpointConfigArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:endpoint-config/.*', ], 'EndpointConfigName' => [ 'type' => 'string', 'max' => 63, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,62}', ], 'EndpointConfigNameContains' => [ 'type' => 'string', 'max' => 63, 'pattern' => '[a-zA-Z0-9-]+', ], 'EndpointConfigSortKey' => [ 'type' => 'string', 'enum' => [ 'Name', 'CreationTime', ], ], 'EndpointConfigSummary' => [ 'type' => 'structure', 'required' => [ 'EndpointConfigName', 'EndpointConfigArn', 'CreationTime', ], 'members' => [ 'EndpointConfigName' => [ 'shape' => 'EndpointConfigName', ], 'EndpointConfigArn' => [ 'shape' => 'EndpointConfigArn', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], ], ], 'EndpointConfigSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EndpointConfigSummary', ], ], 'EndpointInput' => [ 'type' => 'structure', 'required' => [ 'EndpointName', 'LocalPath', ], 'members' => [ 'EndpointName' => [ 'shape' => 'EndpointName', ], 'LocalPath' => [ 'shape' => 'ProcessingLocalPath', ], 'S3InputMode' => [ 'shape' => 'ProcessingS3InputMode', ], 'S3DataDistributionType' => [ 'shape' => 'ProcessingS3DataDistributionType', ], 'FeaturesAttribute' => [ 'shape' => 'String', ], 'InferenceAttribute' => [ 'shape' => 'String', ], 'ProbabilityAttribute' => [ 'shape' => 'String', ], 'ProbabilityThresholdAttribute' => [ 'shape' => 'ProbabilityThresholdAttribute', ], 'StartTimeOffset' => [ 'shape' => 'MonitoringTimeOffsetString', ], 'EndTimeOffset' => [ 'shape' => 'MonitoringTimeOffsetString', ], ], ], 'EndpointName' => [ 'type' => 'string', 'max' => 63, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,62}', ], 'EndpointNameContains' => [ 'type' => 'string', 'max' => 63, 'pattern' => '[a-zA-Z0-9-]+', ], 'EndpointSortKey' => [ 'type' => 'string', 'enum' => [ 'Name', 'CreationTime', 'Status', ], ], 'EndpointStatus' => [ 'type' => 'string', 'enum' => [ 'OutOfService', 'Creating', 'Updating', 'SystemUpdating', 'RollingBack', 'InService', 'Deleting', 'Failed', ], ], 'EndpointSummary' => [ 'type' => 'structure', 'required' => [ 'EndpointName', 'EndpointArn', 'CreationTime', 'LastModifiedTime', 'EndpointStatus', ], 'members' => [ 'EndpointName' => [ 'shape' => 'EndpointName', ], 'EndpointArn' => [ 'shape' => 'EndpointArn', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'EndpointStatus' => [ 'shape' => 'EndpointStatus', ], ], ], 'EndpointSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EndpointSummary', ], ], 'EntityDescription' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '[\\p{L}\\p{M}\\p{Z}\\p{S}\\p{N}\\p{P}]*', ], 'EntityName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,62}$', ], 'EnvironmentKey' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '[a-zA-Z_][a-zA-Z0-9_]*', ], 'EnvironmentMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'EnvironmentKey', ], 'value' => [ 'shape' => 'EnvironmentValue', ], 'max' => 16, ], 'EnvironmentValue' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '[\\S\\s]*', ], 'ExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'Completed', 'CompletedWithViolations', 'InProgress', 'Failed', 'Stopping', 'Stopped', ], ], 'ExitMessage' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '[\\S\\s]*', ], 'Experiment' => [ 'type' => 'structure', 'members' => [ 'ExperimentName' => [ 'shape' => 'ExperimentEntityName', ], 'ExperimentArn' => [ 'shape' => 'ExperimentArn', ], 'DisplayName' => [ 'shape' => 'ExperimentEntityName', ], 'Source' => [ 'shape' => 'ExperimentSource', ], 'Description' => [ 'shape' => 'ExperimentDescription', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'CreatedBy' => [ 'shape' => 'UserContext', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedBy' => [ 'shape' => 'UserContext', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ExperimentArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:experiment/.*', ], 'ExperimentConfig' => [ 'type' => 'structure', 'members' => [ 'ExperimentName' => [ 'shape' => 'ExperimentEntityName', ], 'TrialName' => [ 'shape' => 'ExperimentEntityName', ], 'TrialComponentDisplayName' => [ 'shape' => 'ExperimentEntityName', ], ], ], 'ExperimentDescription' => [ 'type' => 'string', 'max' => 3072, 'pattern' => '.*', ], 'ExperimentEntityName' => [ 'type' => 'string', 'max' => 120, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,119}', ], 'ExperimentSource' => [ 'type' => 'structure', 'required' => [ 'SourceArn', ], 'members' => [ 'SourceArn' => [ 'shape' => 'ExperimentSourceArn', ], 'SourceType' => [ 'shape' => 'SourceType', ], ], ], 'ExperimentSourceArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:.*', ], 'ExperimentSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentSummary', ], ], 'ExperimentSummary' => [ 'type' => 'structure', 'members' => [ 'ExperimentArn' => [ 'shape' => 'ExperimentArn', ], 'ExperimentName' => [ 'shape' => 'ExperimentEntityName', ], 'DisplayName' => [ 'shape' => 'ExperimentEntityName', ], 'ExperimentSource' => [ 'shape' => 'ExperimentSource', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'ExpiresInSeconds' => [ 'type' => 'integer', 'max' => 300, 'min' => 5, ], 'Explainability' => [ 'type' => 'structure', 'members' => [ 'Report' => [ 'shape' => 'MetricsSource', ], ], ], 'ExplainabilityLocation' => [ 'type' => 'string', 'min' => 1, ], 'FailureReason' => [ 'type' => 'string', 'max' => 1024, ], 'FeatureDefinition' => [ 'type' => 'structure', 'members' => [ 'FeatureName' => [ 'shape' => 'FeatureName', ], 'FeatureType' => [ 'shape' => 'FeatureType', ], ], ], 'FeatureDefinitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'FeatureDefinition', ], 'max' => 2500, 'min' => 1, ], 'FeatureGroup' => [ 'type' => 'structure', 'members' => [ 'FeatureGroupArn' => [ 'shape' => 'FeatureGroupArn', ], 'FeatureGroupName' => [ 'shape' => 'FeatureGroupName', ], 'RecordIdentifierFeatureName' => [ 'shape' => 'FeatureName', ], 'EventTimeFeatureName' => [ 'shape' => 'FeatureName', ], 'FeatureDefinitions' => [ 'shape' => 'FeatureDefinitions', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'OnlineStoreConfig' => [ 'shape' => 'OnlineStoreConfig', ], 'OfflineStoreConfig' => [ 'shape' => 'OfflineStoreConfig', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'FeatureGroupStatus' => [ 'shape' => 'FeatureGroupStatus', ], 'OfflineStoreStatus' => [ 'shape' => 'OfflineStoreStatus', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'Description' => [ 'shape' => 'Description', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'FeatureGroupArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:feature-group/.*', ], 'FeatureGroupMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'FeatureGroupName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,63}', ], 'FeatureGroupNameContains' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'FeatureGroupSortBy' => [ 'type' => 'string', 'enum' => [ 'Name', 'FeatureGroupStatus', 'OfflineStoreStatus', 'CreationTime', ], ], 'FeatureGroupSortOrder' => [ 'type' => 'string', 'enum' => [ 'Ascending', 'Descending', ], ], 'FeatureGroupStatus' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Created', 'CreateFailed', 'Deleting', 'DeleteFailed', ], ], 'FeatureGroupSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'FeatureGroupSummary', ], ], 'FeatureGroupSummary' => [ 'type' => 'structure', 'required' => [ 'FeatureGroupName', 'FeatureGroupArn', 'CreationTime', ], 'members' => [ 'FeatureGroupName' => [ 'shape' => 'FeatureGroupName', ], 'FeatureGroupArn' => [ 'shape' => 'FeatureGroupArn', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'FeatureGroupStatus' => [ 'shape' => 'FeatureGroupStatus', ], 'OfflineStoreStatus' => [ 'shape' => 'OfflineStoreStatus', ], ], ], 'FeatureName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9]([-_]*[a-zA-Z0-9]){0,63}', ], 'FeatureType' => [ 'type' => 'string', 'enum' => [ 'Integral', 'Fractional', 'String', ], ], 'FileSystemAccessMode' => [ 'type' => 'string', 'enum' => [ 'rw', 'ro', ], ], 'FileSystemConfig' => [ 'type' => 'structure', 'members' => [ 'MountPath' => [ 'shape' => 'MountPath', ], 'DefaultUid' => [ 'shape' => 'DefaultUid', 'box' => true, ], 'DefaultGid' => [ 'shape' => 'DefaultGid', 'box' => true, ], ], ], 'FileSystemDataSource' => [ 'type' => 'structure', 'required' => [ 'FileSystemId', 'FileSystemAccessMode', 'FileSystemType', 'DirectoryPath', ], 'members' => [ 'FileSystemId' => [ 'shape' => 'FileSystemId', ], 'FileSystemAccessMode' => [ 'shape' => 'FileSystemAccessMode', ], 'FileSystemType' => [ 'shape' => 'FileSystemType', ], 'DirectoryPath' => [ 'shape' => 'DirectoryPath', ], ], ], 'FileSystemId' => [ 'type' => 'string', 'min' => 11, 'pattern' => '.*', ], 'FileSystemType' => [ 'type' => 'string', 'enum' => [ 'EFS', 'FSxLustre', ], ], 'Filter' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'ResourcePropertyName', ], 'Operator' => [ 'shape' => 'Operator', ], 'Value' => [ 'shape' => 'FilterValue', ], ], ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], 'max' => 20, 'min' => 1, ], 'FilterValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '.+', ], 'FinalAutoMLJobObjectiveMetric' => [ 'type' => 'structure', 'required' => [ 'MetricName', 'Value', ], 'members' => [ 'Type' => [ 'shape' => 'AutoMLJobObjectiveType', ], 'MetricName' => [ 'shape' => 'AutoMLMetricEnum', ], 'Value' => [ 'shape' => 'MetricValue', ], ], ], 'FinalHyperParameterTuningJobObjectiveMetric' => [ 'type' => 'structure', 'required' => [ 'MetricName', 'Value', ], 'members' => [ 'Type' => [ 'shape' => 'HyperParameterTuningJobObjectiveType', ], 'MetricName' => [ 'shape' => 'MetricName', ], 'Value' => [ 'shape' => 'MetricValue', ], ], ], 'FinalMetricDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricData', ], 'max' => 40, 'min' => 0, ], 'Float' => [ 'type' => 'float', ], 'FlowDefinitionArn' => [ 'type' => 'string', 'max' => 1024, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:flow-definition/.*', ], 'FlowDefinitionName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-z0-9](-*[a-z0-9]){0,62}', ], 'FlowDefinitionOutputConfig' => [ 'type' => 'structure', 'required' => [ 'S3OutputPath', ], 'members' => [ 'S3OutputPath' => [ 'shape' => 'S3Uri', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'FlowDefinitionStatus' => [ 'type' => 'string', 'enum' => [ 'Initializing', 'Active', 'Failed', 'Deleting', ], ], 'FlowDefinitionSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'FlowDefinitionSummary', ], ], 'FlowDefinitionSummary' => [ 'type' => 'structure', 'required' => [ 'FlowDefinitionName', 'FlowDefinitionArn', 'FlowDefinitionStatus', 'CreationTime', ], 'members' => [ 'FlowDefinitionName' => [ 'shape' => 'FlowDefinitionName', ], 'FlowDefinitionArn' => [ 'shape' => 'FlowDefinitionArn', ], 'FlowDefinitionStatus' => [ 'shape' => 'FlowDefinitionStatus', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], ], ], 'FlowDefinitionTaskAvailabilityLifetimeInSeconds' => [ 'type' => 'integer', 'max' => 864000, 'min' => 1, ], 'FlowDefinitionTaskCount' => [ 'type' => 'integer', 'max' => 3, 'min' => 1, ], 'FlowDefinitionTaskDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '.+', ], 'FlowDefinitionTaskKeyword' => [ 'type' => 'string', 'max' => 30, 'min' => 1, 'pattern' => '^[A-Za-z0-9]+( [A-Za-z0-9]+)*$', ], 'FlowDefinitionTaskKeywords' => [ 'type' => 'list', 'member' => [ 'shape' => 'FlowDefinitionTaskKeyword', ], 'max' => 5, 'min' => 1, ], 'FlowDefinitionTaskTimeLimitInSeconds' => [ 'type' => 'integer', 'max' => 28800, 'min' => 30, ], 'FlowDefinitionTaskTitle' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[\\t\\n\\r -\\uD7FF\\uE000-\\uFFFD]*$', ], 'Framework' => [ 'type' => 'string', 'enum' => [ 'TENSORFLOW', 'KERAS', 'MXNET', 'ONNX', 'PYTORCH', 'XGBOOST', 'TFLITE', 'DARKNET', 'SKLEARN', ], ], 'FrameworkVersion' => [ 'type' => 'string', 'max' => 10, 'min' => 3, 'pattern' => '[0-9]\\.[A-Za-z0-9.]+', ], 'GenerateCandidateDefinitionsOnly' => [ 'type' => 'boolean', ], 'GetDeviceFleetReportRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceFleetName', ], 'members' => [ 'DeviceFleetName' => [ 'shape' => 'EntityName', ], ], ], 'GetDeviceFleetReportResponse' => [ 'type' => 'structure', 'required' => [ 'DeviceFleetArn', 'DeviceFleetName', ], 'members' => [ 'DeviceFleetArn' => [ 'shape' => 'DeviceFleetArn', ], 'DeviceFleetName' => [ 'shape' => 'EntityName', ], 'OutputConfig' => [ 'shape' => 'EdgeOutputConfig', ], 'Description' => [ 'shape' => 'DeviceFleetDescription', ], 'ReportGenerated' => [ 'shape' => 'Timestamp', ], 'DeviceStats' => [ 'shape' => 'DeviceStats', ], 'AgentVersions' => [ 'shape' => 'AgentVersions', ], 'ModelStats' => [ 'shape' => 'EdgeModelStats', ], ], ], 'GetModelPackageGroupPolicyInput' => [ 'type' => 'structure', 'required' => [ 'ModelPackageGroupName', ], 'members' => [ 'ModelPackageGroupName' => [ 'shape' => 'EntityName', ], ], ], 'GetModelPackageGroupPolicyOutput' => [ 'type' => 'structure', 'required' => [ 'ResourcePolicy', ], 'members' => [ 'ResourcePolicy' => [ 'shape' => 'PolicyString', ], ], ], 'GetSagemakerServicecatalogPortfolioStatusInput' => [ 'type' => 'structure', 'members' => [], ], 'GetSagemakerServicecatalogPortfolioStatusOutput' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'SagemakerServicecatalogStatus', ], ], ], 'GetSearchSuggestionsRequest' => [ 'type' => 'structure', 'required' => [ 'Resource', ], 'members' => [ 'Resource' => [ 'shape' => 'ResourceType', ], 'SuggestionQuery' => [ 'shape' => 'SuggestionQuery', ], ], ], 'GetSearchSuggestionsResponse' => [ 'type' => 'structure', 'members' => [ 'PropertyNameSuggestions' => [ 'shape' => 'PropertyNameSuggestionList', ], ], ], 'GitConfig' => [ 'type' => 'structure', 'required' => [ 'RepositoryUrl', ], 'members' => [ 'RepositoryUrl' => [ 'shape' => 'GitConfigUrl', ], 'Branch' => [ 'shape' => 'Branch', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], ], ], 'GitConfigForUpdate' => [ 'type' => 'structure', 'members' => [ 'SecretArn' => [ 'shape' => 'SecretArn', ], ], ], 'GitConfigUrl' => [ 'type' => 'string', 'pattern' => '^https://([^/]+)/?(.*)$', ], 'Group' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '[\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}]+', ], 'Groups' => [ 'type' => 'list', 'member' => [ 'shape' => 'Group', ], 'max' => 10, 'min' => 1, ], 'HookParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'ConfigKey', ], 'value' => [ 'shape' => 'ConfigValue', ], 'max' => 20, 'min' => 0, ], 'HumanLoopActivationConditions' => [ 'type' => 'string', 'max' => 10240, ], 'HumanLoopActivationConditionsConfig' => [ 'type' => 'structure', 'required' => [ 'HumanLoopActivationConditions', ], 'members' => [ 'HumanLoopActivationConditions' => [ 'shape' => 'HumanLoopActivationConditions', 'jsonvalue' => true, ], ], ], 'HumanLoopActivationConfig' => [ 'type' => 'structure', 'required' => [ 'HumanLoopActivationConditionsConfig', ], 'members' => [ 'HumanLoopActivationConditionsConfig' => [ 'shape' => 'HumanLoopActivationConditionsConfig', ], ], ], 'HumanLoopConfig' => [ 'type' => 'structure', 'required' => [ 'WorkteamArn', 'HumanTaskUiArn', 'TaskTitle', 'TaskDescription', 'TaskCount', ], 'members' => [ 'WorkteamArn' => [ 'shape' => 'WorkteamArn', ], 'HumanTaskUiArn' => [ 'shape' => 'HumanTaskUiArn', ], 'TaskTitle' => [ 'shape' => 'FlowDefinitionTaskTitle', ], 'TaskDescription' => [ 'shape' => 'FlowDefinitionTaskDescription', ], 'TaskCount' => [ 'shape' => 'FlowDefinitionTaskCount', ], 'TaskAvailabilityLifetimeInSeconds' => [ 'shape' => 'FlowDefinitionTaskAvailabilityLifetimeInSeconds', ], 'TaskTimeLimitInSeconds' => [ 'shape' => 'FlowDefinitionTaskTimeLimitInSeconds', ], 'TaskKeywords' => [ 'shape' => 'FlowDefinitionTaskKeywords', ], 'PublicWorkforceTaskPrice' => [ 'shape' => 'PublicWorkforceTaskPrice', ], ], ], 'HumanLoopRequestSource' => [ 'type' => 'structure', 'required' => [ 'AwsManagedHumanLoopRequestSource', ], 'members' => [ 'AwsManagedHumanLoopRequestSource' => [ 'shape' => 'AwsManagedHumanLoopRequestSource', ], ], ], 'HumanTaskConfig' => [ 'type' => 'structure', 'required' => [ 'WorkteamArn', 'UiConfig', 'PreHumanTaskLambdaArn', 'TaskTitle', 'TaskDescription', 'NumberOfHumanWorkersPerDataObject', 'TaskTimeLimitInSeconds', 'AnnotationConsolidationConfig', ], 'members' => [ 'WorkteamArn' => [ 'shape' => 'WorkteamArn', ], 'UiConfig' => [ 'shape' => 'UiConfig', ], 'PreHumanTaskLambdaArn' => [ 'shape' => 'LambdaFunctionArn', ], 'TaskKeywords' => [ 'shape' => 'TaskKeywords', ], 'TaskTitle' => [ 'shape' => 'TaskTitle', ], 'TaskDescription' => [ 'shape' => 'TaskDescription', ], 'NumberOfHumanWorkersPerDataObject' => [ 'shape' => 'NumberOfHumanWorkersPerDataObject', ], 'TaskTimeLimitInSeconds' => [ 'shape' => 'TaskTimeLimitInSeconds', ], 'TaskAvailabilityLifetimeInSeconds' => [ 'shape' => 'TaskAvailabilityLifetimeInSeconds', ], 'MaxConcurrentTaskCount' => [ 'shape' => 'MaxConcurrentTaskCount', ], 'AnnotationConsolidationConfig' => [ 'shape' => 'AnnotationConsolidationConfig', ], 'PublicWorkforceTaskPrice' => [ 'shape' => 'PublicWorkforceTaskPrice', ], ], ], 'HumanTaskUiArn' => [ 'type' => 'string', 'max' => 1024, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:human-task-ui/.*', ], 'HumanTaskUiName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-z0-9](-*[a-z0-9])*', ], 'HumanTaskUiStatus' => [ 'type' => 'string', 'enum' => [ 'Active', 'Deleting', ], ], 'HumanTaskUiSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'HumanTaskUiSummary', ], ], 'HumanTaskUiSummary' => [ 'type' => 'structure', 'required' => [ 'HumanTaskUiName', 'HumanTaskUiArn', 'CreationTime', ], 'members' => [ 'HumanTaskUiName' => [ 'shape' => 'HumanTaskUiName', ], 'HumanTaskUiArn' => [ 'shape' => 'HumanTaskUiArn', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], ], ], 'HyperParameterAlgorithmSpecification' => [ 'type' => 'structure', 'required' => [ 'TrainingInputMode', ], 'members' => [ 'TrainingImage' => [ 'shape' => 'AlgorithmImage', ], 'TrainingInputMode' => [ 'shape' => 'TrainingInputMode', ], 'AlgorithmName' => [ 'shape' => 'ArnOrName', ], 'MetricDefinitions' => [ 'shape' => 'MetricDefinitionList', ], ], ], 'HyperParameterKey' => [ 'type' => 'string', 'max' => 256, 'pattern' => '.*', ], 'HyperParameterScalingType' => [ 'type' => 'string', 'enum' => [ 'Auto', 'Linear', 'Logarithmic', 'ReverseLogarithmic', ], ], 'HyperParameterSpecification' => [ 'type' => 'structure', 'required' => [ 'Name', 'Type', ], 'members' => [ 'Name' => [ 'shape' => 'ParameterName', ], 'Description' => [ 'shape' => 'EntityDescription', ], 'Type' => [ 'shape' => 'ParameterType', ], 'Range' => [ 'shape' => 'ParameterRange', ], 'IsTunable' => [ 'shape' => 'Boolean', ], 'IsRequired' => [ 'shape' => 'Boolean', ], 'DefaultValue' => [ 'shape' => 'HyperParameterValue', ], ], ], 'HyperParameterSpecifications' => [ 'type' => 'list', 'member' => [ 'shape' => 'HyperParameterSpecification', ], 'max' => 100, 'min' => 0, ], 'HyperParameterTrainingJobDefinition' => [ 'type' => 'structure', 'required' => [ 'AlgorithmSpecification', 'RoleArn', 'OutputDataConfig', 'ResourceConfig', 'StoppingCondition', ], 'members' => [ 'DefinitionName' => [ 'shape' => 'HyperParameterTrainingJobDefinitionName', ], 'TuningObjective' => [ 'shape' => 'HyperParameterTuningJobObjective', ], 'HyperParameterRanges' => [ 'shape' => 'ParameterRanges', ], 'StaticHyperParameters' => [ 'shape' => 'HyperParameters', ], 'AlgorithmSpecification' => [ 'shape' => 'HyperParameterAlgorithmSpecification', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'OutputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'ResourceConfig' => [ 'shape' => 'ResourceConfig', ], 'StoppingCondition' => [ 'shape' => 'StoppingCondition', ], 'EnableNetworkIsolation' => [ 'shape' => 'Boolean', ], 'EnableInterContainerTrafficEncryption' => [ 'shape' => 'Boolean', ], 'EnableManagedSpotTraining' => [ 'shape' => 'Boolean', ], 'CheckpointConfig' => [ 'shape' => 'CheckpointConfig', ], 'RetryStrategy' => [ 'shape' => 'RetryStrategy', ], ], ], 'HyperParameterTrainingJobDefinitionName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,63}', ], 'HyperParameterTrainingJobDefinitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'HyperParameterTrainingJobDefinition', ], 'max' => 10, 'min' => 1, ], 'HyperParameterTrainingJobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'HyperParameterTrainingJobSummary', ], ], 'HyperParameterTrainingJobSummary' => [ 'type' => 'structure', 'required' => [ 'TrainingJobName', 'TrainingJobArn', 'CreationTime', 'TrainingJobStatus', 'TunedHyperParameters', ], 'members' => [ 'TrainingJobDefinitionName' => [ 'shape' => 'HyperParameterTrainingJobDefinitionName', ], 'TrainingJobName' => [ 'shape' => 'TrainingJobName', ], 'TrainingJobArn' => [ 'shape' => 'TrainingJobArn', ], 'TuningJobName' => [ 'shape' => 'HyperParameterTuningJobName', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'TrainingStartTime' => [ 'shape' => 'Timestamp', ], 'TrainingEndTime' => [ 'shape' => 'Timestamp', ], 'TrainingJobStatus' => [ 'shape' => 'TrainingJobStatus', ], 'TunedHyperParameters' => [ 'shape' => 'HyperParameters', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'FinalHyperParameterTuningJobObjectiveMetric' => [ 'shape' => 'FinalHyperParameterTuningJobObjectiveMetric', ], 'ObjectiveStatus' => [ 'shape' => 'ObjectiveStatus', ], ], ], 'HyperParameterTuningJobArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:hyper-parameter-tuning-job/.*', ], 'HyperParameterTuningJobConfig' => [ 'type' => 'structure', 'required' => [ 'Strategy', 'ResourceLimits', ], 'members' => [ 'Strategy' => [ 'shape' => 'HyperParameterTuningJobStrategyType', ], 'HyperParameterTuningJobObjective' => [ 'shape' => 'HyperParameterTuningJobObjective', ], 'ResourceLimits' => [ 'shape' => 'ResourceLimits', ], 'ParameterRanges' => [ 'shape' => 'ParameterRanges', ], 'TrainingJobEarlyStoppingType' => [ 'shape' => 'TrainingJobEarlyStoppingType', ], 'TuningJobCompletionCriteria' => [ 'shape' => 'TuningJobCompletionCriteria', ], ], ], 'HyperParameterTuningJobName' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,31}', ], 'HyperParameterTuningJobObjective' => [ 'type' => 'structure', 'required' => [ 'Type', 'MetricName', ], 'members' => [ 'Type' => [ 'shape' => 'HyperParameterTuningJobObjectiveType', ], 'MetricName' => [ 'shape' => 'MetricName', ], ], ], 'HyperParameterTuningJobObjectiveType' => [ 'type' => 'string', 'enum' => [ 'Maximize', 'Minimize', ], ], 'HyperParameterTuningJobObjectives' => [ 'type' => 'list', 'member' => [ 'shape' => 'HyperParameterTuningJobObjective', ], ], 'HyperParameterTuningJobSortByOptions' => [ 'type' => 'string', 'enum' => [ 'Name', 'Status', 'CreationTime', ], ], 'HyperParameterTuningJobStatus' => [ 'type' => 'string', 'enum' => [ 'Completed', 'InProgress', 'Failed', 'Stopped', 'Stopping', ], ], 'HyperParameterTuningJobStrategyType' => [ 'type' => 'string', 'enum' => [ 'Bayesian', 'Random', ], ], 'HyperParameterTuningJobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'HyperParameterTuningJobSummary', ], ], 'HyperParameterTuningJobSummary' => [ 'type' => 'structure', 'required' => [ 'HyperParameterTuningJobName', 'HyperParameterTuningJobArn', 'HyperParameterTuningJobStatus', 'Strategy', 'CreationTime', 'TrainingJobStatusCounters', 'ObjectiveStatusCounters', ], 'members' => [ 'HyperParameterTuningJobName' => [ 'shape' => 'HyperParameterTuningJobName', ], 'HyperParameterTuningJobArn' => [ 'shape' => 'HyperParameterTuningJobArn', ], 'HyperParameterTuningJobStatus' => [ 'shape' => 'HyperParameterTuningJobStatus', ], 'Strategy' => [ 'shape' => 'HyperParameterTuningJobStrategyType', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'HyperParameterTuningEndTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'TrainingJobStatusCounters' => [ 'shape' => 'TrainingJobStatusCounters', ], 'ObjectiveStatusCounters' => [ 'shape' => 'ObjectiveStatusCounters', ], 'ResourceLimits' => [ 'shape' => 'ResourceLimits', ], ], ], 'HyperParameterTuningJobWarmStartConfig' => [ 'type' => 'structure', 'required' => [ 'ParentHyperParameterTuningJobs', 'WarmStartType', ], 'members' => [ 'ParentHyperParameterTuningJobs' => [ 'shape' => 'ParentHyperParameterTuningJobs', ], 'WarmStartType' => [ 'shape' => 'HyperParameterTuningJobWarmStartType', ], ], ], 'HyperParameterTuningJobWarmStartType' => [ 'type' => 'string', 'enum' => [ 'IdenticalDataAndAlgorithm', 'TransferLearning', ], ], 'HyperParameterValue' => [ 'type' => 'string', 'max' => 2500, 'pattern' => '.*', ], 'HyperParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'HyperParameterKey', ], 'value' => [ 'shape' => 'HyperParameterValue', ], 'max' => 100, 'min' => 0, ], 'IdempotencyToken' => [ 'type' => 'string', 'max' => 128, 'min' => 32, ], 'Image' => [ 'type' => 'structure', 'required' => [ 'CreationTime', 'ImageArn', 'ImageName', 'ImageStatus', 'LastModifiedTime', ], 'members' => [ 'CreationTime' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'ImageDescription', ], 'DisplayName' => [ 'shape' => 'ImageDisplayName', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'ImageArn' => [ 'shape' => 'ImageArn', ], 'ImageName' => [ 'shape' => 'ImageName', ], 'ImageStatus' => [ 'shape' => 'ImageStatus', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'ImageArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^arn:aws(-[\\w]+)*:sagemaker:.+:[0-9]{12}:image/[a-z0-9]([-.]?[a-z0-9])*$', ], 'ImageBaseImage' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '.*', ], 'ImageConfig' => [ 'type' => 'structure', 'required' => [ 'RepositoryAccessMode', ], 'members' => [ 'RepositoryAccessMode' => [ 'shape' => 'RepositoryAccessMode', ], 'RepositoryAuthConfig' => [ 'shape' => 'RepositoryAuthConfig', ], ], ], 'ImageContainerImage' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ImageDeleteProperty' => [ 'type' => 'string', 'max' => 11, 'min' => 1, 'pattern' => '(^DisplayName$)|(^Description$)', ], 'ImageDeletePropertyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImageDeleteProperty', ], 'max' => 2, ], 'ImageDescription' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '.*', ], 'ImageDigest' => [ 'type' => 'string', 'max' => 72, 'pattern' => '^[Ss][Hh][Aa]256:[0-9a-fA-F]{64}$', ], 'ImageDisplayName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^\\S(.*\\S)?$', ], 'ImageName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-zA-Z0-9]([-.]?[a-zA-Z0-9]){0,62}$', ], 'ImageNameContains' => [ 'type' => 'string', 'max' => 63, 'pattern' => '^[a-zA-Z0-9\\-.]+$', ], 'ImageSortBy' => [ 'type' => 'string', 'enum' => [ 'CREATION_TIME', 'LAST_MODIFIED_TIME', 'IMAGE_NAME', ], ], 'ImageSortOrder' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'ImageStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'CREATED', 'CREATE_FAILED', 'UPDATING', 'UPDATE_FAILED', 'DELETING', 'DELETE_FAILED', ], ], 'ImageUri' => [ 'type' => 'string', 'max' => 255, 'pattern' => '.*', ], 'ImageVersion' => [ 'type' => 'structure', 'required' => [ 'CreationTime', 'ImageArn', 'ImageVersionArn', 'ImageVersionStatus', 'LastModifiedTime', 'Version', ], 'members' => [ 'CreationTime' => [ 'shape' => 'Timestamp', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'ImageArn' => [ 'shape' => 'ImageArn', ], 'ImageVersionArn' => [ 'shape' => 'ImageVersionArn', ], 'ImageVersionStatus' => [ 'shape' => 'ImageVersionStatus', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'Version' => [ 'shape' => 'ImageVersionNumber', ], ], ], 'ImageVersionArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^arn:aws(-[\\w]+)*:sagemaker:.+:[0-9]{12}:image-version/[a-z0-9]([-.]?[a-z0-9])*/[0-9]+$', ], 'ImageVersionNumber' => [ 'type' => 'integer', 'min' => 0, ], 'ImageVersionSortBy' => [ 'type' => 'string', 'enum' => [ 'CREATION_TIME', 'LAST_MODIFIED_TIME', 'VERSION', ], ], 'ImageVersionSortOrder' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'ImageVersionStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'CREATED', 'CREATE_FAILED', 'DELETING', 'DELETE_FAILED', ], ], 'ImageVersions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImageVersion', ], ], 'Images' => [ 'type' => 'list', 'member' => [ 'shape' => 'Image', ], ], 'InferenceExecutionConfig' => [ 'type' => 'structure', 'required' => [ 'Mode', ], 'members' => [ 'Mode' => [ 'shape' => 'InferenceExecutionMode', ], ], ], 'InferenceExecutionMode' => [ 'type' => 'string', 'enum' => [ 'Serial', 'Direct', ], ], 'InferenceImage' => [ 'type' => 'string', 'max' => 256, ], 'InferenceSpecification' => [ 'type' => 'structure', 'required' => [ 'Containers', 'SupportedContentTypes', 'SupportedResponseMIMETypes', ], 'members' => [ 'Containers' => [ 'shape' => 'ModelPackageContainerDefinitionList', ], 'SupportedTransformInstanceTypes' => [ 'shape' => 'TransformInstanceTypes', ], 'SupportedRealtimeInferenceInstanceTypes' => [ 'shape' => 'RealtimeInferenceInstanceTypes', ], 'SupportedContentTypes' => [ 'shape' => 'ContentTypes', ], 'SupportedResponseMIMETypes' => [ 'shape' => 'ResponseMIMETypes', ], ], ], 'InputConfig' => [ 'type' => 'structure', 'required' => [ 'S3Uri', 'DataInputConfig', 'Framework', ], 'members' => [ 'S3Uri' => [ 'shape' => 'S3Uri', ], 'DataInputConfig' => [ 'shape' => 'DataInputConfig', ], 'Framework' => [ 'shape' => 'Framework', ], 'FrameworkVersion' => [ 'shape' => 'FrameworkVersion', ], ], ], 'InputDataConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'Channel', ], 'max' => 20, 'min' => 1, ], 'InputMode' => [ 'type' => 'string', 'enum' => [ 'Pipe', 'File', ], ], 'InputModes' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrainingInputMode', ], 'min' => 1, ], 'InstanceType' => [ 'type' => 'string', 'enum' => [ 'ml.t2.medium', 'ml.t2.large', 'ml.t2.xlarge', 'ml.t2.2xlarge', 'ml.t3.medium', 'ml.t3.large', 'ml.t3.xlarge', 'ml.t3.2xlarge', 'ml.m4.xlarge', 'ml.m4.2xlarge', 'ml.m4.4xlarge', 'ml.m4.10xlarge', 'ml.m4.16xlarge', 'ml.m5.xlarge', 'ml.m5.2xlarge', 'ml.m5.4xlarge', 'ml.m5.12xlarge', 'ml.m5.24xlarge', 'ml.c4.xlarge', 'ml.c4.2xlarge', 'ml.c4.4xlarge', 'ml.c4.8xlarge', 'ml.c5.xlarge', 'ml.c5.2xlarge', 'ml.c5.4xlarge', 'ml.c5.9xlarge', 'ml.c5.18xlarge', 'ml.c5d.xlarge', 'ml.c5d.2xlarge', 'ml.c5d.4xlarge', 'ml.c5d.9xlarge', 'ml.c5d.18xlarge', 'ml.p2.xlarge', 'ml.p2.8xlarge', 'ml.p2.16xlarge', 'ml.p3.2xlarge', 'ml.p3.8xlarge', 'ml.p3.16xlarge', ], ], 'Integer' => [ 'type' => 'integer', ], 'IntegerParameterRange' => [ 'type' => 'structure', 'required' => [ 'Name', 'MinValue', 'MaxValue', ], 'members' => [ 'Name' => [ 'shape' => 'ParameterKey', ], 'MinValue' => [ 'shape' => 'ParameterValue', ], 'MaxValue' => [ 'shape' => 'ParameterValue', ], 'ScalingType' => [ 'shape' => 'HyperParameterScalingType', ], ], ], 'IntegerParameterRangeSpecification' => [ 'type' => 'structure', 'required' => [ 'MinValue', 'MaxValue', ], 'members' => [ 'MinValue' => [ 'shape' => 'ParameterValue', ], 'MaxValue' => [ 'shape' => 'ParameterValue', ], ], ], 'IntegerParameterRanges' => [ 'type' => 'list', 'member' => [ 'shape' => 'IntegerParameterRange', ], 'max' => 20, 'min' => 0, ], 'InvocationsMaxRetries' => [ 'type' => 'integer', 'max' => 3, 'min' => 0, ], 'InvocationsTimeoutInSeconds' => [ 'type' => 'integer', 'max' => 3600, 'min' => 1, ], 'IotRoleAlias' => [ 'type' => 'string', 'pattern' => '^arn:aws[a-z\\-]*:iam::\\d{12}:rolealias/?[a-zA-Z_0-9+=,.@\\-_/]+$', ], 'JobReferenceCode' => [ 'type' => 'string', 'min' => 1, 'pattern' => '.+', ], 'JobReferenceCodeContains' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '.+', ], 'JoinSource' => [ 'type' => 'string', 'enum' => [ 'Input', 'None', ], ], 'JsonContentType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9])*\\/[a-zA-Z0-9](-*[a-zA-Z0-9.])*', ], 'JsonContentTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'JsonContentType', ], 'max' => 10, 'min' => 1, ], 'JsonPath' => [ 'type' => 'string', 'max' => 63, 'min' => 0, ], 'JupyterServerAppSettings' => [ 'type' => 'structure', 'members' => [ 'DefaultResourceSpec' => [ 'shape' => 'ResourceSpec', ], ], ], 'KernelDisplayName' => [ 'type' => 'string', 'max' => 1024, ], 'KernelGatewayAppSettings' => [ 'type' => 'structure', 'members' => [ 'DefaultResourceSpec' => [ 'shape' => 'ResourceSpec', ], 'CustomImages' => [ 'shape' => 'CustomImages', ], ], ], 'KernelGatewayImageConfig' => [ 'type' => 'structure', 'required' => [ 'KernelSpecs', ], 'members' => [ 'KernelSpecs' => [ 'shape' => 'KernelSpecs', ], 'FileSystemConfig' => [ 'shape' => 'FileSystemConfig', ], ], ], 'KernelName' => [ 'type' => 'string', 'max' => 1024, ], 'KernelSpec' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'KernelName', ], 'DisplayName' => [ 'shape' => 'KernelDisplayName', ], ], ], 'KernelSpecs' => [ 'type' => 'list', 'member' => [ 'shape' => 'KernelSpec', ], 'max' => 1, 'min' => 1, ], 'KmsKeyId' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '.*', ], 'LabelAttributeName' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,126}', ], 'LabelCounter' => [ 'type' => 'integer', 'min' => 0, ], 'LabelCounters' => [ 'type' => 'structure', 'members' => [ 'TotalLabeled' => [ 'shape' => 'LabelCounter', ], 'HumanLabeled' => [ 'shape' => 'LabelCounter', ], 'MachineLabeled' => [ 'shape' => 'LabelCounter', ], 'FailedNonRetryableError' => [ 'shape' => 'LabelCounter', ], 'Unlabeled' => [ 'shape' => 'LabelCounter', ], ], ], 'LabelCountersForWorkteam' => [ 'type' => 'structure', 'members' => [ 'HumanLabeled' => [ 'shape' => 'LabelCounter', ], 'PendingHuman' => [ 'shape' => 'LabelCounter', ], 'Total' => [ 'shape' => 'LabelCounter', ], ], ], 'LabelingJobAlgorithmSpecificationArn' => [ 'type' => 'string', 'max' => 2048, 'pattern' => 'arn:.*', ], 'LabelingJobAlgorithmsConfig' => [ 'type' => 'structure', 'required' => [ 'LabelingJobAlgorithmSpecificationArn', ], 'members' => [ 'LabelingJobAlgorithmSpecificationArn' => [ 'shape' => 'LabelingJobAlgorithmSpecificationArn', ], 'InitialActiveLearningModelArn' => [ 'shape' => 'ModelArn', ], 'LabelingJobResourceConfig' => [ 'shape' => 'LabelingJobResourceConfig', ], ], ], 'LabelingJobArn' => [ 'type' => 'string', 'max' => 2048, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:labeling-job/.*', ], 'LabelingJobDataAttributes' => [ 'type' => 'structure', 'members' => [ 'ContentClassifiers' => [ 'shape' => 'ContentClassifiers', ], ], ], 'LabelingJobDataSource' => [ 'type' => 'structure', 'members' => [ 'S3DataSource' => [ 'shape' => 'LabelingJobS3DataSource', ], 'SnsDataSource' => [ 'shape' => 'LabelingJobSnsDataSource', ], ], ], 'LabelingJobForWorkteamSummary' => [ 'type' => 'structure', 'required' => [ 'JobReferenceCode', 'WorkRequesterAccountId', 'CreationTime', ], 'members' => [ 'LabelingJobName' => [ 'shape' => 'LabelingJobName', ], 'JobReferenceCode' => [ 'shape' => 'JobReferenceCode', ], 'WorkRequesterAccountId' => [ 'shape' => 'AccountId', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LabelCounters' => [ 'shape' => 'LabelCountersForWorkteam', ], 'NumberOfHumanWorkersPerDataObject' => [ 'shape' => 'NumberOfHumanWorkersPerDataObject', ], ], ], 'LabelingJobForWorkteamSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LabelingJobForWorkteamSummary', ], ], 'LabelingJobInputConfig' => [ 'type' => 'structure', 'required' => [ 'DataSource', ], 'members' => [ 'DataSource' => [ 'shape' => 'LabelingJobDataSource', ], 'DataAttributes' => [ 'shape' => 'LabelingJobDataAttributes', ], ], ], 'LabelingJobName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,62}', ], 'LabelingJobOutput' => [ 'type' => 'structure', 'required' => [ 'OutputDatasetS3Uri', ], 'members' => [ 'OutputDatasetS3Uri' => [ 'shape' => 'S3Uri', ], 'FinalActiveLearningModelArn' => [ 'shape' => 'ModelArn', ], ], ], 'LabelingJobOutputConfig' => [ 'type' => 'structure', 'required' => [ 'S3OutputPath', ], 'members' => [ 'S3OutputPath' => [ 'shape' => 'S3Uri', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'SnsTopicArn' => [ 'shape' => 'SnsTopicArn', ], ], ], 'LabelingJobResourceConfig' => [ 'type' => 'structure', 'members' => [ 'VolumeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'LabelingJobS3DataSource' => [ 'type' => 'structure', 'required' => [ 'ManifestS3Uri', ], 'members' => [ 'ManifestS3Uri' => [ 'shape' => 'S3Uri', ], ], ], 'LabelingJobSnsDataSource' => [ 'type' => 'structure', 'required' => [ 'SnsTopicArn', ], 'members' => [ 'SnsTopicArn' => [ 'shape' => 'SnsTopicArn', ], ], ], 'LabelingJobStatus' => [ 'type' => 'string', 'enum' => [ 'Initializing', 'InProgress', 'Completed', 'Failed', 'Stopping', 'Stopped', ], ], 'LabelingJobStoppingConditions' => [ 'type' => 'structure', 'members' => [ 'MaxHumanLabeledObjectCount' => [ 'shape' => 'MaxHumanLabeledObjectCount', ], 'MaxPercentageOfInputDatasetLabeled' => [ 'shape' => 'MaxPercentageOfInputDatasetLabeled', ], ], ], 'LabelingJobSummary' => [ 'type' => 'structure', 'required' => [ 'LabelingJobName', 'LabelingJobArn', 'CreationTime', 'LastModifiedTime', 'LabelingJobStatus', 'LabelCounters', 'WorkteamArn', 'PreHumanTaskLambdaArn', ], 'members' => [ 'LabelingJobName' => [ 'shape' => 'LabelingJobName', ], 'LabelingJobArn' => [ 'shape' => 'LabelingJobArn', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LabelingJobStatus' => [ 'shape' => 'LabelingJobStatus', ], 'LabelCounters' => [ 'shape' => 'LabelCounters', ], 'WorkteamArn' => [ 'shape' => 'WorkteamArn', ], 'PreHumanTaskLambdaArn' => [ 'shape' => 'LambdaFunctionArn', ], 'AnnotationConsolidationLambdaArn' => [ 'shape' => 'LambdaFunctionArn', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'LabelingJobOutput' => [ 'shape' => 'LabelingJobOutput', ], 'InputConfig' => [ 'shape' => 'LabelingJobInputConfig', ], ], ], 'LabelingJobSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LabelingJobSummary', ], ], 'LambdaFunctionArn' => [ 'type' => 'string', 'max' => 2048, 'pattern' => 'arn:aws[a-z\\-]*:lambda:[a-z0-9\\-]*:[0-9]{12}:function:.*', ], 'LastModifiedTime' => [ 'type' => 'timestamp', ], 'LineageEntityParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'StringParameterValue', ], 'value' => [ 'shape' => 'StringParameterValue', ], 'max' => 30, ], 'ListActionsRequest' => [ 'type' => 'structure', 'members' => [ 'SourceUri' => [ 'shape' => 'SourceUri', ], 'ActionType' => [ 'shape' => 'String256', ], 'CreatedAfter' => [ 'shape' => 'Timestamp', ], 'CreatedBefore' => [ 'shape' => 'Timestamp', ], 'SortBy' => [ 'shape' => 'SortActionsBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListActionsResponse' => [ 'type' => 'structure', 'members' => [ 'ActionSummaries' => [ 'shape' => 'ActionSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAlgorithmsInput' => [ 'type' => 'structure', 'members' => [ 'CreationTimeAfter' => [ 'shape' => 'CreationTime', ], 'CreationTimeBefore' => [ 'shape' => 'CreationTime', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NameContains' => [ 'shape' => 'NameContains', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'SortBy' => [ 'shape' => 'AlgorithmSortBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'ListAlgorithmsOutput' => [ 'type' => 'structure', 'required' => [ 'AlgorithmSummaryList', ], 'members' => [ 'AlgorithmSummaryList' => [ 'shape' => 'AlgorithmSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppImageConfigsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'NameContains' => [ 'shape' => 'AppImageConfigName', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], 'ModifiedTimeBefore' => [ 'shape' => 'Timestamp', ], 'ModifiedTimeAfter' => [ 'shape' => 'Timestamp', ], 'SortBy' => [ 'shape' => 'AppImageConfigSortKey', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'ListAppImageConfigsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'AppImageConfigs' => [ 'shape' => 'AppImageConfigList', ], ], ], 'ListAppsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'SortBy' => [ 'shape' => 'AppSortKey', ], 'DomainIdEquals' => [ 'shape' => 'DomainId', ], 'UserProfileNameEquals' => [ 'shape' => 'UserProfileName', ], ], ], 'ListAppsResponse' => [ 'type' => 'structure', 'members' => [ 'Apps' => [ 'shape' => 'AppList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListArtifactsRequest' => [ 'type' => 'structure', 'members' => [ 'SourceUri' => [ 'shape' => 'SourceUri', ], 'ArtifactType' => [ 'shape' => 'String256', ], 'CreatedAfter' => [ 'shape' => 'Timestamp', ], 'CreatedBefore' => [ 'shape' => 'Timestamp', ], 'SortBy' => [ 'shape' => 'SortArtifactsBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListArtifactsResponse' => [ 'type' => 'structure', 'members' => [ 'ArtifactSummaries' => [ 'shape' => 'ArtifactSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAssociationsRequest' => [ 'type' => 'structure', 'members' => [ 'SourceArn' => [ 'shape' => 'AssociationEntityArn', ], 'DestinationArn' => [ 'shape' => 'AssociationEntityArn', ], 'SourceType' => [ 'shape' => 'String256', ], 'DestinationType' => [ 'shape' => 'String256', ], 'AssociationType' => [ 'shape' => 'AssociationEdgeType', ], 'CreatedAfter' => [ 'shape' => 'Timestamp', ], 'CreatedBefore' => [ 'shape' => 'Timestamp', ], 'SortBy' => [ 'shape' => 'SortAssociationsBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'AssociationSummaries' => [ 'shape' => 'AssociationSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAutoMLJobsRequest' => [ 'type' => 'structure', 'members' => [ 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimeAfter' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimeBefore' => [ 'shape' => 'Timestamp', ], 'NameContains' => [ 'shape' => 'AutoMLNameContains', ], 'StatusEquals' => [ 'shape' => 'AutoMLJobStatus', ], 'SortOrder' => [ 'shape' => 'AutoMLSortOrder', ], 'SortBy' => [ 'shape' => 'AutoMLSortBy', ], 'MaxResults' => [ 'shape' => 'AutoMLMaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAutoMLJobsResponse' => [ 'type' => 'structure', 'required' => [ 'AutoMLJobSummaries', ], 'members' => [ 'AutoMLJobSummaries' => [ 'shape' => 'AutoMLJobSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCandidatesForAutoMLJobRequest' => [ 'type' => 'structure', 'required' => [ 'AutoMLJobName', ], 'members' => [ 'AutoMLJobName' => [ 'shape' => 'AutoMLJobName', ], 'StatusEquals' => [ 'shape' => 'CandidateStatus', ], 'CandidateNameEquals' => [ 'shape' => 'CandidateName', ], 'SortOrder' => [ 'shape' => 'AutoMLSortOrder', ], 'SortBy' => [ 'shape' => 'CandidateSortBy', ], 'MaxResults' => [ 'shape' => 'AutoMLMaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCandidatesForAutoMLJobResponse' => [ 'type' => 'structure', 'required' => [ 'Candidates', ], 'members' => [ 'Candidates' => [ 'shape' => 'AutoMLCandidates', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCodeRepositoriesInput' => [ 'type' => 'structure', 'members' => [ 'CreationTimeAfter' => [ 'shape' => 'CreationTime', ], 'CreationTimeBefore' => [ 'shape' => 'CreationTime', ], 'LastModifiedTimeAfter' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimeBefore' => [ 'shape' => 'Timestamp', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NameContains' => [ 'shape' => 'CodeRepositoryNameContains', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'SortBy' => [ 'shape' => 'CodeRepositorySortBy', ], 'SortOrder' => [ 'shape' => 'CodeRepositorySortOrder', ], ], ], 'ListCodeRepositoriesOutput' => [ 'type' => 'structure', 'required' => [ 'CodeRepositorySummaryList', ], 'members' => [ 'CodeRepositorySummaryList' => [ 'shape' => 'CodeRepositorySummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCompilationJobsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'CreationTimeAfter' => [ 'shape' => 'CreationTime', ], 'CreationTimeBefore' => [ 'shape' => 'CreationTime', ], 'LastModifiedTimeAfter' => [ 'shape' => 'LastModifiedTime', ], 'LastModifiedTimeBefore' => [ 'shape' => 'LastModifiedTime', ], 'NameContains' => [ 'shape' => 'NameContains', ], 'StatusEquals' => [ 'shape' => 'CompilationJobStatus', ], 'SortBy' => [ 'shape' => 'ListCompilationJobsSortBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'ListCompilationJobsResponse' => [ 'type' => 'structure', 'required' => [ 'CompilationJobSummaries', ], 'members' => [ 'CompilationJobSummaries' => [ 'shape' => 'CompilationJobSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCompilationJobsSortBy' => [ 'type' => 'string', 'enum' => [ 'Name', 'CreationTime', 'Status', ], ], 'ListContextsRequest' => [ 'type' => 'structure', 'members' => [ 'SourceUri' => [ 'shape' => 'SourceUri', ], 'ContextType' => [ 'shape' => 'String256', ], 'CreatedAfter' => [ 'shape' => 'Timestamp', ], 'CreatedBefore' => [ 'shape' => 'Timestamp', ], 'SortBy' => [ 'shape' => 'SortContextsBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListContextsResponse' => [ 'type' => 'structure', 'members' => [ 'ContextSummaries' => [ 'shape' => 'ContextSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDataQualityJobDefinitionsRequest' => [ 'type' => 'structure', 'members' => [ 'EndpointName' => [ 'shape' => 'EndpointName', ], 'SortBy' => [ 'shape' => 'MonitoringJobDefinitionSortKey', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NameContains' => [ 'shape' => 'NameContains', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], ], ], 'ListDataQualityJobDefinitionsResponse' => [ 'type' => 'structure', 'required' => [ 'JobDefinitionSummaries', ], 'members' => [ 'JobDefinitionSummaries' => [ 'shape' => 'MonitoringJobDefinitionSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDeviceFleetsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'ListMaxResults', 'box' => true, ], 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimeAfter' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimeBefore' => [ 'shape' => 'Timestamp', ], 'NameContains' => [ 'shape' => 'NameContains', ], 'SortBy' => [ 'shape' => 'ListDeviceFleetsSortBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'ListDeviceFleetsResponse' => [ 'type' => 'structure', 'required' => [ 'DeviceFleetSummaries', ], 'members' => [ 'DeviceFleetSummaries' => [ 'shape' => 'DeviceFleetSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDeviceFleetsSortBy' => [ 'type' => 'string', 'enum' => [ 'NAME', 'CREATION_TIME', 'LAST_MODIFIED_TIME', ], ], 'ListDevicesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'ListMaxResults', 'box' => true, ], 'LatestHeartbeatAfter' => [ 'shape' => 'Timestamp', ], 'ModelName' => [ 'shape' => 'EntityName', ], 'DeviceFleetName' => [ 'shape' => 'EntityName', ], ], ], 'ListDevicesResponse' => [ 'type' => 'structure', 'required' => [ 'DeviceSummaries', ], 'members' => [ 'DeviceSummaries' => [ 'shape' => 'DeviceSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDomainsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListDomainsResponse' => [ 'type' => 'structure', 'members' => [ 'Domains' => [ 'shape' => 'DomainList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEdgePackagingJobsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'ListMaxResults', 'box' => true, ], 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimeAfter' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimeBefore' => [ 'shape' => 'Timestamp', ], 'NameContains' => [ 'shape' => 'NameContains', ], 'ModelNameContains' => [ 'shape' => 'NameContains', ], 'StatusEquals' => [ 'shape' => 'EdgePackagingJobStatus', ], 'SortBy' => [ 'shape' => 'ListEdgePackagingJobsSortBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'ListEdgePackagingJobsResponse' => [ 'type' => 'structure', 'required' => [ 'EdgePackagingJobSummaries', ], 'members' => [ 'EdgePackagingJobSummaries' => [ 'shape' => 'EdgePackagingJobSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEdgePackagingJobsSortBy' => [ 'type' => 'string', 'enum' => [ 'NAME', 'MODEL_NAME', 'CREATION_TIME', 'LAST_MODIFIED_TIME', 'STATUS', ], ], 'ListEndpointConfigsInput' => [ 'type' => 'structure', 'members' => [ 'SortBy' => [ 'shape' => 'EndpointConfigSortKey', ], 'SortOrder' => [ 'shape' => 'OrderKey', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NameContains' => [ 'shape' => 'EndpointConfigNameContains', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], ], ], 'ListEndpointConfigsOutput' => [ 'type' => 'structure', 'required' => [ 'EndpointConfigs', ], 'members' => [ 'EndpointConfigs' => [ 'shape' => 'EndpointConfigSummaryList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListEndpointsInput' => [ 'type' => 'structure', 'members' => [ 'SortBy' => [ 'shape' => 'EndpointSortKey', ], 'SortOrder' => [ 'shape' => 'OrderKey', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NameContains' => [ 'shape' => 'EndpointNameContains', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimeBefore' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimeAfter' => [ 'shape' => 'Timestamp', ], 'StatusEquals' => [ 'shape' => 'EndpointStatus', ], ], ], 'ListEndpointsOutput' => [ 'type' => 'structure', 'required' => [ 'Endpoints', ], 'members' => [ 'Endpoints' => [ 'shape' => 'EndpointSummaryList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListExperimentsRequest' => [ 'type' => 'structure', 'members' => [ 'CreatedAfter' => [ 'shape' => 'Timestamp', ], 'CreatedBefore' => [ 'shape' => 'Timestamp', ], 'SortBy' => [ 'shape' => 'SortExperimentsBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListExperimentsResponse' => [ 'type' => 'structure', 'members' => [ 'ExperimentSummaries' => [ 'shape' => 'ExperimentSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFeatureGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'NameContains' => [ 'shape' => 'FeatureGroupNameContains', ], 'FeatureGroupStatusEquals' => [ 'shape' => 'FeatureGroupStatus', ], 'OfflineStoreStatusEquals' => [ 'shape' => 'OfflineStoreStatusValue', ], 'CreationTimeAfter' => [ 'shape' => 'CreationTime', ], 'CreationTimeBefore' => [ 'shape' => 'CreationTime', ], 'SortOrder' => [ 'shape' => 'FeatureGroupSortOrder', ], 'SortBy' => [ 'shape' => 'FeatureGroupSortBy', ], 'MaxResults' => [ 'shape' => 'FeatureGroupMaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFeatureGroupsResponse' => [ 'type' => 'structure', 'required' => [ 'FeatureGroupSummaries', 'NextToken', ], 'members' => [ 'FeatureGroupSummaries' => [ 'shape' => 'FeatureGroupSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFlowDefinitionsRequest' => [ 'type' => 'structure', 'members' => [ 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], ], ], 'ListFlowDefinitionsResponse' => [ 'type' => 'structure', 'required' => [ 'FlowDefinitionSummaries', ], 'members' => [ 'FlowDefinitionSummaries' => [ 'shape' => 'FlowDefinitionSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListHumanTaskUisRequest' => [ 'type' => 'structure', 'members' => [ 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], ], ], 'ListHumanTaskUisResponse' => [ 'type' => 'structure', 'required' => [ 'HumanTaskUiSummaries', ], 'members' => [ 'HumanTaskUiSummaries' => [ 'shape' => 'HumanTaskUiSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListHyperParameterTuningJobsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'SortBy' => [ 'shape' => 'HyperParameterTuningJobSortByOptions', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'NameContains' => [ 'shape' => 'NameContains', ], 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimeAfter' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimeBefore' => [ 'shape' => 'Timestamp', ], 'StatusEquals' => [ 'shape' => 'HyperParameterTuningJobStatus', ], ], ], 'ListHyperParameterTuningJobsResponse' => [ 'type' => 'structure', 'required' => [ 'HyperParameterTuningJobSummaries', ], 'members' => [ 'HyperParameterTuningJobSummaries' => [ 'shape' => 'HyperParameterTuningJobSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListImageVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'ImageName', ], 'members' => [ 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], 'ImageName' => [ 'shape' => 'ImageName', ], 'LastModifiedTimeAfter' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimeBefore' => [ 'shape' => 'Timestamp', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'SortBy' => [ 'shape' => 'ImageVersionSortBy', ], 'SortOrder' => [ 'shape' => 'ImageVersionSortOrder', ], ], ], 'ListImageVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'ImageVersions' => [ 'shape' => 'ImageVersions', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListImagesRequest' => [ 'type' => 'structure', 'members' => [ 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimeAfter' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimeBefore' => [ 'shape' => 'Timestamp', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NameContains' => [ 'shape' => 'ImageNameContains', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'SortBy' => [ 'shape' => 'ImageSortBy', ], 'SortOrder' => [ 'shape' => 'ImageSortOrder', ], ], ], 'ListImagesResponse' => [ 'type' => 'structure', 'members' => [ 'Images' => [ 'shape' => 'Images', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListLabelingJobsForWorkteamRequest' => [ 'type' => 'structure', 'required' => [ 'WorkteamArn', ], 'members' => [ 'WorkteamArn' => [ 'shape' => 'WorkteamArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], 'JobReferenceCodeContains' => [ 'shape' => 'JobReferenceCodeContains', ], 'SortBy' => [ 'shape' => 'ListLabelingJobsForWorkteamSortByOptions', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'ListLabelingJobsForWorkteamResponse' => [ 'type' => 'structure', 'required' => [ 'LabelingJobSummaryList', ], 'members' => [ 'LabelingJobSummaryList' => [ 'shape' => 'LabelingJobForWorkteamSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListLabelingJobsForWorkteamSortByOptions' => [ 'type' => 'string', 'enum' => [ 'CreationTime', ], ], 'ListLabelingJobsRequest' => [ 'type' => 'structure', 'members' => [ 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimeAfter' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimeBefore' => [ 'shape' => 'Timestamp', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'NameContains' => [ 'shape' => 'NameContains', ], 'SortBy' => [ 'shape' => 'SortBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'StatusEquals' => [ 'shape' => 'LabelingJobStatus', ], ], ], 'ListLabelingJobsResponse' => [ 'type' => 'structure', 'members' => [ 'LabelingJobSummaryList' => [ 'shape' => 'LabelingJobSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListLineageEntityParameterKey' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringParameterValue', ], ], 'ListMaxResults' => [ 'type' => 'integer', 'max' => 100, ], 'ListModelBiasJobDefinitionsRequest' => [ 'type' => 'structure', 'members' => [ 'EndpointName' => [ 'shape' => 'EndpointName', ], 'SortBy' => [ 'shape' => 'MonitoringJobDefinitionSortKey', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NameContains' => [ 'shape' => 'NameContains', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], ], ], 'ListModelBiasJobDefinitionsResponse' => [ 'type' => 'structure', 'required' => [ 'JobDefinitionSummaries', ], 'members' => [ 'JobDefinitionSummaries' => [ 'shape' => 'MonitoringJobDefinitionSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListModelExplainabilityJobDefinitionsRequest' => [ 'type' => 'structure', 'members' => [ 'EndpointName' => [ 'shape' => 'EndpointName', ], 'SortBy' => [ 'shape' => 'MonitoringJobDefinitionSortKey', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NameContains' => [ 'shape' => 'NameContains', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], ], ], 'ListModelExplainabilityJobDefinitionsResponse' => [ 'type' => 'structure', 'required' => [ 'JobDefinitionSummaries', ], 'members' => [ 'JobDefinitionSummaries' => [ 'shape' => 'MonitoringJobDefinitionSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListModelPackageGroupsInput' => [ 'type' => 'structure', 'members' => [ 'CreationTimeAfter' => [ 'shape' => 'CreationTime', ], 'CreationTimeBefore' => [ 'shape' => 'CreationTime', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NameContains' => [ 'shape' => 'NameContains', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'SortBy' => [ 'shape' => 'ModelPackageGroupSortBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'ListModelPackageGroupsOutput' => [ 'type' => 'structure', 'required' => [ 'ModelPackageGroupSummaryList', ], 'members' => [ 'ModelPackageGroupSummaryList' => [ 'shape' => 'ModelPackageGroupSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListModelPackagesInput' => [ 'type' => 'structure', 'members' => [ 'CreationTimeAfter' => [ 'shape' => 'CreationTime', ], 'CreationTimeBefore' => [ 'shape' => 'CreationTime', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NameContains' => [ 'shape' => 'NameContains', ], 'ModelApprovalStatus' => [ 'shape' => 'ModelApprovalStatus', ], 'ModelPackageGroupName' => [ 'shape' => 'ArnOrName', ], 'ModelPackageType' => [ 'shape' => 'ModelPackageType', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'SortBy' => [ 'shape' => 'ModelPackageSortBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'ListModelPackagesOutput' => [ 'type' => 'structure', 'required' => [ 'ModelPackageSummaryList', ], 'members' => [ 'ModelPackageSummaryList' => [ 'shape' => 'ModelPackageSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListModelQualityJobDefinitionsRequest' => [ 'type' => 'structure', 'members' => [ 'EndpointName' => [ 'shape' => 'EndpointName', ], 'SortBy' => [ 'shape' => 'MonitoringJobDefinitionSortKey', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NameContains' => [ 'shape' => 'NameContains', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], ], ], 'ListModelQualityJobDefinitionsResponse' => [ 'type' => 'structure', 'required' => [ 'JobDefinitionSummaries', ], 'members' => [ 'JobDefinitionSummaries' => [ 'shape' => 'MonitoringJobDefinitionSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListModelsInput' => [ 'type' => 'structure', 'members' => [ 'SortBy' => [ 'shape' => 'ModelSortKey', ], 'SortOrder' => [ 'shape' => 'OrderKey', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NameContains' => [ 'shape' => 'ModelNameContains', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], ], ], 'ListModelsOutput' => [ 'type' => 'structure', 'required' => [ 'Models', ], 'members' => [ 'Models' => [ 'shape' => 'ModelSummaryList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListMonitoringExecutionsRequest' => [ 'type' => 'structure', 'members' => [ 'MonitoringScheduleName' => [ 'shape' => 'MonitoringScheduleName', ], 'EndpointName' => [ 'shape' => 'EndpointName', ], 'SortBy' => [ 'shape' => 'MonitoringExecutionSortKey', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'ScheduledTimeBefore' => [ 'shape' => 'Timestamp', ], 'ScheduledTimeAfter' => [ 'shape' => 'Timestamp', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimeBefore' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimeAfter' => [ 'shape' => 'Timestamp', ], 'StatusEquals' => [ 'shape' => 'ExecutionStatus', ], 'MonitoringJobDefinitionName' => [ 'shape' => 'MonitoringJobDefinitionName', ], 'MonitoringTypeEquals' => [ 'shape' => 'MonitoringType', ], ], ], 'ListMonitoringExecutionsResponse' => [ 'type' => 'structure', 'required' => [ 'MonitoringExecutionSummaries', ], 'members' => [ 'MonitoringExecutionSummaries' => [ 'shape' => 'MonitoringExecutionSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMonitoringSchedulesRequest' => [ 'type' => 'structure', 'members' => [ 'EndpointName' => [ 'shape' => 'EndpointName', ], 'SortBy' => [ 'shape' => 'MonitoringScheduleSortKey', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NameContains' => [ 'shape' => 'NameContains', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimeBefore' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimeAfter' => [ 'shape' => 'Timestamp', ], 'StatusEquals' => [ 'shape' => 'ScheduleStatus', ], 'MonitoringJobDefinitionName' => [ 'shape' => 'MonitoringJobDefinitionName', ], 'MonitoringTypeEquals' => [ 'shape' => 'MonitoringType', ], ], ], 'ListMonitoringSchedulesResponse' => [ 'type' => 'structure', 'required' => [ 'MonitoringScheduleSummaries', ], 'members' => [ 'MonitoringScheduleSummaries' => [ 'shape' => 'MonitoringScheduleSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListNotebookInstanceLifecycleConfigsInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'SortBy' => [ 'shape' => 'NotebookInstanceLifecycleConfigSortKey', ], 'SortOrder' => [ 'shape' => 'NotebookInstanceLifecycleConfigSortOrder', ], 'NameContains' => [ 'shape' => 'NotebookInstanceLifecycleConfigNameContains', ], 'CreationTimeBefore' => [ 'shape' => 'CreationTime', ], 'CreationTimeAfter' => [ 'shape' => 'CreationTime', ], 'LastModifiedTimeBefore' => [ 'shape' => 'LastModifiedTime', ], 'LastModifiedTimeAfter' => [ 'shape' => 'LastModifiedTime', ], ], ], 'ListNotebookInstanceLifecycleConfigsOutput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'NotebookInstanceLifecycleConfigs' => [ 'shape' => 'NotebookInstanceLifecycleConfigSummaryList', ], ], ], 'ListNotebookInstancesInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'SortBy' => [ 'shape' => 'NotebookInstanceSortKey', ], 'SortOrder' => [ 'shape' => 'NotebookInstanceSortOrder', ], 'NameContains' => [ 'shape' => 'NotebookInstanceNameContains', ], 'CreationTimeBefore' => [ 'shape' => 'CreationTime', ], 'CreationTimeAfter' => [ 'shape' => 'CreationTime', ], 'LastModifiedTimeBefore' => [ 'shape' => 'LastModifiedTime', ], 'LastModifiedTimeAfter' => [ 'shape' => 'LastModifiedTime', ], 'StatusEquals' => [ 'shape' => 'NotebookInstanceStatus', ], 'NotebookInstanceLifecycleConfigNameContains' => [ 'shape' => 'NotebookInstanceLifecycleConfigName', ], 'DefaultCodeRepositoryContains' => [ 'shape' => 'CodeRepositoryContains', ], 'AdditionalCodeRepositoryEquals' => [ 'shape' => 'CodeRepositoryNameOrUrl', ], ], ], 'ListNotebookInstancesOutput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'NotebookInstances' => [ 'shape' => 'NotebookInstanceSummaryList', ], ], ], 'ListPipelineExecutionStepsRequest' => [ 'type' => 'structure', 'members' => [ 'PipelineExecutionArn' => [ 'shape' => 'PipelineExecutionArn', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'ListPipelineExecutionStepsResponse' => [ 'type' => 'structure', 'members' => [ 'PipelineExecutionSteps' => [ 'shape' => 'PipelineExecutionStepList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPipelineExecutionsRequest' => [ 'type' => 'structure', 'required' => [ 'PipelineName', ], 'members' => [ 'PipelineName' => [ 'shape' => 'PipelineName', ], 'CreatedAfter' => [ 'shape' => 'Timestamp', ], 'CreatedBefore' => [ 'shape' => 'Timestamp', ], 'SortBy' => [ 'shape' => 'SortPipelineExecutionsBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListPipelineExecutionsResponse' => [ 'type' => 'structure', 'members' => [ 'PipelineExecutionSummaries' => [ 'shape' => 'PipelineExecutionSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPipelineParametersForExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'PipelineExecutionArn', ], 'members' => [ 'PipelineExecutionArn' => [ 'shape' => 'PipelineExecutionArn', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListPipelineParametersForExecutionResponse' => [ 'type' => 'structure', 'members' => [ 'PipelineParameters' => [ 'shape' => 'ParameterList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPipelinesRequest' => [ 'type' => 'structure', 'members' => [ 'PipelineNamePrefix' => [ 'shape' => 'PipelineName', ], 'CreatedAfter' => [ 'shape' => 'Timestamp', ], 'CreatedBefore' => [ 'shape' => 'Timestamp', ], 'SortBy' => [ 'shape' => 'SortPipelinesBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListPipelinesResponse' => [ 'type' => 'structure', 'members' => [ 'PipelineSummaries' => [ 'shape' => 'PipelineSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListProcessingJobsRequest' => [ 'type' => 'structure', 'members' => [ 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimeAfter' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimeBefore' => [ 'shape' => 'Timestamp', ], 'NameContains' => [ 'shape' => 'String', ], 'StatusEquals' => [ 'shape' => 'ProcessingJobStatus', ], 'SortBy' => [ 'shape' => 'SortBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], ], ], 'ListProcessingJobsResponse' => [ 'type' => 'structure', 'required' => [ 'ProcessingJobSummaries', ], 'members' => [ 'ProcessingJobSummaries' => [ 'shape' => 'ProcessingJobSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListProjectsInput' => [ 'type' => 'structure', 'members' => [ 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NameContains' => [ 'shape' => 'ProjectEntityName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'SortBy' => [ 'shape' => 'ProjectSortBy', ], 'SortOrder' => [ 'shape' => 'ProjectSortOrder', ], ], ], 'ListProjectsOutput' => [ 'type' => 'structure', 'required' => [ 'ProjectSummaryList', ], 'members' => [ 'ProjectSummaryList' => [ 'shape' => 'ProjectSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSubscribedWorkteamsRequest' => [ 'type' => 'structure', 'members' => [ 'NameContains' => [ 'shape' => 'WorkteamName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], ], ], 'ListSubscribedWorkteamsResponse' => [ 'type' => 'structure', 'required' => [ 'SubscribedWorkteams', ], 'members' => [ 'SubscribedWorkteams' => [ 'shape' => 'SubscribedWorkteams', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsInput' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'ListTagsMaxResults', ], ], ], 'ListTagsMaxResults' => [ 'type' => 'integer', 'min' => 50, ], 'ListTagsOutput' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTrainingJobsForHyperParameterTuningJobRequest' => [ 'type' => 'structure', 'required' => [ 'HyperParameterTuningJobName', ], 'members' => [ 'HyperParameterTuningJobName' => [ 'shape' => 'HyperParameterTuningJobName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'StatusEquals' => [ 'shape' => 'TrainingJobStatus', ], 'SortBy' => [ 'shape' => 'TrainingJobSortByOptions', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'ListTrainingJobsForHyperParameterTuningJobResponse' => [ 'type' => 'structure', 'required' => [ 'TrainingJobSummaries', ], 'members' => [ 'TrainingJobSummaries' => [ 'shape' => 'HyperParameterTrainingJobSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTrainingJobsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimeAfter' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimeBefore' => [ 'shape' => 'Timestamp', ], 'NameContains' => [ 'shape' => 'NameContains', ], 'StatusEquals' => [ 'shape' => 'TrainingJobStatus', ], 'SortBy' => [ 'shape' => 'SortBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'ListTrainingJobsResponse' => [ 'type' => 'structure', 'required' => [ 'TrainingJobSummaries', ], 'members' => [ 'TrainingJobSummaries' => [ 'shape' => 'TrainingJobSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTransformJobsRequest' => [ 'type' => 'structure', 'members' => [ 'CreationTimeAfter' => [ 'shape' => 'Timestamp', ], 'CreationTimeBefore' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimeAfter' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimeBefore' => [ 'shape' => 'Timestamp', ], 'NameContains' => [ 'shape' => 'NameContains', ], 'StatusEquals' => [ 'shape' => 'TransformJobStatus', ], 'SortBy' => [ 'shape' => 'SortBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], ], ], 'ListTransformJobsResponse' => [ 'type' => 'structure', 'required' => [ 'TransformJobSummaries', ], 'members' => [ 'TransformJobSummaries' => [ 'shape' => 'TransformJobSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTrialComponentKey256' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrialComponentKey256', ], ], 'ListTrialComponentsRequest' => [ 'type' => 'structure', 'members' => [ 'ExperimentName' => [ 'shape' => 'ExperimentEntityName', ], 'TrialName' => [ 'shape' => 'ExperimentEntityName', ], 'SourceArn' => [ 'shape' => 'String256', ], 'CreatedAfter' => [ 'shape' => 'Timestamp', ], 'CreatedBefore' => [ 'shape' => 'Timestamp', ], 'SortBy' => [ 'shape' => 'SortTrialComponentsBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTrialComponentsResponse' => [ 'type' => 'structure', 'members' => [ 'TrialComponentSummaries' => [ 'shape' => 'TrialComponentSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTrialsRequest' => [ 'type' => 'structure', 'members' => [ 'ExperimentName' => [ 'shape' => 'ExperimentEntityName', ], 'TrialComponentName' => [ 'shape' => 'ExperimentEntityName', ], 'CreatedAfter' => [ 'shape' => 'Timestamp', ], 'CreatedBefore' => [ 'shape' => 'Timestamp', ], 'SortBy' => [ 'shape' => 'SortTrialsBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTrialsResponse' => [ 'type' => 'structure', 'members' => [ 'TrialSummaries' => [ 'shape' => 'TrialSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListUserProfilesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'SortBy' => [ 'shape' => 'UserProfileSortKey', ], 'DomainIdEquals' => [ 'shape' => 'DomainId', ], 'UserProfileNameContains' => [ 'shape' => 'UserProfileName', ], ], ], 'ListUserProfilesResponse' => [ 'type' => 'structure', 'members' => [ 'UserProfiles' => [ 'shape' => 'UserProfileList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListWorkforcesRequest' => [ 'type' => 'structure', 'members' => [ 'SortBy' => [ 'shape' => 'ListWorkforcesSortByOptions', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'NameContains' => [ 'shape' => 'WorkforceName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], ], ], 'ListWorkforcesResponse' => [ 'type' => 'structure', 'required' => [ 'Workforces', ], 'members' => [ 'Workforces' => [ 'shape' => 'Workforces', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListWorkforcesSortByOptions' => [ 'type' => 'string', 'enum' => [ 'Name', 'CreateDate', ], ], 'ListWorkteamsRequest' => [ 'type' => 'structure', 'members' => [ 'SortBy' => [ 'shape' => 'ListWorkteamsSortByOptions', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'NameContains' => [ 'shape' => 'WorkteamName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], ], ], 'ListWorkteamsResponse' => [ 'type' => 'structure', 'required' => [ 'Workteams', ], 'members' => [ 'Workteams' => [ 'shape' => 'Workteams', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListWorkteamsSortByOptions' => [ 'type' => 'string', 'enum' => [ 'Name', 'CreateDate', ], ], 'Long' => [ 'type' => 'long', ], 'MaxAutoMLJobRuntimeInSeconds' => [ 'type' => 'integer', 'min' => 1, ], 'MaxCandidates' => [ 'type' => 'integer', 'min' => 1, ], 'MaxConcurrentTaskCount' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'MaxConcurrentTransforms' => [ 'type' => 'integer', 'min' => 0, ], 'MaxHumanLabeledObjectCount' => [ 'type' => 'integer', 'min' => 1, ], 'MaxNumberOfTrainingJobs' => [ 'type' => 'integer', 'min' => 1, ], 'MaxParallelTrainingJobs' => [ 'type' => 'integer', 'min' => 1, ], 'MaxPayloadInMB' => [ 'type' => 'integer', 'min' => 0, ], 'MaxPercentageOfInputDatasetLabeled' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MaxRuntimeInSeconds' => [ 'type' => 'integer', 'min' => 1, ], 'MaxRuntimePerTrainingJobInSeconds' => [ 'type' => 'integer', 'min' => 1, ], 'MaxWaitTimeInSeconds' => [ 'type' => 'integer', 'min' => 1, ], 'MaximumExecutionTimeoutInSeconds' => [ 'type' => 'integer', 'max' => 14400, 'min' => 600, ], 'MaximumRetryAttempts' => [ 'type' => 'integer', 'max' => 30, 'min' => 1, ], 'MediaType' => [ 'type' => 'string', 'max' => 64, 'pattern' => '^[-\\w]+\\/[-\\w+]+$', ], 'MemberDefinition' => [ 'type' => 'structure', 'members' => [ 'CognitoMemberDefinition' => [ 'shape' => 'CognitoMemberDefinition', ], 'OidcMemberDefinition' => [ 'shape' => 'OidcMemberDefinition', ], ], ], 'MemberDefinitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'MemberDefinition', ], 'max' => 10, 'min' => 1, ], 'MetadataProperties' => [ 'type' => 'structure', 'members' => [ 'CommitId' => [ 'shape' => 'MetadataPropertyValue', ], 'Repository' => [ 'shape' => 'MetadataPropertyValue', ], 'GeneratedBy' => [ 'shape' => 'MetadataPropertyValue', ], 'ProjectId' => [ 'shape' => 'MetadataPropertyValue', ], ], ], 'MetadataPropertyValue' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '.*', ], 'MetricData' => [ 'type' => 'structure', 'members' => [ 'MetricName' => [ 'shape' => 'MetricName', ], 'Value' => [ 'shape' => 'Float', ], 'Timestamp' => [ 'shape' => 'Timestamp', ], ], ], 'MetricDefinition' => [ 'type' => 'structure', 'required' => [ 'Name', 'Regex', ], 'members' => [ 'Name' => [ 'shape' => 'MetricName', ], 'Regex' => [ 'shape' => 'MetricRegex', ], ], ], 'MetricDefinitionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricDefinition', ], 'max' => 40, 'min' => 0, ], 'MetricName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '.+', ], 'MetricRegex' => [ 'type' => 'string', 'max' => 500, 'min' => 1, 'pattern' => '.+', ], 'MetricValue' => [ 'type' => 'float', ], 'MetricsSource' => [ 'type' => 'structure', 'required' => [ 'ContentType', 'S3Uri', ], 'members' => [ 'ContentType' => [ 'shape' => 'ContentType', ], 'ContentDigest' => [ 'shape' => 'ContentDigest', ], 'S3Uri' => [ 'shape' => 'S3Uri', ], ], ], 'ModelApprovalStatus' => [ 'type' => 'string', 'enum' => [ 'Approved', 'Rejected', 'PendingManualApproval', ], ], 'ModelArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:model/.*', ], 'ModelArtifacts' => [ 'type' => 'structure', 'required' => [ 'S3ModelArtifacts', ], 'members' => [ 'S3ModelArtifacts' => [ 'shape' => 'S3Uri', ], ], ], 'ModelBiasAppSpecification' => [ 'type' => 'structure', 'required' => [ 'ImageUri', 'ConfigUri', ], 'members' => [ 'ImageUri' => [ 'shape' => 'ImageUri', ], 'ConfigUri' => [ 'shape' => 'S3Uri', ], 'Environment' => [ 'shape' => 'MonitoringEnvironmentMap', ], ], ], 'ModelBiasBaselineConfig' => [ 'type' => 'structure', 'members' => [ 'BaseliningJobName' => [ 'shape' => 'ProcessingJobName', ], 'ConstraintsResource' => [ 'shape' => 'MonitoringConstraintsResource', ], ], ], 'ModelBiasJobInput' => [ 'type' => 'structure', 'required' => [ 'EndpointInput', 'GroundTruthS3Input', ], 'members' => [ 'EndpointInput' => [ 'shape' => 'EndpointInput', ], 'GroundTruthS3Input' => [ 'shape' => 'MonitoringGroundTruthS3Input', ], ], ], 'ModelCacheSetting' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'ModelClientConfig' => [ 'type' => 'structure', 'members' => [ 'InvocationsTimeoutInSeconds' => [ 'shape' => 'InvocationsTimeoutInSeconds', ], 'InvocationsMaxRetries' => [ 'shape' => 'InvocationsMaxRetries', ], ], ], 'ModelDataQuality' => [ 'type' => 'structure', 'members' => [ 'Statistics' => [ 'shape' => 'MetricsSource', ], 'Constraints' => [ 'shape' => 'MetricsSource', ], ], ], 'ModelDeployConfig' => [ 'type' => 'structure', 'members' => [ 'AutoGenerateEndpointName' => [ 'shape' => 'AutoGenerateEndpointName', ], 'EndpointName' => [ 'shape' => 'EndpointName', ], ], ], 'ModelDeployResult' => [ 'type' => 'structure', 'members' => [ 'EndpointName' => [ 'shape' => 'EndpointName', ], ], ], 'ModelDigests' => [ 'type' => 'structure', 'members' => [ 'ArtifactDigest' => [ 'shape' => 'ArtifactDigest', ], ], ], 'ModelExplainabilityAppSpecification' => [ 'type' => 'structure', 'required' => [ 'ImageUri', 'ConfigUri', ], 'members' => [ 'ImageUri' => [ 'shape' => 'ImageUri', ], 'ConfigUri' => [ 'shape' => 'S3Uri', ], 'Environment' => [ 'shape' => 'MonitoringEnvironmentMap', ], ], ], 'ModelExplainabilityBaselineConfig' => [ 'type' => 'structure', 'members' => [ 'BaseliningJobName' => [ 'shape' => 'ProcessingJobName', ], 'ConstraintsResource' => [ 'shape' => 'MonitoringConstraintsResource', ], ], ], 'ModelExplainabilityJobInput' => [ 'type' => 'structure', 'required' => [ 'EndpointInput', ], 'members' => [ 'EndpointInput' => [ 'shape' => 'EndpointInput', ], ], ], 'ModelMetrics' => [ 'type' => 'structure', 'members' => [ 'ModelQuality' => [ 'shape' => 'ModelQuality', ], 'ModelDataQuality' => [ 'shape' => 'ModelDataQuality', ], 'Bias' => [ 'shape' => 'Bias', ], 'Explainability' => [ 'shape' => 'Explainability', ], ], ], 'ModelName' => [ 'type' => 'string', 'max' => 63, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9])*', ], 'ModelNameContains' => [ 'type' => 'string', 'max' => 63, 'pattern' => '[a-zA-Z0-9-]+', ], 'ModelPackage' => [ 'type' => 'structure', 'members' => [ 'ModelPackageName' => [ 'shape' => 'EntityName', ], 'ModelPackageGroupName' => [ 'shape' => 'EntityName', ], 'ModelPackageVersion' => [ 'shape' => 'ModelPackageVersion', ], 'ModelPackageArn' => [ 'shape' => 'ModelPackageArn', ], 'ModelPackageDescription' => [ 'shape' => 'EntityDescription', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'InferenceSpecification' => [ 'shape' => 'InferenceSpecification', ], 'SourceAlgorithmSpecification' => [ 'shape' => 'SourceAlgorithmSpecification', ], 'ValidationSpecification' => [ 'shape' => 'ModelPackageValidationSpecification', ], 'ModelPackageStatus' => [ 'shape' => 'ModelPackageStatus', ], 'ModelPackageStatusDetails' => [ 'shape' => 'ModelPackageStatusDetails', ], 'CertifyForMarketplace' => [ 'shape' => 'CertifyForMarketplace', ], 'ModelApprovalStatus' => [ 'shape' => 'ModelApprovalStatus', ], 'CreatedBy' => [ 'shape' => 'UserContext', ], 'MetadataProperties' => [ 'shape' => 'MetadataProperties', ], 'ModelMetrics' => [ 'shape' => 'ModelMetrics', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedBy' => [ 'shape' => 'UserContext', ], 'ApprovalDescription' => [ 'shape' => 'ApprovalDescription', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ModelPackageArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:model-package/.*', ], 'ModelPackageContainerDefinition' => [ 'type' => 'structure', 'required' => [ 'Image', ], 'members' => [ 'ContainerHostname' => [ 'shape' => 'ContainerHostname', ], 'Image' => [ 'shape' => 'ContainerImage', ], 'ImageDigest' => [ 'shape' => 'ImageDigest', ], 'ModelDataUrl' => [ 'shape' => 'Url', ], 'ProductId' => [ 'shape' => 'ProductId', ], 'Environment' => [ 'shape' => 'EnvironmentMap', ], ], ], 'ModelPackageContainerDefinitionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ModelPackageContainerDefinition', ], 'max' => 5, 'min' => 1, ], 'ModelPackageGroup' => [ 'type' => 'structure', 'members' => [ 'ModelPackageGroupName' => [ 'shape' => 'EntityName', ], 'ModelPackageGroupArn' => [ 'shape' => 'ModelPackageGroupArn', ], 'ModelPackageGroupDescription' => [ 'shape' => 'EntityDescription', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'CreatedBy' => [ 'shape' => 'UserContext', ], 'ModelPackageGroupStatus' => [ 'shape' => 'ModelPackageGroupStatus', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ModelPackageGroupArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:model-package-group/.*', ], 'ModelPackageGroupSortBy' => [ 'type' => 'string', 'enum' => [ 'Name', 'CreationTime', ], ], 'ModelPackageGroupStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'InProgress', 'Completed', 'Failed', 'Deleting', 'DeleteFailed', ], ], 'ModelPackageGroupSummary' => [ 'type' => 'structure', 'required' => [ 'ModelPackageGroupName', 'ModelPackageGroupArn', 'CreationTime', 'ModelPackageGroupStatus', ], 'members' => [ 'ModelPackageGroupName' => [ 'shape' => 'EntityName', ], 'ModelPackageGroupArn' => [ 'shape' => 'ModelPackageGroupArn', ], 'ModelPackageGroupDescription' => [ 'shape' => 'EntityDescription', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'ModelPackageGroupStatus' => [ 'shape' => 'ModelPackageGroupStatus', ], ], ], 'ModelPackageGroupSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ModelPackageGroupSummary', ], ], 'ModelPackageSortBy' => [ 'type' => 'string', 'enum' => [ 'Name', 'CreationTime', ], ], 'ModelPackageStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'InProgress', 'Completed', 'Failed', 'Deleting', ], ], 'ModelPackageStatusDetails' => [ 'type' => 'structure', 'required' => [ 'ValidationStatuses', ], 'members' => [ 'ValidationStatuses' => [ 'shape' => 'ModelPackageStatusItemList', ], 'ImageScanStatuses' => [ 'shape' => 'ModelPackageStatusItemList', ], ], ], 'ModelPackageStatusItem' => [ 'type' => 'structure', 'required' => [ 'Name', 'Status', ], 'members' => [ 'Name' => [ 'shape' => 'EntityName', ], 'Status' => [ 'shape' => 'DetailedModelPackageStatus', ], 'FailureReason' => [ 'shape' => 'String', ], ], ], 'ModelPackageStatusItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ModelPackageStatusItem', ], ], 'ModelPackageSummary' => [ 'type' => 'structure', 'required' => [ 'ModelPackageName', 'ModelPackageArn', 'CreationTime', 'ModelPackageStatus', ], 'members' => [ 'ModelPackageName' => [ 'shape' => 'EntityName', ], 'ModelPackageGroupName' => [ 'shape' => 'EntityName', ], 'ModelPackageVersion' => [ 'shape' => 'ModelPackageVersion', ], 'ModelPackageArn' => [ 'shape' => 'ModelPackageArn', ], 'ModelPackageDescription' => [ 'shape' => 'EntityDescription', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'ModelPackageStatus' => [ 'shape' => 'ModelPackageStatus', ], 'ModelApprovalStatus' => [ 'shape' => 'ModelApprovalStatus', ], ], ], 'ModelPackageSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ModelPackageSummary', ], ], 'ModelPackageType' => [ 'type' => 'string', 'enum' => [ 'Versioned', 'Unversioned', 'Both', ], ], 'ModelPackageValidationProfile' => [ 'type' => 'structure', 'required' => [ 'ProfileName', 'TransformJobDefinition', ], 'members' => [ 'ProfileName' => [ 'shape' => 'EntityName', ], 'TransformJobDefinition' => [ 'shape' => 'TransformJobDefinition', ], ], ], 'ModelPackageValidationProfiles' => [ 'type' => 'list', 'member' => [ 'shape' => 'ModelPackageValidationProfile', ], 'max' => 1, 'min' => 1, ], 'ModelPackageValidationSpecification' => [ 'type' => 'structure', 'required' => [ 'ValidationRole', 'ValidationProfiles', ], 'members' => [ 'ValidationRole' => [ 'shape' => 'RoleArn', ], 'ValidationProfiles' => [ 'shape' => 'ModelPackageValidationProfiles', ], ], ], 'ModelPackageVersion' => [ 'type' => 'integer', 'min' => 1, ], 'ModelQuality' => [ 'type' => 'structure', 'members' => [ 'Statistics' => [ 'shape' => 'MetricsSource', ], 'Constraints' => [ 'shape' => 'MetricsSource', ], ], ], 'ModelQualityAppSpecification' => [ 'type' => 'structure', 'required' => [ 'ImageUri', ], 'members' => [ 'ImageUri' => [ 'shape' => 'ImageUri', ], 'ContainerEntrypoint' => [ 'shape' => 'ContainerEntrypoint', ], 'ContainerArguments' => [ 'shape' => 'MonitoringContainerArguments', ], 'RecordPreprocessorSourceUri' => [ 'shape' => 'S3Uri', ], 'PostAnalyticsProcessorSourceUri' => [ 'shape' => 'S3Uri', ], 'ProblemType' => [ 'shape' => 'MonitoringProblemType', ], 'Environment' => [ 'shape' => 'MonitoringEnvironmentMap', ], ], ], 'ModelQualityBaselineConfig' => [ 'type' => 'structure', 'members' => [ 'BaseliningJobName' => [ 'shape' => 'ProcessingJobName', ], 'ConstraintsResource' => [ 'shape' => 'MonitoringConstraintsResource', ], ], ], 'ModelQualityJobInput' => [ 'type' => 'structure', 'required' => [ 'EndpointInput', 'GroundTruthS3Input', ], 'members' => [ 'EndpointInput' => [ 'shape' => 'EndpointInput', ], 'GroundTruthS3Input' => [ 'shape' => 'MonitoringGroundTruthS3Input', ], ], ], 'ModelSortKey' => [ 'type' => 'string', 'enum' => [ 'Name', 'CreationTime', ], ], 'ModelStepMetadata' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'String256', ], ], ], 'ModelSummary' => [ 'type' => 'structure', 'required' => [ 'ModelName', 'ModelArn', 'CreationTime', ], 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'ModelArn' => [ 'shape' => 'ModelArn', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], ], ], 'ModelSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ModelSummary', ], ], 'MonitoringAppSpecification' => [ 'type' => 'structure', 'required' => [ 'ImageUri', ], 'members' => [ 'ImageUri' => [ 'shape' => 'ImageUri', ], 'ContainerEntrypoint' => [ 'shape' => 'ContainerEntrypoint', ], 'ContainerArguments' => [ 'shape' => 'MonitoringContainerArguments', ], 'RecordPreprocessorSourceUri' => [ 'shape' => 'S3Uri', ], 'PostAnalyticsProcessorSourceUri' => [ 'shape' => 'S3Uri', ], ], ], 'MonitoringBaselineConfig' => [ 'type' => 'structure', 'members' => [ 'BaseliningJobName' => [ 'shape' => 'ProcessingJobName', ], 'ConstraintsResource' => [ 'shape' => 'MonitoringConstraintsResource', ], 'StatisticsResource' => [ 'shape' => 'MonitoringStatisticsResource', ], ], ], 'MonitoringClusterConfig' => [ 'type' => 'structure', 'required' => [ 'InstanceCount', 'InstanceType', 'VolumeSizeInGB', ], 'members' => [ 'InstanceCount' => [ 'shape' => 'ProcessingInstanceCount', ], 'InstanceType' => [ 'shape' => 'ProcessingInstanceType', ], 'VolumeSizeInGB' => [ 'shape' => 'ProcessingVolumeSizeInGB', ], 'VolumeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'MonitoringConstraintsResource' => [ 'type' => 'structure', 'members' => [ 'S3Uri' => [ 'shape' => 'S3Uri', ], ], ], 'MonitoringContainerArguments' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerArgument', ], 'max' => 50, 'min' => 1, ], 'MonitoringEnvironmentMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ProcessingEnvironmentKey', ], 'value' => [ 'shape' => 'ProcessingEnvironmentValue', ], 'max' => 50, ], 'MonitoringExecutionSortKey' => [ 'type' => 'string', 'enum' => [ 'CreationTime', 'ScheduledTime', 'Status', ], ], 'MonitoringExecutionSummary' => [ 'type' => 'structure', 'required' => [ 'MonitoringScheduleName', 'ScheduledTime', 'CreationTime', 'LastModifiedTime', 'MonitoringExecutionStatus', ], 'members' => [ 'MonitoringScheduleName' => [ 'shape' => 'MonitoringScheduleName', ], 'ScheduledTime' => [ 'shape' => 'Timestamp', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'MonitoringExecutionStatus' => [ 'shape' => 'ExecutionStatus', ], 'ProcessingJobArn' => [ 'shape' => 'ProcessingJobArn', ], 'EndpointName' => [ 'shape' => 'EndpointName', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'MonitoringJobDefinitionName' => [ 'shape' => 'MonitoringJobDefinitionName', ], 'MonitoringType' => [ 'shape' => 'MonitoringType', ], ], ], 'MonitoringExecutionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MonitoringExecutionSummary', ], ], 'MonitoringGroundTruthS3Input' => [ 'type' => 'structure', 'members' => [ 'S3Uri' => [ 'shape' => 'MonitoringS3Uri', ], ], ], 'MonitoringInput' => [ 'type' => 'structure', 'required' => [ 'EndpointInput', ], 'members' => [ 'EndpointInput' => [ 'shape' => 'EndpointInput', ], ], ], 'MonitoringInputs' => [ 'type' => 'list', 'member' => [ 'shape' => 'MonitoringInput', ], 'max' => 1, 'min' => 1, ], 'MonitoringJobDefinition' => [ 'type' => 'structure', 'required' => [ 'MonitoringInputs', 'MonitoringOutputConfig', 'MonitoringResources', 'MonitoringAppSpecification', 'RoleArn', ], 'members' => [ 'BaselineConfig' => [ 'shape' => 'MonitoringBaselineConfig', ], 'MonitoringInputs' => [ 'shape' => 'MonitoringInputs', ], 'MonitoringOutputConfig' => [ 'shape' => 'MonitoringOutputConfig', ], 'MonitoringResources' => [ 'shape' => 'MonitoringResources', ], 'MonitoringAppSpecification' => [ 'shape' => 'MonitoringAppSpecification', ], 'StoppingCondition' => [ 'shape' => 'MonitoringStoppingCondition', ], 'Environment' => [ 'shape' => 'MonitoringEnvironmentMap', ], 'NetworkConfig' => [ 'shape' => 'NetworkConfig', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], ], ], 'MonitoringJobDefinitionArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => '.*', ], 'MonitoringJobDefinitionName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9])*$', ], 'MonitoringJobDefinitionSortKey' => [ 'type' => 'string', 'enum' => [ 'Name', 'CreationTime', ], ], 'MonitoringJobDefinitionSummary' => [ 'type' => 'structure', 'required' => [ 'MonitoringJobDefinitionName', 'MonitoringJobDefinitionArn', 'CreationTime', 'EndpointName', ], 'members' => [ 'MonitoringJobDefinitionName' => [ 'shape' => 'MonitoringJobDefinitionName', ], 'MonitoringJobDefinitionArn' => [ 'shape' => 'MonitoringJobDefinitionArn', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'EndpointName' => [ 'shape' => 'EndpointName', ], ], ], 'MonitoringJobDefinitionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MonitoringJobDefinitionSummary', ], ], 'MonitoringMaxRuntimeInSeconds' => [ 'type' => 'integer', 'max' => 86400, 'min' => 1, ], 'MonitoringNetworkConfig' => [ 'type' => 'structure', 'members' => [ 'EnableInterContainerTrafficEncryption' => [ 'shape' => 'Boolean', ], 'EnableNetworkIsolation' => [ 'shape' => 'Boolean', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], ], ], 'MonitoringOutput' => [ 'type' => 'structure', 'required' => [ 'S3Output', ], 'members' => [ 'S3Output' => [ 'shape' => 'MonitoringS3Output', ], ], ], 'MonitoringOutputConfig' => [ 'type' => 'structure', 'required' => [ 'MonitoringOutputs', ], 'members' => [ 'MonitoringOutputs' => [ 'shape' => 'MonitoringOutputs', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'MonitoringOutputs' => [ 'type' => 'list', 'member' => [ 'shape' => 'MonitoringOutput', ], 'max' => 1, 'min' => 1, ], 'MonitoringProblemType' => [ 'type' => 'string', 'enum' => [ 'BinaryClassification', 'MulticlassClassification', 'Regression', ], ], 'MonitoringResources' => [ 'type' => 'structure', 'required' => [ 'ClusterConfig', ], 'members' => [ 'ClusterConfig' => [ 'shape' => 'MonitoringClusterConfig', ], ], ], 'MonitoringS3Output' => [ 'type' => 'structure', 'required' => [ 'S3Uri', 'LocalPath', ], 'members' => [ 'S3Uri' => [ 'shape' => 'MonitoringS3Uri', ], 'LocalPath' => [ 'shape' => 'ProcessingLocalPath', ], 'S3UploadMode' => [ 'shape' => 'ProcessingS3UploadMode', ], ], ], 'MonitoringS3Uri' => [ 'type' => 'string', 'max' => 512, 'pattern' => '^(https|s3)://([^/]+)/?(.*)$', ], 'MonitoringSchedule' => [ 'type' => 'structure', 'members' => [ 'MonitoringScheduleArn' => [ 'shape' => 'MonitoringScheduleArn', ], 'MonitoringScheduleName' => [ 'shape' => 'MonitoringScheduleName', ], 'MonitoringScheduleStatus' => [ 'shape' => 'ScheduleStatus', ], 'MonitoringType' => [ 'shape' => 'MonitoringType', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'MonitoringScheduleConfig' => [ 'shape' => 'MonitoringScheduleConfig', ], 'EndpointName' => [ 'shape' => 'EndpointName', ], 'LastMonitoringExecutionSummary' => [ 'shape' => 'MonitoringExecutionSummary', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'MonitoringScheduleArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => '.*', ], 'MonitoringScheduleConfig' => [ 'type' => 'structure', 'members' => [ 'ScheduleConfig' => [ 'shape' => 'ScheduleConfig', ], 'MonitoringJobDefinition' => [ 'shape' => 'MonitoringJobDefinition', ], 'MonitoringJobDefinitionName' => [ 'shape' => 'MonitoringJobDefinitionName', ], 'MonitoringType' => [ 'shape' => 'MonitoringType', ], ], ], 'MonitoringScheduleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MonitoringSchedule', ], ], 'MonitoringScheduleName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,62}$', ], 'MonitoringScheduleSortKey' => [ 'type' => 'string', 'enum' => [ 'Name', 'CreationTime', 'Status', ], ], 'MonitoringScheduleSummary' => [ 'type' => 'structure', 'required' => [ 'MonitoringScheduleName', 'MonitoringScheduleArn', 'CreationTime', 'LastModifiedTime', 'MonitoringScheduleStatus', ], 'members' => [ 'MonitoringScheduleName' => [ 'shape' => 'MonitoringScheduleName', ], 'MonitoringScheduleArn' => [ 'shape' => 'MonitoringScheduleArn', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'MonitoringScheduleStatus' => [ 'shape' => 'ScheduleStatus', ], 'EndpointName' => [ 'shape' => 'EndpointName', ], 'MonitoringJobDefinitionName' => [ 'shape' => 'MonitoringJobDefinitionName', ], 'MonitoringType' => [ 'shape' => 'MonitoringType', ], ], ], 'MonitoringScheduleSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MonitoringScheduleSummary', ], ], 'MonitoringStatisticsResource' => [ 'type' => 'structure', 'members' => [ 'S3Uri' => [ 'shape' => 'S3Uri', ], ], ], 'MonitoringStoppingCondition' => [ 'type' => 'structure', 'required' => [ 'MaxRuntimeInSeconds', ], 'members' => [ 'MaxRuntimeInSeconds' => [ 'shape' => 'MonitoringMaxRuntimeInSeconds', ], ], ], 'MonitoringTimeOffsetString' => [ 'type' => 'string', 'max' => 15, 'min' => 1, 'pattern' => '^.?P.*', ], 'MonitoringType' => [ 'type' => 'string', 'enum' => [ 'DataQuality', 'ModelQuality', 'ModelBias', 'ModelExplainability', ], ], 'MountPath' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '^\\/.*', ], 'MultiModelConfig' => [ 'type' => 'structure', 'members' => [ 'ModelCacheSetting' => [ 'shape' => 'ModelCacheSetting', ], ], ], 'NameContains' => [ 'type' => 'string', 'max' => 63, 'pattern' => '[a-zA-Z0-9\\-]+', ], 'NeoVpcConfig' => [ 'type' => 'structure', 'required' => [ 'SecurityGroupIds', 'Subnets', ], 'members' => [ 'SecurityGroupIds' => [ 'shape' => 'NeoVpcSecurityGroupIds', ], 'Subnets' => [ 'shape' => 'NeoVpcSubnets', ], ], ], 'NeoVpcSecurityGroupId' => [ 'type' => 'string', 'max' => 32, 'pattern' => '[-0-9a-zA-Z]+', ], 'NeoVpcSecurityGroupIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'NeoVpcSecurityGroupId', ], 'max' => 5, 'min' => 1, ], 'NeoVpcSubnetId' => [ 'type' => 'string', 'max' => 32, 'pattern' => '[-0-9a-zA-Z]+', ], 'NeoVpcSubnets' => [ 'type' => 'list', 'member' => [ 'shape' => 'NeoVpcSubnetId', ], 'max' => 16, 'min' => 1, ], 'NestedFilters' => [ 'type' => 'structure', 'required' => [ 'NestedPropertyName', 'Filters', ], 'members' => [ 'NestedPropertyName' => [ 'shape' => 'ResourcePropertyName', ], 'Filters' => [ 'shape' => 'FilterList', ], ], ], 'NestedFiltersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NestedFilters', ], 'max' => 20, 'min' => 1, ], 'NetworkConfig' => [ 'type' => 'structure', 'members' => [ 'EnableInterContainerTrafficEncryption' => [ 'shape' => 'Boolean', ], 'EnableNetworkIsolation' => [ 'shape' => 'Boolean', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], ], ], 'NetworkInterfaceId' => [ 'type' => 'string', ], 'NextToken' => [ 'type' => 'string', 'max' => 8192, 'pattern' => '.*', ], 'NotebookInstanceAcceleratorType' => [ 'type' => 'string', 'enum' => [ 'ml.eia1.medium', 'ml.eia1.large', 'ml.eia1.xlarge', 'ml.eia2.medium', 'ml.eia2.large', 'ml.eia2.xlarge', ], ], 'NotebookInstanceAcceleratorTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'NotebookInstanceAcceleratorType', ], ], 'NotebookInstanceArn' => [ 'type' => 'string', 'max' => 256, ], 'NotebookInstanceLifecycleConfigArn' => [ 'type' => 'string', 'max' => 256, ], 'NotebookInstanceLifecycleConfigContent' => [ 'type' => 'string', 'max' => 16384, 'min' => 1, 'pattern' => '[\\S\\s]+', ], 'NotebookInstanceLifecycleConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NotebookInstanceLifecycleHook', ], 'max' => 1, ], 'NotebookInstanceLifecycleConfigName' => [ 'type' => 'string', 'max' => 63, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9])*', ], 'NotebookInstanceLifecycleConfigNameContains' => [ 'type' => 'string', 'max' => 63, 'pattern' => '[a-zA-Z0-9-]+', ], 'NotebookInstanceLifecycleConfigSortKey' => [ 'type' => 'string', 'enum' => [ 'Name', 'CreationTime', 'LastModifiedTime', ], ], 'NotebookInstanceLifecycleConfigSortOrder' => [ 'type' => 'string', 'enum' => [ 'Ascending', 'Descending', ], ], 'NotebookInstanceLifecycleConfigSummary' => [ 'type' => 'structure', 'required' => [ 'NotebookInstanceLifecycleConfigName', 'NotebookInstanceLifecycleConfigArn', ], 'members' => [ 'NotebookInstanceLifecycleConfigName' => [ 'shape' => 'NotebookInstanceLifecycleConfigName', ], 'NotebookInstanceLifecycleConfigArn' => [ 'shape' => 'NotebookInstanceLifecycleConfigArn', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'LastModifiedTime' => [ 'shape' => 'LastModifiedTime', ], ], ], 'NotebookInstanceLifecycleConfigSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NotebookInstanceLifecycleConfigSummary', ], ], 'NotebookInstanceLifecycleHook' => [ 'type' => 'structure', 'members' => [ 'Content' => [ 'shape' => 'NotebookInstanceLifecycleConfigContent', ], ], ], 'NotebookInstanceName' => [ 'type' => 'string', 'max' => 63, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9])*', ], 'NotebookInstanceNameContains' => [ 'type' => 'string', 'max' => 63, 'pattern' => '[a-zA-Z0-9-]+', ], 'NotebookInstanceSortKey' => [ 'type' => 'string', 'enum' => [ 'Name', 'CreationTime', 'Status', ], ], 'NotebookInstanceSortOrder' => [ 'type' => 'string', 'enum' => [ 'Ascending', 'Descending', ], ], 'NotebookInstanceStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'InService', 'Stopping', 'Stopped', 'Failed', 'Deleting', 'Updating', ], ], 'NotebookInstanceSummary' => [ 'type' => 'structure', 'required' => [ 'NotebookInstanceName', 'NotebookInstanceArn', ], 'members' => [ 'NotebookInstanceName' => [ 'shape' => 'NotebookInstanceName', ], 'NotebookInstanceArn' => [ 'shape' => 'NotebookInstanceArn', ], 'NotebookInstanceStatus' => [ 'shape' => 'NotebookInstanceStatus', ], 'Url' => [ 'shape' => 'NotebookInstanceUrl', ], 'InstanceType' => [ 'shape' => 'InstanceType', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'LastModifiedTime' => [ 'shape' => 'LastModifiedTime', ], 'NotebookInstanceLifecycleConfigName' => [ 'shape' => 'NotebookInstanceLifecycleConfigName', ], 'DefaultCodeRepository' => [ 'shape' => 'CodeRepositoryNameOrUrl', ], 'AdditionalCodeRepositories' => [ 'shape' => 'AdditionalCodeRepositoryNamesOrUrls', ], ], ], 'NotebookInstanceSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NotebookInstanceSummary', ], ], 'NotebookInstanceUrl' => [ 'type' => 'string', ], 'NotebookInstanceVolumeSizeInGB' => [ 'type' => 'integer', 'max' => 16384, 'min' => 5, ], 'NotebookOutputOption' => [ 'type' => 'string', 'enum' => [ 'Allowed', 'Disabled', ], ], 'NotificationConfiguration' => [ 'type' => 'structure', 'members' => [ 'NotificationTopicArn' => [ 'shape' => 'NotificationTopicArn', ], ], ], 'NotificationTopicArn' => [ 'type' => 'string', 'pattern' => 'arn:aws[a-z\\-]*:sns:[a-z0-9\\-]*:[0-9]{12}:[a-zA-Z0-9_.-]*', ], 'NumberOfHumanWorkersPerDataObject' => [ 'type' => 'integer', 'max' => 9, 'min' => 1, ], 'ObjectiveStatus' => [ 'type' => 'string', 'enum' => [ 'Succeeded', 'Pending', 'Failed', ], ], 'ObjectiveStatusCounter' => [ 'type' => 'integer', 'min' => 0, ], 'ObjectiveStatusCounters' => [ 'type' => 'structure', 'members' => [ 'Succeeded' => [ 'shape' => 'ObjectiveStatusCounter', ], 'Pending' => [ 'shape' => 'ObjectiveStatusCounter', ], 'Failed' => [ 'shape' => 'ObjectiveStatusCounter', ], ], ], 'OfflineStoreConfig' => [ 'type' => 'structure', 'required' => [ 'S3StorageConfig', ], 'members' => [ 'S3StorageConfig' => [ 'shape' => 'S3StorageConfig', ], 'DisableGlueTableCreation' => [ 'shape' => 'Boolean', ], 'DataCatalogConfig' => [ 'shape' => 'DataCatalogConfig', ], ], ], 'OfflineStoreStatus' => [ 'type' => 'structure', 'required' => [ 'Status', ], 'members' => [ 'Status' => [ 'shape' => 'OfflineStoreStatusValue', ], 'BlockedReason' => [ 'shape' => 'BlockedReason', ], ], ], 'OfflineStoreStatusValue' => [ 'type' => 'string', 'enum' => [ 'Active', 'Blocked', 'Disabled', ], ], 'OidcConfig' => [ 'type' => 'structure', 'required' => [ 'ClientId', 'ClientSecret', 'Issuer', 'AuthorizationEndpoint', 'TokenEndpoint', 'UserInfoEndpoint', 'LogoutEndpoint', 'JwksUri', ], 'members' => [ 'ClientId' => [ 'shape' => 'ClientId', ], 'ClientSecret' => [ 'shape' => 'ClientSecret', ], 'Issuer' => [ 'shape' => 'OidcEndpoint', ], 'AuthorizationEndpoint' => [ 'shape' => 'OidcEndpoint', ], 'TokenEndpoint' => [ 'shape' => 'OidcEndpoint', ], 'UserInfoEndpoint' => [ 'shape' => 'OidcEndpoint', ], 'LogoutEndpoint' => [ 'shape' => 'OidcEndpoint', ], 'JwksUri' => [ 'shape' => 'OidcEndpoint', ], ], ], 'OidcConfigForResponse' => [ 'type' => 'structure', 'members' => [ 'ClientId' => [ 'shape' => 'ClientId', ], 'Issuer' => [ 'shape' => 'OidcEndpoint', ], 'AuthorizationEndpoint' => [ 'shape' => 'OidcEndpoint', ], 'TokenEndpoint' => [ 'shape' => 'OidcEndpoint', ], 'UserInfoEndpoint' => [ 'shape' => 'OidcEndpoint', ], 'LogoutEndpoint' => [ 'shape' => 'OidcEndpoint', ], 'JwksUri' => [ 'shape' => 'OidcEndpoint', ], ], ], 'OidcEndpoint' => [ 'type' => 'string', 'max' => 500, 'pattern' => 'https://\\S+', ], 'OidcMemberDefinition' => [ 'type' => 'structure', 'required' => [ 'Groups', ], 'members' => [ 'Groups' => [ 'shape' => 'Groups', ], ], ], 'OnlineStoreConfig' => [ 'type' => 'structure', 'members' => [ 'SecurityConfig' => [ 'shape' => 'OnlineStoreSecurityConfig', ], 'EnableOnlineStore' => [ 'shape' => 'Boolean', ], ], ], 'OnlineStoreSecurityConfig' => [ 'type' => 'structure', 'members' => [ 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'Operator' => [ 'type' => 'string', 'enum' => [ 'Equals', 'NotEquals', 'GreaterThan', 'GreaterThanOrEqualTo', 'LessThan', 'LessThanOrEqualTo', 'Contains', 'Exists', 'NotExists', 'In', ], ], 'OptionalDouble' => [ 'type' => 'double', ], 'OptionalInteger' => [ 'type' => 'integer', ], 'OptionalVolumeSizeInGB' => [ 'type' => 'integer', 'min' => 0, ], 'OrderKey' => [ 'type' => 'string', 'enum' => [ 'Ascending', 'Descending', ], ], 'OutputConfig' => [ 'type' => 'structure', 'required' => [ 'S3OutputLocation', ], 'members' => [ 'S3OutputLocation' => [ 'shape' => 'S3Uri', ], 'TargetDevice' => [ 'shape' => 'TargetDevice', ], 'TargetPlatform' => [ 'shape' => 'TargetPlatform', ], 'CompilerOptions' => [ 'shape' => 'CompilerOptions', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'OutputDataConfig' => [ 'type' => 'structure', 'required' => [ 'S3OutputPath', ], 'members' => [ 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'S3OutputPath' => [ 'shape' => 'S3Uri', ], ], ], 'OutputParameter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Value', ], 'members' => [ 'Name' => [ 'shape' => 'String256', ], 'Value' => [ 'shape' => 'String1024', ], ], ], 'OutputParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutputParameter', ], 'max' => 50, 'min' => 0, ], 'PaginationToken' => [ 'type' => 'string', 'max' => 8192, 'pattern' => '.*', ], 'Parameter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Value', ], 'members' => [ 'Name' => [ 'shape' => 'PipelineParameterName', ], 'Value' => [ 'shape' => 'String1024', ], ], ], 'ParameterKey' => [ 'type' => 'string', 'max' => 256, 'pattern' => '.*', ], 'ParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Parameter', ], 'max' => 50, 'min' => 0, ], 'ParameterName' => [ 'type' => 'string', 'max' => 256, 'pattern' => '[\\p{L}\\p{M}\\p{Z}\\p{S}\\p{N}\\p{P}]*', ], 'ParameterRange' => [ 'type' => 'structure', 'members' => [ 'IntegerParameterRangeSpecification' => [ 'shape' => 'IntegerParameterRangeSpecification', ], 'ContinuousParameterRangeSpecification' => [ 'shape' => 'ContinuousParameterRangeSpecification', ], 'CategoricalParameterRangeSpecification' => [ 'shape' => 'CategoricalParameterRangeSpecification', ], ], ], 'ParameterRanges' => [ 'type' => 'structure', 'members' => [ 'IntegerParameterRanges' => [ 'shape' => 'IntegerParameterRanges', ], 'ContinuousParameterRanges' => [ 'shape' => 'ContinuousParameterRanges', ], 'CategoricalParameterRanges' => [ 'shape' => 'CategoricalParameterRanges', ], ], ], 'ParameterType' => [ 'type' => 'string', 'enum' => [ 'Integer', 'Continuous', 'Categorical', 'FreeText', ], ], 'ParameterValue' => [ 'type' => 'string', 'max' => 256, 'pattern' => '.*', ], 'ParameterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParameterValue', ], 'max' => 20, 'min' => 1, ], 'Parent' => [ 'type' => 'structure', 'members' => [ 'TrialName' => [ 'shape' => 'ExperimentEntityName', ], 'ExperimentName' => [ 'shape' => 'ExperimentEntityName', ], ], ], 'ParentHyperParameterTuningJob' => [ 'type' => 'structure', 'members' => [ 'HyperParameterTuningJobName' => [ 'shape' => 'HyperParameterTuningJobName', ], ], ], 'ParentHyperParameterTuningJobs' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParentHyperParameterTuningJob', ], 'max' => 5, 'min' => 1, ], 'Parents' => [ 'type' => 'list', 'member' => [ 'shape' => 'Parent', ], ], 'Pipeline' => [ 'type' => 'structure', 'members' => [ 'PipelineArn' => [ 'shape' => 'PipelineArn', ], 'PipelineName' => [ 'shape' => 'PipelineName', ], 'PipelineDisplayName' => [ 'shape' => 'PipelineName', ], 'PipelineDescription' => [ 'shape' => 'PipelineDescription', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'PipelineStatus' => [ 'shape' => 'PipelineStatus', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastRunTime' => [ 'shape' => 'Timestamp', ], 'CreatedBy' => [ 'shape' => 'UserContext', ], 'LastModifiedBy' => [ 'shape' => 'UserContext', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'PipelineArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:pipeline/.*', ], 'PipelineDefinition' => [ 'type' => 'string', 'max' => 1048576, 'min' => 1, 'pattern' => '.*(?:[ \\r\\n\\t].*)*', ], 'PipelineDescription' => [ 'type' => 'string', 'max' => 3072, 'min' => 0, 'pattern' => '.*', ], 'PipelineExecution' => [ 'type' => 'structure', 'members' => [ 'PipelineArn' => [ 'shape' => 'PipelineArn', ], 'PipelineExecutionArn' => [ 'shape' => 'PipelineExecutionArn', ], 'PipelineExecutionDisplayName' => [ 'shape' => 'PipelineExecutionName', ], 'PipelineExecutionStatus' => [ 'shape' => 'PipelineExecutionStatus', ], 'PipelineExecutionDescription' => [ 'shape' => 'PipelineExecutionDescription', ], 'PipelineExperimentConfig' => [ 'shape' => 'PipelineExperimentConfig', ], 'FailureReason' => [ 'shape' => 'PipelineExecutionFailureReason', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'CreatedBy' => [ 'shape' => 'UserContext', ], 'LastModifiedBy' => [ 'shape' => 'UserContext', ], 'PipelineParameters' => [ 'shape' => 'ParameterList', ], ], ], 'PipelineExecutionArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:pipeline\\/.*\\/execution\\/.*$', ], 'PipelineExecutionDescription' => [ 'type' => 'string', 'max' => 3072, 'min' => 0, 'pattern' => '.*', ], 'PipelineExecutionFailureReason' => [ 'type' => 'string', 'max' => 1300, 'pattern' => '.*', ], 'PipelineExecutionName' => [ 'type' => 'string', 'max' => 82, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,81}', ], 'PipelineExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'Executing', 'Stopping', 'Stopped', 'Failed', 'Succeeded', ], ], 'PipelineExecutionStep' => [ 'type' => 'structure', 'members' => [ 'StepName' => [ 'shape' => 'StepName', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'StepStatus' => [ 'shape' => 'StepStatus', ], 'CacheHitResult' => [ 'shape' => 'CacheHitResult', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'Metadata' => [ 'shape' => 'PipelineExecutionStepMetadata', ], ], ], 'PipelineExecutionStepList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PipelineExecutionStep', ], 'max' => 100, 'min' => 0, ], 'PipelineExecutionStepMetadata' => [ 'type' => 'structure', 'members' => [ 'TrainingJob' => [ 'shape' => 'TrainingJobStepMetadata', ], 'ProcessingJob' => [ 'shape' => 'ProcessingJobStepMetadata', ], 'TransformJob' => [ 'shape' => 'TransformJobStepMetadata', ], 'Model' => [ 'shape' => 'ModelStepMetadata', ], 'RegisterModel' => [ 'shape' => 'RegisterModelStepMetadata', ], 'Condition' => [ 'shape' => 'ConditionStepMetadata', ], 'Callback' => [ 'shape' => 'CallbackStepMetadata', ], ], ], 'PipelineExecutionSummary' => [ 'type' => 'structure', 'members' => [ 'PipelineExecutionArn' => [ 'shape' => 'PipelineExecutionArn', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'PipelineExecutionStatus' => [ 'shape' => 'PipelineExecutionStatus', ], 'PipelineExecutionDescription' => [ 'shape' => 'PipelineExecutionDescription', ], 'PipelineExecutionDisplayName' => [ 'shape' => 'PipelineExecutionName', ], ], ], 'PipelineExecutionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PipelineExecutionSummary', ], 'max' => 100, 'min' => 0, ], 'PipelineExperimentConfig' => [ 'type' => 'structure', 'members' => [ 'ExperimentName' => [ 'shape' => 'ExperimentEntityName', ], 'TrialName' => [ 'shape' => 'ExperimentEntityName', ], ], ], 'PipelineName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,255}', ], 'PipelineParameterName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,255}$', ], 'PipelineStatus' => [ 'type' => 'string', 'enum' => [ 'Active', ], ], 'PipelineSummary' => [ 'type' => 'structure', 'members' => [ 'PipelineArn' => [ 'shape' => 'PipelineArn', ], 'PipelineName' => [ 'shape' => 'PipelineName', ], 'PipelineDisplayName' => [ 'shape' => 'PipelineName', ], 'PipelineDescription' => [ 'shape' => 'PipelineDescription', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastExecutionTime' => [ 'shape' => 'Timestamp', ], ], ], 'PipelineSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PipelineSummary', ], 'max' => 100, 'min' => 0, ], 'PolicyString' => [ 'type' => 'string', 'max' => 20480, 'min' => 1, 'pattern' => '.*', ], 'PresignedDomainUrl' => [ 'type' => 'string', ], 'ProbabilityThresholdAttribute' => [ 'type' => 'double', ], 'ProblemType' => [ 'type' => 'string', 'enum' => [ 'BinaryClassification', 'MulticlassClassification', 'Regression', ], ], 'ProcessingClusterConfig' => [ 'type' => 'structure', 'required' => [ 'InstanceCount', 'InstanceType', 'VolumeSizeInGB', ], 'members' => [ 'InstanceCount' => [ 'shape' => 'ProcessingInstanceCount', ], 'InstanceType' => [ 'shape' => 'ProcessingInstanceType', ], 'VolumeSizeInGB' => [ 'shape' => 'ProcessingVolumeSizeInGB', ], 'VolumeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'ProcessingEnvironmentKey' => [ 'type' => 'string', 'max' => 256, 'pattern' => '[a-zA-Z_][a-zA-Z0-9_]*', ], 'ProcessingEnvironmentMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ProcessingEnvironmentKey', ], 'value' => [ 'shape' => 'ProcessingEnvironmentValue', ], 'max' => 100, ], 'ProcessingEnvironmentValue' => [ 'type' => 'string', 'max' => 256, 'pattern' => '[\\S\\s]*', ], 'ProcessingFeatureStoreOutput' => [ 'type' => 'structure', 'required' => [ 'FeatureGroupName', ], 'members' => [ 'FeatureGroupName' => [ 'shape' => 'FeatureGroupName', ], ], ], 'ProcessingInput' => [ 'type' => 'structure', 'required' => [ 'InputName', ], 'members' => [ 'InputName' => [ 'shape' => 'String', ], 'AppManaged' => [ 'shape' => 'AppManaged', ], 'S3Input' => [ 'shape' => 'ProcessingS3Input', ], 'DatasetDefinition' => [ 'shape' => 'DatasetDefinition', ], ], ], 'ProcessingInputs' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProcessingInput', ], 'max' => 10, 'min' => 0, ], 'ProcessingInstanceCount' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'ProcessingInstanceType' => [ 'type' => 'string', 'enum' => [ 'ml.t3.medium', 'ml.t3.large', 'ml.t3.xlarge', 'ml.t3.2xlarge', 'ml.m4.xlarge', 'ml.m4.2xlarge', 'ml.m4.4xlarge', 'ml.m4.10xlarge', 'ml.m4.16xlarge', 'ml.c4.xlarge', 'ml.c4.2xlarge', 'ml.c4.4xlarge', 'ml.c4.8xlarge', 'ml.p2.xlarge', 'ml.p2.8xlarge', 'ml.p2.16xlarge', 'ml.p3.2xlarge', 'ml.p3.8xlarge', 'ml.p3.16xlarge', 'ml.c5.xlarge', 'ml.c5.2xlarge', 'ml.c5.4xlarge', 'ml.c5.9xlarge', 'ml.c5.18xlarge', 'ml.m5.large', 'ml.m5.xlarge', 'ml.m5.2xlarge', 'ml.m5.4xlarge', 'ml.m5.12xlarge', 'ml.m5.24xlarge', 'ml.r5.large', 'ml.r5.xlarge', 'ml.r5.2xlarge', 'ml.r5.4xlarge', 'ml.r5.8xlarge', 'ml.r5.12xlarge', 'ml.r5.16xlarge', 'ml.r5.24xlarge', 'ml.g4dn.xlarge', 'ml.g4dn.2xlarge', 'ml.g4dn.4xlarge', 'ml.g4dn.8xlarge', 'ml.g4dn.12xlarge', 'ml.g4dn.16xlarge', ], ], 'ProcessingJob' => [ 'type' => 'structure', 'members' => [ 'ProcessingInputs' => [ 'shape' => 'ProcessingInputs', ], 'ProcessingOutputConfig' => [ 'shape' => 'ProcessingOutputConfig', ], 'ProcessingJobName' => [ 'shape' => 'ProcessingJobName', ], 'ProcessingResources' => [ 'shape' => 'ProcessingResources', ], 'StoppingCondition' => [ 'shape' => 'ProcessingStoppingCondition', ], 'AppSpecification' => [ 'shape' => 'AppSpecification', ], 'Environment' => [ 'shape' => 'ProcessingEnvironmentMap', ], 'NetworkConfig' => [ 'shape' => 'NetworkConfig', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'ExperimentConfig' => [ 'shape' => 'ExperimentConfig', ], 'ProcessingJobArn' => [ 'shape' => 'ProcessingJobArn', ], 'ProcessingJobStatus' => [ 'shape' => 'ProcessingJobStatus', ], 'ExitMessage' => [ 'shape' => 'ExitMessage', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'ProcessingEndTime' => [ 'shape' => 'Timestamp', ], 'ProcessingStartTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'MonitoringScheduleArn' => [ 'shape' => 'MonitoringScheduleArn', ], 'AutoMLJobArn' => [ 'shape' => 'AutoMLJobArn', ], 'TrainingJobArn' => [ 'shape' => 'TrainingJobArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ProcessingJobArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:processing-job/.*', ], 'ProcessingJobName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,62}', ], 'ProcessingJobStatus' => [ 'type' => 'string', 'enum' => [ 'InProgress', 'Completed', 'Failed', 'Stopping', 'Stopped', ], ], 'ProcessingJobStepMetadata' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ProcessingJobArn', ], ], ], 'ProcessingJobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProcessingJobSummary', ], ], 'ProcessingJobSummary' => [ 'type' => 'structure', 'required' => [ 'ProcessingJobName', 'ProcessingJobArn', 'CreationTime', 'ProcessingJobStatus', ], 'members' => [ 'ProcessingJobName' => [ 'shape' => 'ProcessingJobName', ], 'ProcessingJobArn' => [ 'shape' => 'ProcessingJobArn', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'ProcessingEndTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'ProcessingJobStatus' => [ 'shape' => 'ProcessingJobStatus', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'ExitMessage' => [ 'shape' => 'ExitMessage', ], ], ], 'ProcessingLocalPath' => [ 'type' => 'string', 'max' => 256, 'pattern' => '.*', ], 'ProcessingMaxRuntimeInSeconds' => [ 'type' => 'integer', 'max' => 604800, 'min' => 1, ], 'ProcessingOutput' => [ 'type' => 'structure', 'required' => [ 'OutputName', ], 'members' => [ 'OutputName' => [ 'shape' => 'String', ], 'S3Output' => [ 'shape' => 'ProcessingS3Output', ], 'FeatureStoreOutput' => [ 'shape' => 'ProcessingFeatureStoreOutput', ], 'AppManaged' => [ 'shape' => 'AppManaged', ], ], ], 'ProcessingOutputConfig' => [ 'type' => 'structure', 'required' => [ 'Outputs', ], 'members' => [ 'Outputs' => [ 'shape' => 'ProcessingOutputs', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'ProcessingOutputs' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProcessingOutput', ], 'max' => 10, 'min' => 0, ], 'ProcessingResources' => [ 'type' => 'structure', 'required' => [ 'ClusterConfig', ], 'members' => [ 'ClusterConfig' => [ 'shape' => 'ProcessingClusterConfig', ], ], ], 'ProcessingS3CompressionType' => [ 'type' => 'string', 'enum' => [ 'None', 'Gzip', ], ], 'ProcessingS3DataDistributionType' => [ 'type' => 'string', 'enum' => [ 'FullyReplicated', 'ShardedByS3Key', ], ], 'ProcessingS3DataType' => [ 'type' => 'string', 'enum' => [ 'ManifestFile', 'S3Prefix', ], ], 'ProcessingS3Input' => [ 'type' => 'structure', 'required' => [ 'S3Uri', 'S3DataType', ], 'members' => [ 'S3Uri' => [ 'shape' => 'S3Uri', ], 'LocalPath' => [ 'shape' => 'ProcessingLocalPath', ], 'S3DataType' => [ 'shape' => 'ProcessingS3DataType', ], 'S3InputMode' => [ 'shape' => 'ProcessingS3InputMode', ], 'S3DataDistributionType' => [ 'shape' => 'ProcessingS3DataDistributionType', ], 'S3CompressionType' => [ 'shape' => 'ProcessingS3CompressionType', ], ], ], 'ProcessingS3InputMode' => [ 'type' => 'string', 'enum' => [ 'Pipe', 'File', ], ], 'ProcessingS3Output' => [ 'type' => 'structure', 'required' => [ 'S3Uri', 'LocalPath', 'S3UploadMode', ], 'members' => [ 'S3Uri' => [ 'shape' => 'S3Uri', ], 'LocalPath' => [ 'shape' => 'ProcessingLocalPath', ], 'S3UploadMode' => [ 'shape' => 'ProcessingS3UploadMode', ], ], ], 'ProcessingS3UploadMode' => [ 'type' => 'string', 'enum' => [ 'Continuous', 'EndOfJob', ], ], 'ProcessingStoppingCondition' => [ 'type' => 'structure', 'required' => [ 'MaxRuntimeInSeconds', ], 'members' => [ 'MaxRuntimeInSeconds' => [ 'shape' => 'ProcessingMaxRuntimeInSeconds', ], ], ], 'ProcessingVolumeSizeInGB' => [ 'type' => 'integer', 'max' => 16384, 'min' => 1, ], 'ProductId' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9])*$', ], 'ProductListings' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ProductionVariant' => [ 'type' => 'structure', 'required' => [ 'VariantName', 'ModelName', 'InitialInstanceCount', 'InstanceType', ], 'members' => [ 'VariantName' => [ 'shape' => 'VariantName', ], 'ModelName' => [ 'shape' => 'ModelName', ], 'InitialInstanceCount' => [ 'shape' => 'TaskCount', ], 'InstanceType' => [ 'shape' => 'ProductionVariantInstanceType', ], 'InitialVariantWeight' => [ 'shape' => 'VariantWeight', ], 'AcceleratorType' => [ 'shape' => 'ProductionVariantAcceleratorType', ], 'CoreDumpConfig' => [ 'shape' => 'ProductionVariantCoreDumpConfig', ], ], ], 'ProductionVariantAcceleratorType' => [ 'type' => 'string', 'enum' => [ 'ml.eia1.medium', 'ml.eia1.large', 'ml.eia1.xlarge', 'ml.eia2.medium', 'ml.eia2.large', 'ml.eia2.xlarge', ], ], 'ProductionVariantCoreDumpConfig' => [ 'type' => 'structure', 'required' => [ 'DestinationS3Uri', ], 'members' => [ 'DestinationS3Uri' => [ 'shape' => 'DestinationS3Uri', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'ProductionVariantInstanceType' => [ 'type' => 'string', 'enum' => [ 'ml.t2.medium', 'ml.t2.large', 'ml.t2.xlarge', 'ml.t2.2xlarge', 'ml.m4.xlarge', 'ml.m4.2xlarge', 'ml.m4.4xlarge', 'ml.m4.10xlarge', 'ml.m4.16xlarge', 'ml.m5.large', 'ml.m5.xlarge', 'ml.m5.2xlarge', 'ml.m5.4xlarge', 'ml.m5.12xlarge', 'ml.m5.24xlarge', 'ml.m5d.large', 'ml.m5d.xlarge', 'ml.m5d.2xlarge', 'ml.m5d.4xlarge', 'ml.m5d.12xlarge', 'ml.m5d.24xlarge', 'ml.c4.large', 'ml.c4.xlarge', 'ml.c4.2xlarge', 'ml.c4.4xlarge', 'ml.c4.8xlarge', 'ml.p2.xlarge', 'ml.p2.8xlarge', 'ml.p2.16xlarge', 'ml.p3.2xlarge', 'ml.p3.8xlarge', 'ml.p3.16xlarge', 'ml.c5.large', 'ml.c5.xlarge', 'ml.c5.2xlarge', 'ml.c5.4xlarge', 'ml.c5.9xlarge', 'ml.c5.18xlarge', 'ml.c5d.large', 'ml.c5d.xlarge', 'ml.c5d.2xlarge', 'ml.c5d.4xlarge', 'ml.c5d.9xlarge', 'ml.c5d.18xlarge', 'ml.g4dn.xlarge', 'ml.g4dn.2xlarge', 'ml.g4dn.4xlarge', 'ml.g4dn.8xlarge', 'ml.g4dn.12xlarge', 'ml.g4dn.16xlarge', 'ml.r5.large', 'ml.r5.xlarge', 'ml.r5.2xlarge', 'ml.r5.4xlarge', 'ml.r5.12xlarge', 'ml.r5.24xlarge', 'ml.r5d.large', 'ml.r5d.xlarge', 'ml.r5d.2xlarge', 'ml.r5d.4xlarge', 'ml.r5d.12xlarge', 'ml.r5d.24xlarge', 'ml.inf1.xlarge', 'ml.inf1.2xlarge', 'ml.inf1.6xlarge', 'ml.inf1.24xlarge', ], ], 'ProductionVariantList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProductionVariant', ], 'max' => 10, 'min' => 1, ], 'ProductionVariantSummary' => [ 'type' => 'structure', 'required' => [ 'VariantName', ], 'members' => [ 'VariantName' => [ 'shape' => 'VariantName', ], 'DeployedImages' => [ 'shape' => 'DeployedImages', ], 'CurrentWeight' => [ 'shape' => 'VariantWeight', ], 'DesiredWeight' => [ 'shape' => 'VariantWeight', ], 'CurrentInstanceCount' => [ 'shape' => 'TaskCount', ], 'DesiredInstanceCount' => [ 'shape' => 'TaskCount', ], ], ], 'ProductionVariantSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProductionVariantSummary', ], 'min' => 1, ], 'ProfilerConfig' => [ 'type' => 'structure', 'required' => [ 'S3OutputPath', ], 'members' => [ 'S3OutputPath' => [ 'shape' => 'S3Uri', ], 'ProfilingIntervalInMilliseconds' => [ 'shape' => 'ProfilingIntervalInMilliseconds', ], 'ProfilingParameters' => [ 'shape' => 'ProfilingParameters', ], ], ], 'ProfilerConfigForUpdate' => [ 'type' => 'structure', 'members' => [ 'S3OutputPath' => [ 'shape' => 'S3Uri', ], 'ProfilingIntervalInMilliseconds' => [ 'shape' => 'ProfilingIntervalInMilliseconds', ], 'ProfilingParameters' => [ 'shape' => 'ProfilingParameters', ], 'DisableProfiler' => [ 'shape' => 'DisableProfiler', ], ], ], 'ProfilerRuleConfiguration' => [ 'type' => 'structure', 'required' => [ 'RuleConfigurationName', 'RuleEvaluatorImage', ], 'members' => [ 'RuleConfigurationName' => [ 'shape' => 'RuleConfigurationName', ], 'LocalPath' => [ 'shape' => 'DirectoryPath', ], 'S3OutputPath' => [ 'shape' => 'S3Uri', ], 'RuleEvaluatorImage' => [ 'shape' => 'AlgorithmImage', ], 'InstanceType' => [ 'shape' => 'ProcessingInstanceType', ], 'VolumeSizeInGB' => [ 'shape' => 'OptionalVolumeSizeInGB', ], 'RuleParameters' => [ 'shape' => 'RuleParameters', ], ], ], 'ProfilerRuleConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProfilerRuleConfiguration', ], 'max' => 20, 'min' => 0, ], 'ProfilerRuleEvaluationStatus' => [ 'type' => 'structure', 'members' => [ 'RuleConfigurationName' => [ 'shape' => 'RuleConfigurationName', ], 'RuleEvaluationJobArn' => [ 'shape' => 'ProcessingJobArn', ], 'RuleEvaluationStatus' => [ 'shape' => 'RuleEvaluationStatus', ], 'StatusDetails' => [ 'shape' => 'StatusDetails', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'ProfilerRuleEvaluationStatuses' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProfilerRuleEvaluationStatus', ], 'max' => 20, 'min' => 0, ], 'ProfilingIntervalInMilliseconds' => [ 'type' => 'long', ], 'ProfilingParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'ConfigKey', ], 'value' => [ 'shape' => 'ConfigValue', ], 'max' => 20, 'min' => 0, ], 'ProfilingStatus' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'ProjectArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:project:.*', ], 'ProjectEntityName' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,31}', ], 'ProjectId' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9])*', ], 'ProjectSortBy' => [ 'type' => 'string', 'enum' => [ 'Name', 'CreationTime', ], ], 'ProjectSortOrder' => [ 'type' => 'string', 'enum' => [ 'Ascending', 'Descending', ], ], 'ProjectStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'CreateInProgress', 'CreateCompleted', 'CreateFailed', 'DeleteInProgress', 'DeleteFailed', 'DeleteCompleted', ], ], 'ProjectSummary' => [ 'type' => 'structure', 'required' => [ 'ProjectName', 'ProjectArn', 'ProjectId', 'CreationTime', 'ProjectStatus', ], 'members' => [ 'ProjectName' => [ 'shape' => 'ProjectEntityName', ], 'ProjectDescription' => [ 'shape' => 'EntityDescription', ], 'ProjectArn' => [ 'shape' => 'ProjectArn', ], 'ProjectId' => [ 'shape' => 'ProjectId', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'ProjectStatus' => [ 'shape' => 'ProjectStatus', ], ], ], 'ProjectSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProjectSummary', ], ], 'PropertyNameHint' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'pattern' => '.*', ], 'PropertyNameQuery' => [ 'type' => 'structure', 'required' => [ 'PropertyNameHint', ], 'members' => [ 'PropertyNameHint' => [ 'shape' => 'PropertyNameHint', ], ], ], 'PropertyNameSuggestion' => [ 'type' => 'structure', 'members' => [ 'PropertyName' => [ 'shape' => 'ResourcePropertyName', ], ], ], 'PropertyNameSuggestionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PropertyNameSuggestion', ], ], 'ProvisionedProductStatusMessage' => [ 'type' => 'string', 'pattern' => '.*', ], 'ProvisioningParameter' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'ProvisioningParameterKey', ], 'Value' => [ 'shape' => 'ProvisioningParameterValue', ], ], ], 'ProvisioningParameterKey' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '.*', ], 'ProvisioningParameterValue' => [ 'type' => 'string', 'max' => 4096, 'pattern' => '.*', ], 'ProvisioningParameters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProvisioningParameter', ], ], 'PublicWorkforceTaskPrice' => [ 'type' => 'structure', 'members' => [ 'AmountInUsd' => [ 'shape' => 'USD', ], ], ], 'PutModelPackageGroupPolicyInput' => [ 'type' => 'structure', 'required' => [ 'ModelPackageGroupName', 'ResourcePolicy', ], 'members' => [ 'ModelPackageGroupName' => [ 'shape' => 'EntityName', ], 'ResourcePolicy' => [ 'shape' => 'PolicyString', ], ], ], 'PutModelPackageGroupPolicyOutput' => [ 'type' => 'structure', 'required' => [ 'ModelPackageGroupArn', ], 'members' => [ 'ModelPackageGroupArn' => [ 'shape' => 'ModelPackageGroupArn', ], ], ], 'RealtimeInferenceInstanceTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProductionVariantInstanceType', ], ], 'RecordWrapper' => [ 'type' => 'string', 'enum' => [ 'None', 'RecordIO', ], ], 'RedshiftClusterId' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '.*', ], 'RedshiftDatabase' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '.*', ], 'RedshiftDatasetDefinition' => [ 'type' => 'structure', 'required' => [ 'ClusterId', 'Database', 'DbUser', 'QueryString', 'ClusterRoleArn', 'OutputS3Uri', 'OutputFormat', ], 'members' => [ 'ClusterId' => [ 'shape' => 'RedshiftClusterId', ], 'Database' => [ 'shape' => 'RedshiftDatabase', ], 'DbUser' => [ 'shape' => 'RedshiftUserName', ], 'QueryString' => [ 'shape' => 'RedshiftQueryString', ], 'ClusterRoleArn' => [ 'shape' => 'RoleArn', ], 'OutputS3Uri' => [ 'shape' => 'S3Uri', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'OutputFormat' => [ 'shape' => 'RedshiftResultFormat', ], 'OutputCompression' => [ 'shape' => 'RedshiftResultCompressionType', ], ], ], 'RedshiftQueryString' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '[\\s\\S]+', ], 'RedshiftResultCompressionType' => [ 'type' => 'string', 'enum' => [ 'None', 'GZIP', 'BZIP2', 'ZSTD', 'SNAPPY', ], ], 'RedshiftResultFormat' => [ 'type' => 'string', 'enum' => [ 'PARQUET', 'CSV', ], ], 'RedshiftUserName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '.*', ], 'RegisterDevicesRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceFleetName', 'Devices', ], 'members' => [ 'DeviceFleetName' => [ 'shape' => 'EntityName', ], 'Devices' => [ 'shape' => 'Devices', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'RegisterModelStepMetadata' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'String256', ], ], ], 'RenderUiTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'Task', 'RoleArn', ], 'members' => [ 'UiTemplate' => [ 'shape' => 'UiTemplate', ], 'Task' => [ 'shape' => 'RenderableTask', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'HumanTaskUiArn' => [ 'shape' => 'HumanTaskUiArn', ], ], ], 'RenderUiTemplateResponse' => [ 'type' => 'structure', 'required' => [ 'RenderedContent', 'Errors', ], 'members' => [ 'RenderedContent' => [ 'shape' => 'String', ], 'Errors' => [ 'shape' => 'RenderingErrorList', ], ], ], 'RenderableTask' => [ 'type' => 'structure', 'required' => [ 'Input', ], 'members' => [ 'Input' => [ 'shape' => 'TaskInput', ], ], ], 'RenderingError' => [ 'type' => 'structure', 'required' => [ 'Code', 'Message', ], 'members' => [ 'Code' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], ], 'RenderingErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RenderingError', ], ], 'RepositoryAccessMode' => [ 'type' => 'string', 'enum' => [ 'Platform', 'Vpc', ], ], 'RepositoryAuthConfig' => [ 'type' => 'structure', 'required' => [ 'RepositoryCredentialsProviderArn', ], 'members' => [ 'RepositoryCredentialsProviderArn' => [ 'shape' => 'RepositoryCredentialsProviderArn', ], ], ], 'RepositoryCredentialsProviderArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '.*', ], 'ResolvedAttributes' => [ 'type' => 'structure', 'members' => [ 'AutoMLJobObjective' => [ 'shape' => 'AutoMLJobObjective', ], 'ProblemType' => [ 'shape' => 'ProblemType', ], 'CompletionCriteria' => [ 'shape' => 'AutoMLJobCompletionCriteria', ], ], ], 'ResourceArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws[a-z-]*:sagemaker:[a-z0-9-]*:[0-9]{12}:.+', ], 'ResourceConfig' => [ 'type' => 'structure', 'required' => [ 'InstanceType', 'InstanceCount', 'VolumeSizeInGB', ], 'members' => [ 'InstanceType' => [ 'shape' => 'TrainingInstanceType', ], 'InstanceCount' => [ 'shape' => 'TrainingInstanceCount', ], 'VolumeSizeInGB' => [ 'shape' => 'VolumeSizeInGB', ], 'VolumeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'ResourceId' => [ 'type' => 'string', 'max' => 32, ], 'ResourceInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'FailureReason', ], ], 'exception' => true, ], 'ResourceLimitExceeded' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'FailureReason', ], ], 'exception' => true, ], 'ResourceLimits' => [ 'type' => 'structure', 'required' => [ 'MaxNumberOfTrainingJobs', 'MaxParallelTrainingJobs', ], 'members' => [ 'MaxNumberOfTrainingJobs' => [ 'shape' => 'MaxNumberOfTrainingJobs', ], 'MaxParallelTrainingJobs' => [ 'shape' => 'MaxParallelTrainingJobs', ], ], ], 'ResourceNotFound' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'FailureReason', ], ], 'exception' => true, ], 'ResourcePropertyName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '.+', ], 'ResourceSpec' => [ 'type' => 'structure', 'members' => [ 'SageMakerImageArn' => [ 'shape' => 'ImageArn', ], 'SageMakerImageVersionArn' => [ 'shape' => 'ImageVersionArn', ], 'InstanceType' => [ 'shape' => 'AppInstanceType', ], ], ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'TrainingJob', 'Experiment', 'ExperimentTrial', 'ExperimentTrialComponent', 'Endpoint', 'ModelPackage', 'ModelPackageGroup', 'Pipeline', 'PipelineExecution', 'FeatureGroup', ], ], 'ResponseMIMEType' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '^[-\\w]+\\/.+$', ], 'ResponseMIMETypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResponseMIMEType', ], ], 'RetentionPolicy' => [ 'type' => 'structure', 'members' => [ 'HomeEfsFileSystem' => [ 'shape' => 'RetentionType', ], ], ], 'RetentionType' => [ 'type' => 'string', 'enum' => [ 'Retain', 'Delete', ], ], 'RetryStrategy' => [ 'type' => 'structure', 'required' => [ 'MaximumRetryAttempts', ], 'members' => [ 'MaximumRetryAttempts' => [ 'shape' => 'MaximumRetryAttempts', ], ], ], 'RoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:aws[a-z\\-]*:iam::\\d{12}:role/?[a-zA-Z_0-9+=,.@\\-_/]+$', ], 'RootAccess' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'RuleConfigurationName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '.*', ], 'RuleEvaluationStatus' => [ 'type' => 'string', 'enum' => [ 'InProgress', 'NoIssuesFound', 'IssuesFound', 'Error', 'Stopping', 'Stopped', ], ], 'RuleParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'ConfigKey', ], 'value' => [ 'shape' => 'ConfigValue', ], 'max' => 100, 'min' => 0, ], 'S3DataDistribution' => [ 'type' => 'string', 'enum' => [ 'FullyReplicated', 'ShardedByS3Key', ], ], 'S3DataSource' => [ 'type' => 'structure', 'required' => [ 'S3DataType', 'S3Uri', ], 'members' => [ 'S3DataType' => [ 'shape' => 'S3DataType', ], 'S3Uri' => [ 'shape' => 'S3Uri', ], 'S3DataDistributionType' => [ 'shape' => 'S3DataDistribution', ], 'AttributeNames' => [ 'shape' => 'AttributeNames', ], ], ], 'S3DataType' => [ 'type' => 'string', 'enum' => [ 'ManifestFile', 'S3Prefix', 'AugmentedManifestFile', ], ], 'S3StorageConfig' => [ 'type' => 'structure', 'required' => [ 'S3Uri', ], 'members' => [ 'S3Uri' => [ 'shape' => 'S3Uri', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'ResolvedOutputS3Uri' => [ 'shape' => 'S3Uri', ], ], ], 'S3Uri' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '^(https|s3)://([^/]+)/?(.*)$', ], 'SagemakerServicecatalogStatus' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'SamplingPercentage' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'ScheduleConfig' => [ 'type' => 'structure', 'required' => [ 'ScheduleExpression', ], 'members' => [ 'ScheduleExpression' => [ 'shape' => 'ScheduleExpression', ], ], ], 'ScheduleExpression' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'ScheduleStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'Failed', 'Scheduled', 'Stopped', ], ], 'SearchExpression' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'NestedFilters' => [ 'shape' => 'NestedFiltersList', ], 'SubExpressions' => [ 'shape' => 'SearchExpressionList', ], 'Operator' => [ 'shape' => 'BooleanOperator', ], ], ], 'SearchExpressionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchExpression', ], 'max' => 20, 'min' => 1, ], 'SearchRecord' => [ 'type' => 'structure', 'members' => [ 'TrainingJob' => [ 'shape' => 'TrainingJob', ], 'Experiment' => [ 'shape' => 'Experiment', ], 'Trial' => [ 'shape' => 'Trial', ], 'TrialComponent' => [ 'shape' => 'TrialComponent', ], 'Endpoint' => [ 'shape' => 'Endpoint', ], 'ModelPackage' => [ 'shape' => 'ModelPackage', ], 'ModelPackageGroup' => [ 'shape' => 'ModelPackageGroup', ], 'Pipeline' => [ 'shape' => 'Pipeline', ], 'PipelineExecution' => [ 'shape' => 'PipelineExecution', ], 'FeatureGroup' => [ 'shape' => 'FeatureGroup', ], ], ], 'SearchRequest' => [ 'type' => 'structure', 'required' => [ 'Resource', ], 'members' => [ 'Resource' => [ 'shape' => 'ResourceType', ], 'SearchExpression' => [ 'shape' => 'SearchExpression', ], 'SortBy' => [ 'shape' => 'ResourcePropertyName', ], 'SortOrder' => [ 'shape' => 'SearchSortOrder', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], ], ], 'SearchResponse' => [ 'type' => 'structure', 'members' => [ 'Results' => [ 'shape' => 'SearchResultsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'SearchResultsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchRecord', ], ], 'SearchSortOrder' => [ 'type' => 'string', 'enum' => [ 'Ascending', 'Descending', ], ], 'SecondaryStatus' => [ 'type' => 'string', 'enum' => [ 'Starting', 'LaunchingMLInstances', 'PreparingTrainingStack', 'Downloading', 'DownloadingTrainingImage', 'Training', 'Uploading', 'Stopping', 'Stopped', 'MaxRuntimeExceeded', 'Completed', 'Failed', 'Interrupted', 'MaxWaitTimeExceeded', 'Updating', 'Restarting', ], ], 'SecondaryStatusTransition' => [ 'type' => 'structure', 'required' => [ 'Status', 'StartTime', ], 'members' => [ 'Status' => [ 'shape' => 'SecondaryStatus', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'StatusMessage' => [ 'shape' => 'StatusMessage', ], ], ], 'SecondaryStatusTransitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecondaryStatusTransition', ], ], 'SecretArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:aws[a-z\\-]*:secretsmanager:[a-z0-9\\-]*:[0-9]{12}:secret:.*', ], 'SecurityGroupId' => [ 'type' => 'string', 'max' => 32, 'pattern' => '[-0-9a-zA-Z]+', ], 'SecurityGroupIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'max' => 5, ], 'Seed' => [ 'type' => 'long', ], 'SendPipelineExecutionStepFailureRequest' => [ 'type' => 'structure', 'required' => [ 'CallbackToken', ], 'members' => [ 'CallbackToken' => [ 'shape' => 'CallbackToken', ], 'FailureReason' => [ 'shape' => 'String256', ], 'ClientRequestToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], ], ], 'SendPipelineExecutionStepFailureResponse' => [ 'type' => 'structure', 'members' => [ 'PipelineExecutionArn' => [ 'shape' => 'PipelineExecutionArn', ], ], ], 'SendPipelineExecutionStepSuccessRequest' => [ 'type' => 'structure', 'required' => [ 'CallbackToken', ], 'members' => [ 'CallbackToken' => [ 'shape' => 'CallbackToken', ], 'OutputParameters' => [ 'shape' => 'OutputParameterList', ], 'ClientRequestToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], ], ], 'SendPipelineExecutionStepSuccessResponse' => [ 'type' => 'structure', 'members' => [ 'PipelineExecutionArn' => [ 'shape' => 'PipelineExecutionArn', ], ], ], 'ServiceCatalogEntityId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_\\-]*', ], 'ServiceCatalogProvisionedProductDetails' => [ 'type' => 'structure', 'members' => [ 'ProvisionedProductId' => [ 'shape' => 'ServiceCatalogEntityId', ], 'ProvisionedProductStatusMessage' => [ 'shape' => 'ProvisionedProductStatusMessage', ], ], ], 'ServiceCatalogProvisioningDetails' => [ 'type' => 'structure', 'required' => [ 'ProductId', 'ProvisioningArtifactId', ], 'members' => [ 'ProductId' => [ 'shape' => 'ServiceCatalogEntityId', ], 'ProvisioningArtifactId' => [ 'shape' => 'ServiceCatalogEntityId', ], 'PathId' => [ 'shape' => 'ServiceCatalogEntityId', ], 'ProvisioningParameters' => [ 'shape' => 'ProvisioningParameters', ], ], ], 'SessionExpirationDurationInSeconds' => [ 'type' => 'integer', 'max' => 43200, 'min' => 1800, ], 'SharingSettings' => [ 'type' => 'structure', 'members' => [ 'NotebookOutputOption' => [ 'shape' => 'NotebookOutputOption', ], 'S3OutputPath' => [ 'shape' => 'S3Uri', ], 'S3KmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'ShuffleConfig' => [ 'type' => 'structure', 'required' => [ 'Seed', ], 'members' => [ 'Seed' => [ 'shape' => 'Seed', ], ], ], 'SingleSignOnUserIdentifier' => [ 'type' => 'string', 'pattern' => 'UserName', ], 'SnsTopicArn' => [ 'type' => 'string', 'max' => 2048, 'pattern' => 'arn:aws[a-z\\-]*:sns:[a-z0-9\\-]*:[0-9]{12}:[a-zA-Z0-9_.-]+', ], 'SortActionsBy' => [ 'type' => 'string', 'enum' => [ 'Name', 'CreationTime', ], ], 'SortArtifactsBy' => [ 'type' => 'string', 'enum' => [ 'CreationTime', ], ], 'SortAssociationsBy' => [ 'type' => 'string', 'enum' => [ 'SourceArn', 'DestinationArn', 'SourceType', 'DestinationType', 'CreationTime', ], ], 'SortBy' => [ 'type' => 'string', 'enum' => [ 'Name', 'CreationTime', 'Status', ], ], 'SortContextsBy' => [ 'type' => 'string', 'enum' => [ 'Name', 'CreationTime', ], ], 'SortExperimentsBy' => [ 'type' => 'string', 'enum' => [ 'Name', 'CreationTime', ], ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'Ascending', 'Descending', ], ], 'SortPipelineExecutionsBy' => [ 'type' => 'string', 'enum' => [ 'CreationTime', 'PipelineExecutionArn', ], ], 'SortPipelinesBy' => [ 'type' => 'string', 'enum' => [ 'Name', 'CreationTime', ], ], 'SortTrialComponentsBy' => [ 'type' => 'string', 'enum' => [ 'Name', 'CreationTime', ], ], 'SortTrialsBy' => [ 'type' => 'string', 'enum' => [ 'Name', 'CreationTime', ], ], 'SourceAlgorithm' => [ 'type' => 'structure', 'required' => [ 'AlgorithmName', ], 'members' => [ 'ModelDataUrl' => [ 'shape' => 'Url', ], 'AlgorithmName' => [ 'shape' => 'ArnOrName', ], ], ], 'SourceAlgorithmList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceAlgorithm', ], 'max' => 1, 'min' => 1, ], 'SourceAlgorithmSpecification' => [ 'type' => 'structure', 'required' => [ 'SourceAlgorithms', ], 'members' => [ 'SourceAlgorithms' => [ 'shape' => 'SourceAlgorithmList', ], ], ], 'SourceIpConfig' => [ 'type' => 'structure', 'required' => [ 'Cidrs', ], 'members' => [ 'Cidrs' => [ 'shape' => 'Cidrs', ], ], ], 'SourceType' => [ 'type' => 'string', 'max' => 128, ], 'SourceUri' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '.*', ], 'SplitType' => [ 'type' => 'string', 'enum' => [ 'None', 'Line', 'RecordIO', 'TFRecord', ], ], 'StartMonitoringScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'MonitoringScheduleName', ], 'members' => [ 'MonitoringScheduleName' => [ 'shape' => 'MonitoringScheduleName', ], ], ], 'StartNotebookInstanceInput' => [ 'type' => 'structure', 'required' => [ 'NotebookInstanceName', ], 'members' => [ 'NotebookInstanceName' => [ 'shape' => 'NotebookInstanceName', ], ], ], 'StartPipelineExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'PipelineName', 'ClientRequestToken', ], 'members' => [ 'PipelineName' => [ 'shape' => 'PipelineName', ], 'PipelineExecutionDisplayName' => [ 'shape' => 'PipelineExecutionName', ], 'PipelineParameters' => [ 'shape' => 'ParameterList', ], 'PipelineExecutionDescription' => [ 'shape' => 'PipelineExecutionDescription', ], 'ClientRequestToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], ], ], 'StartPipelineExecutionResponse' => [ 'type' => 'structure', 'members' => [ 'PipelineExecutionArn' => [ 'shape' => 'PipelineExecutionArn', ], ], ], 'StatusDetails' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '.*', ], 'StatusMessage' => [ 'type' => 'string', ], 'StepName' => [ 'type' => 'string', 'max' => 256, 'pattern' => '.*', ], 'StepStatus' => [ 'type' => 'string', 'enum' => [ 'Starting', 'Executing', 'Stopping', 'Stopped', 'Failed', 'Succeeded', ], ], 'StopAutoMLJobRequest' => [ 'type' => 'structure', 'required' => [ 'AutoMLJobName', ], 'members' => [ 'AutoMLJobName' => [ 'shape' => 'AutoMLJobName', ], ], ], 'StopCompilationJobRequest' => [ 'type' => 'structure', 'required' => [ 'CompilationJobName', ], 'members' => [ 'CompilationJobName' => [ 'shape' => 'EntityName', ], ], ], 'StopEdgePackagingJobRequest' => [ 'type' => 'structure', 'required' => [ 'EdgePackagingJobName', ], 'members' => [ 'EdgePackagingJobName' => [ 'shape' => 'EntityName', ], ], ], 'StopHyperParameterTuningJobRequest' => [ 'type' => 'structure', 'required' => [ 'HyperParameterTuningJobName', ], 'members' => [ 'HyperParameterTuningJobName' => [ 'shape' => 'HyperParameterTuningJobName', ], ], ], 'StopLabelingJobRequest' => [ 'type' => 'structure', 'required' => [ 'LabelingJobName', ], 'members' => [ 'LabelingJobName' => [ 'shape' => 'LabelingJobName', ], ], ], 'StopMonitoringScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'MonitoringScheduleName', ], 'members' => [ 'MonitoringScheduleName' => [ 'shape' => 'MonitoringScheduleName', ], ], ], 'StopNotebookInstanceInput' => [ 'type' => 'structure', 'required' => [ 'NotebookInstanceName', ], 'members' => [ 'NotebookInstanceName' => [ 'shape' => 'NotebookInstanceName', ], ], ], 'StopPipelineExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'PipelineExecutionArn', 'ClientRequestToken', ], 'members' => [ 'PipelineExecutionArn' => [ 'shape' => 'PipelineExecutionArn', ], 'ClientRequestToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], ], ], 'StopPipelineExecutionResponse' => [ 'type' => 'structure', 'members' => [ 'PipelineExecutionArn' => [ 'shape' => 'PipelineExecutionArn', ], ], ], 'StopProcessingJobRequest' => [ 'type' => 'structure', 'required' => [ 'ProcessingJobName', ], 'members' => [ 'ProcessingJobName' => [ 'shape' => 'ProcessingJobName', ], ], ], 'StopTrainingJobRequest' => [ 'type' => 'structure', 'required' => [ 'TrainingJobName', ], 'members' => [ 'TrainingJobName' => [ 'shape' => 'TrainingJobName', ], ], ], 'StopTransformJobRequest' => [ 'type' => 'structure', 'required' => [ 'TransformJobName', ], 'members' => [ 'TransformJobName' => [ 'shape' => 'TransformJobName', ], ], ], 'StoppingCondition' => [ 'type' => 'structure', 'members' => [ 'MaxRuntimeInSeconds' => [ 'shape' => 'MaxRuntimeInSeconds', ], 'MaxWaitTimeInSeconds' => [ 'shape' => 'MaxWaitTimeInSeconds', ], ], ], 'String' => [ 'type' => 'string', ], 'String1024' => [ 'type' => 'string', 'max' => 1024, ], 'String200' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '.+', ], 'String2048' => [ 'type' => 'string', 'max' => 2048, ], 'String256' => [ 'type' => 'string', 'max' => 256, ], 'String64' => [ 'type' => 'string', 'max' => 64, ], 'StringParameterValue' => [ 'type' => 'string', 'max' => 256, 'pattern' => '.*', ], 'SubnetId' => [ 'type' => 'string', 'max' => 32, 'pattern' => '[-0-9a-zA-Z]+', ], 'Subnets' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], 'max' => 16, 'min' => 1, ], 'SubscribedWorkteam' => [ 'type' => 'structure', 'required' => [ 'WorkteamArn', ], 'members' => [ 'WorkteamArn' => [ 'shape' => 'WorkteamArn', ], 'MarketplaceTitle' => [ 'shape' => 'String200', ], 'SellerName' => [ 'shape' => 'String', ], 'MarketplaceDescription' => [ 'shape' => 'String200', ], 'ListingId' => [ 'shape' => 'String', ], ], ], 'SubscribedWorkteams' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubscribedWorkteam', ], ], 'Success' => [ 'type' => 'boolean', ], 'SuggestionQuery' => [ 'type' => 'structure', 'members' => [ 'PropertyNameQuery' => [ 'shape' => 'PropertyNameQuery', ], ], ], 'TableName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 50, 'min' => 0, ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TargetAttributeName' => [ 'type' => 'string', 'min' => 1, ], 'TargetDevice' => [ 'type' => 'string', 'enum' => [ 'lambda', 'ml_m4', 'ml_m5', 'ml_c4', 'ml_c5', 'ml_p2', 'ml_p3', 'ml_g4dn', 'ml_inf1', 'ml_eia2', 'jetson_tx1', 'jetson_tx2', 'jetson_nano', 'jetson_xavier', 'rasp3b', 'imx8qm', 'deeplens', 'rk3399', 'rk3288', 'aisage', 'sbe_c', 'qcs605', 'qcs603', 'sitara_am57x', 'amba_cv22', 'amba_cv25', 'x86_win32', 'x86_win64', 'coreml', 'jacinto_tda4vm', ], ], 'TargetObjectiveMetricValue' => [ 'type' => 'float', ], 'TargetPlatform' => [ 'type' => 'structure', 'required' => [ 'Os', 'Arch', ], 'members' => [ 'Os' => [ 'shape' => 'TargetPlatformOs', ], 'Arch' => [ 'shape' => 'TargetPlatformArch', ], 'Accelerator' => [ 'shape' => 'TargetPlatformAccelerator', ], ], ], 'TargetPlatformAccelerator' => [ 'type' => 'string', 'enum' => [ 'INTEL_GRAPHICS', 'MALI', 'NVIDIA', ], ], 'TargetPlatformArch' => [ 'type' => 'string', 'enum' => [ 'X86_64', 'X86', 'ARM64', 'ARM_EABI', 'ARM_EABIHF', ], ], 'TargetPlatformOs' => [ 'type' => 'string', 'enum' => [ 'ANDROID', 'LINUX', ], ], 'TaskAvailabilityLifetimeInSeconds' => [ 'type' => 'integer', 'min' => 60, ], 'TaskCount' => [ 'type' => 'integer', 'min' => 1, ], 'TaskDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '.+', ], 'TaskInput' => [ 'type' => 'string', 'max' => 128000, 'min' => 2, 'pattern' => '[\\S\\s]+', ], 'TaskKeyword' => [ 'type' => 'string', 'max' => 30, 'min' => 1, 'pattern' => '^[A-Za-z0-9]+( [A-Za-z0-9]+)*$', ], 'TaskKeywords' => [ 'type' => 'list', 'member' => [ 'shape' => 'TaskKeyword', ], 'max' => 5, 'min' => 1, ], 'TaskTimeLimitInSeconds' => [ 'type' => 'integer', 'min' => 30, ], 'TaskTitle' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[\\t\\n\\r -\\uD7FF\\uE000-\\uFFFD]*$', ], 'TemplateContent' => [ 'type' => 'string', 'max' => 128000, 'min' => 1, 'pattern' => '[\\S\\s]+', ], 'TemplateContentSha256' => [ 'type' => 'string', 'max' => 128000, 'min' => 1, ], 'TemplateUrl' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'TensorBoardAppSettings' => [ 'type' => 'structure', 'members' => [ 'DefaultResourceSpec' => [ 'shape' => 'ResourceSpec', ], ], ], 'TensorBoardOutputConfig' => [ 'type' => 'structure', 'required' => [ 'S3OutputPath', ], 'members' => [ 'LocalPath' => [ 'shape' => 'DirectoryPath', ], 'S3OutputPath' => [ 'shape' => 'S3Uri', ], ], ], 'TenthFractionsOfACent' => [ 'type' => 'integer', 'max' => 9, 'min' => 0, ], 'TerminationWaitInSeconds' => [ 'type' => 'integer', 'max' => 3600, 'min' => 0, ], 'ThingName' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[a-zA-Z0-9:_-]+', ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TrafficRoutingConfig' => [ 'type' => 'structure', 'required' => [ 'Type', 'WaitIntervalInSeconds', ], 'members' => [ 'Type' => [ 'shape' => 'TrafficRoutingConfigType', ], 'WaitIntervalInSeconds' => [ 'shape' => 'WaitIntervalInSeconds', ], 'CanarySize' => [ 'shape' => 'CapacitySize', ], ], ], 'TrafficRoutingConfigType' => [ 'type' => 'string', 'enum' => [ 'ALL_AT_ONCE', 'CANARY', ], ], 'TrainingEnvironmentKey' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[a-zA-Z_][a-zA-Z0-9_]*', ], 'TrainingEnvironmentMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TrainingEnvironmentKey', ], 'value' => [ 'shape' => 'TrainingEnvironmentValue', ], 'max' => 48, ], 'TrainingEnvironmentValue' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[\\S\\s]*', ], 'TrainingInputMode' => [ 'type' => 'string', 'enum' => [ 'Pipe', 'File', ], ], 'TrainingInstanceCount' => [ 'type' => 'integer', 'min' => 1, ], 'TrainingInstanceType' => [ 'type' => 'string', 'enum' => [ 'ml.m4.xlarge', 'ml.m4.2xlarge', 'ml.m4.4xlarge', 'ml.m4.10xlarge', 'ml.m4.16xlarge', 'ml.g4dn.xlarge', 'ml.g4dn.2xlarge', 'ml.g4dn.4xlarge', 'ml.g4dn.8xlarge', 'ml.g4dn.12xlarge', 'ml.g4dn.16xlarge', 'ml.m5.large', 'ml.m5.xlarge', 'ml.m5.2xlarge', 'ml.m5.4xlarge', 'ml.m5.12xlarge', 'ml.m5.24xlarge', 'ml.c4.xlarge', 'ml.c4.2xlarge', 'ml.c4.4xlarge', 'ml.c4.8xlarge', 'ml.p2.xlarge', 'ml.p2.8xlarge', 'ml.p2.16xlarge', 'ml.p3.2xlarge', 'ml.p3.8xlarge', 'ml.p3.16xlarge', 'ml.p3dn.24xlarge', 'ml.p4d.24xlarge', 'ml.c5.xlarge', 'ml.c5.2xlarge', 'ml.c5.4xlarge', 'ml.c5.9xlarge', 'ml.c5.18xlarge', 'ml.c5n.xlarge', 'ml.c5n.2xlarge', 'ml.c5n.4xlarge', 'ml.c5n.9xlarge', 'ml.c5n.18xlarge', ], ], 'TrainingInstanceTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrainingInstanceType', ], ], 'TrainingJob' => [ 'type' => 'structure', 'members' => [ 'TrainingJobName' => [ 'shape' => 'TrainingJobName', ], 'TrainingJobArn' => [ 'shape' => 'TrainingJobArn', ], 'TuningJobArn' => [ 'shape' => 'HyperParameterTuningJobArn', ], 'LabelingJobArn' => [ 'shape' => 'LabelingJobArn', ], 'AutoMLJobArn' => [ 'shape' => 'AutoMLJobArn', ], 'ModelArtifacts' => [ 'shape' => 'ModelArtifacts', ], 'TrainingJobStatus' => [ 'shape' => 'TrainingJobStatus', ], 'SecondaryStatus' => [ 'shape' => 'SecondaryStatus', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'HyperParameters' => [ 'shape' => 'HyperParameters', ], 'AlgorithmSpecification' => [ 'shape' => 'AlgorithmSpecification', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'OutputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'ResourceConfig' => [ 'shape' => 'ResourceConfig', ], 'VpcConfig' => [ 'shape' => 'VpcConfig', ], 'StoppingCondition' => [ 'shape' => 'StoppingCondition', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'TrainingStartTime' => [ 'shape' => 'Timestamp', ], 'TrainingEndTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'SecondaryStatusTransitions' => [ 'shape' => 'SecondaryStatusTransitions', ], 'FinalMetricDataList' => [ 'shape' => 'FinalMetricDataList', ], 'EnableNetworkIsolation' => [ 'shape' => 'Boolean', ], 'EnableInterContainerTrafficEncryption' => [ 'shape' => 'Boolean', ], 'EnableManagedSpotTraining' => [ 'shape' => 'Boolean', ], 'CheckpointConfig' => [ 'shape' => 'CheckpointConfig', ], 'TrainingTimeInSeconds' => [ 'shape' => 'TrainingTimeInSeconds', ], 'BillableTimeInSeconds' => [ 'shape' => 'BillableTimeInSeconds', ], 'DebugHookConfig' => [ 'shape' => 'DebugHookConfig', ], 'ExperimentConfig' => [ 'shape' => 'ExperimentConfig', ], 'DebugRuleConfigurations' => [ 'shape' => 'DebugRuleConfigurations', ], 'TensorBoardOutputConfig' => [ 'shape' => 'TensorBoardOutputConfig', ], 'DebugRuleEvaluationStatuses' => [ 'shape' => 'DebugRuleEvaluationStatuses', ], 'Environment' => [ 'shape' => 'TrainingEnvironmentMap', ], 'RetryStrategy' => [ 'shape' => 'RetryStrategy', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TrainingJobArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:training-job/.*', ], 'TrainingJobDefinition' => [ 'type' => 'structure', 'required' => [ 'TrainingInputMode', 'InputDataConfig', 'OutputDataConfig', 'ResourceConfig', 'StoppingCondition', ], 'members' => [ 'TrainingInputMode' => [ 'shape' => 'TrainingInputMode', ], 'HyperParameters' => [ 'shape' => 'HyperParameters', ], 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'OutputDataConfig' => [ 'shape' => 'OutputDataConfig', ], 'ResourceConfig' => [ 'shape' => 'ResourceConfig', ], 'StoppingCondition' => [ 'shape' => 'StoppingCondition', ], ], ], 'TrainingJobEarlyStoppingType' => [ 'type' => 'string', 'enum' => [ 'Off', 'Auto', ], ], 'TrainingJobName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,62}', ], 'TrainingJobSortByOptions' => [ 'type' => 'string', 'enum' => [ 'Name', 'CreationTime', 'Status', 'FinalObjectiveMetricValue', ], ], 'TrainingJobStatus' => [ 'type' => 'string', 'enum' => [ 'InProgress', 'Completed', 'Failed', 'Stopping', 'Stopped', ], ], 'TrainingJobStatusCounter' => [ 'type' => 'integer', 'min' => 0, ], 'TrainingJobStatusCounters' => [ 'type' => 'structure', 'members' => [ 'Completed' => [ 'shape' => 'TrainingJobStatusCounter', ], 'InProgress' => [ 'shape' => 'TrainingJobStatusCounter', ], 'RetryableError' => [ 'shape' => 'TrainingJobStatusCounter', ], 'NonRetryableError' => [ 'shape' => 'TrainingJobStatusCounter', ], 'Stopped' => [ 'shape' => 'TrainingJobStatusCounter', ], ], ], 'TrainingJobStepMetadata' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'TrainingJobArn', ], ], ], 'TrainingJobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrainingJobSummary', ], ], 'TrainingJobSummary' => [ 'type' => 'structure', 'required' => [ 'TrainingJobName', 'TrainingJobArn', 'CreationTime', 'TrainingJobStatus', ], 'members' => [ 'TrainingJobName' => [ 'shape' => 'TrainingJobName', ], 'TrainingJobArn' => [ 'shape' => 'TrainingJobArn', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'TrainingEndTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'TrainingJobStatus' => [ 'shape' => 'TrainingJobStatus', ], ], ], 'TrainingSpecification' => [ 'type' => 'structure', 'required' => [ 'TrainingImage', 'SupportedTrainingInstanceTypes', 'TrainingChannels', ], 'members' => [ 'TrainingImage' => [ 'shape' => 'ContainerImage', ], 'TrainingImageDigest' => [ 'shape' => 'ImageDigest', ], 'SupportedHyperParameters' => [ 'shape' => 'HyperParameterSpecifications', ], 'SupportedTrainingInstanceTypes' => [ 'shape' => 'TrainingInstanceTypes', ], 'SupportsDistributedTraining' => [ 'shape' => 'Boolean', ], 'MetricDefinitions' => [ 'shape' => 'MetricDefinitionList', ], 'TrainingChannels' => [ 'shape' => 'ChannelSpecifications', ], 'SupportedTuningJobObjectiveMetrics' => [ 'shape' => 'HyperParameterTuningJobObjectives', ], ], ], 'TrainingTimeInSeconds' => [ 'type' => 'integer', 'min' => 1, ], 'TransformDataSource' => [ 'type' => 'structure', 'required' => [ 'S3DataSource', ], 'members' => [ 'S3DataSource' => [ 'shape' => 'TransformS3DataSource', ], ], ], 'TransformEnvironmentKey' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '[a-zA-Z_][a-zA-Z0-9_]{0,1023}', ], 'TransformEnvironmentMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TransformEnvironmentKey', ], 'value' => [ 'shape' => 'TransformEnvironmentValue', ], 'max' => 16, ], 'TransformEnvironmentValue' => [ 'type' => 'string', 'max' => 10240, 'pattern' => '[\\S\\s]*', ], 'TransformInput' => [ 'type' => 'structure', 'required' => [ 'DataSource', ], 'members' => [ 'DataSource' => [ 'shape' => 'TransformDataSource', ], 'ContentType' => [ 'shape' => 'ContentType', ], 'CompressionType' => [ 'shape' => 'CompressionType', ], 'SplitType' => [ 'shape' => 'SplitType', ], ], ], 'TransformInstanceCount' => [ 'type' => 'integer', 'min' => 1, ], 'TransformInstanceType' => [ 'type' => 'string', 'enum' => [ 'ml.m4.xlarge', 'ml.m4.2xlarge', 'ml.m4.4xlarge', 'ml.m4.10xlarge', 'ml.m4.16xlarge', 'ml.c4.xlarge', 'ml.c4.2xlarge', 'ml.c4.4xlarge', 'ml.c4.8xlarge', 'ml.p2.xlarge', 'ml.p2.8xlarge', 'ml.p2.16xlarge', 'ml.p3.2xlarge', 'ml.p3.8xlarge', 'ml.p3.16xlarge', 'ml.c5.xlarge', 'ml.c5.2xlarge', 'ml.c5.4xlarge', 'ml.c5.9xlarge', 'ml.c5.18xlarge', 'ml.m5.large', 'ml.m5.xlarge', 'ml.m5.2xlarge', 'ml.m5.4xlarge', 'ml.m5.12xlarge', 'ml.m5.24xlarge', 'ml.g4dn.xlarge', 'ml.g4dn.2xlarge', 'ml.g4dn.4xlarge', 'ml.g4dn.8xlarge', 'ml.g4dn.12xlarge', 'ml.g4dn.16xlarge', ], ], 'TransformInstanceTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'TransformInstanceType', ], 'min' => 1, ], 'TransformJob' => [ 'type' => 'structure', 'members' => [ 'TransformJobName' => [ 'shape' => 'TransformJobName', ], 'TransformJobArn' => [ 'shape' => 'TransformJobArn', ], 'TransformJobStatus' => [ 'shape' => 'TransformJobStatus', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'ModelName' => [ 'shape' => 'ModelName', ], 'MaxConcurrentTransforms' => [ 'shape' => 'MaxConcurrentTransforms', ], 'ModelClientConfig' => [ 'shape' => 'ModelClientConfig', ], 'MaxPayloadInMB' => [ 'shape' => 'MaxPayloadInMB', ], 'BatchStrategy' => [ 'shape' => 'BatchStrategy', ], 'Environment' => [ 'shape' => 'TransformEnvironmentMap', ], 'TransformInput' => [ 'shape' => 'TransformInput', ], 'TransformOutput' => [ 'shape' => 'TransformOutput', ], 'TransformResources' => [ 'shape' => 'TransformResources', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'TransformStartTime' => [ 'shape' => 'Timestamp', ], 'TransformEndTime' => [ 'shape' => 'Timestamp', ], 'LabelingJobArn' => [ 'shape' => 'LabelingJobArn', ], 'AutoMLJobArn' => [ 'shape' => 'AutoMLJobArn', ], 'DataProcessing' => [ 'shape' => 'DataProcessing', ], 'ExperimentConfig' => [ 'shape' => 'ExperimentConfig', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TransformJobArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:transform-job/.*', ], 'TransformJobDefinition' => [ 'type' => 'structure', 'required' => [ 'TransformInput', 'TransformOutput', 'TransformResources', ], 'members' => [ 'MaxConcurrentTransforms' => [ 'shape' => 'MaxConcurrentTransforms', ], 'MaxPayloadInMB' => [ 'shape' => 'MaxPayloadInMB', ], 'BatchStrategy' => [ 'shape' => 'BatchStrategy', ], 'Environment' => [ 'shape' => 'TransformEnvironmentMap', ], 'TransformInput' => [ 'shape' => 'TransformInput', ], 'TransformOutput' => [ 'shape' => 'TransformOutput', ], 'TransformResources' => [ 'shape' => 'TransformResources', ], ], ], 'TransformJobName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,62}', ], 'TransformJobStatus' => [ 'type' => 'string', 'enum' => [ 'InProgress', 'Completed', 'Failed', 'Stopping', 'Stopped', ], ], 'TransformJobStepMetadata' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'TransformJobArn', ], ], ], 'TransformJobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'TransformJobSummary', ], ], 'TransformJobSummary' => [ 'type' => 'structure', 'required' => [ 'TransformJobName', 'TransformJobArn', 'CreationTime', 'TransformJobStatus', ], 'members' => [ 'TransformJobName' => [ 'shape' => 'TransformJobName', ], 'TransformJobArn' => [ 'shape' => 'TransformJobArn', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'TransformEndTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'TransformJobStatus' => [ 'shape' => 'TransformJobStatus', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], ], ], 'TransformOutput' => [ 'type' => 'structure', 'required' => [ 'S3OutputPath', ], 'members' => [ 'S3OutputPath' => [ 'shape' => 'S3Uri', ], 'Accept' => [ 'shape' => 'Accept', ], 'AssembleWith' => [ 'shape' => 'AssemblyType', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'TransformResources' => [ 'type' => 'structure', 'required' => [ 'InstanceType', 'InstanceCount', ], 'members' => [ 'InstanceType' => [ 'shape' => 'TransformInstanceType', ], 'InstanceCount' => [ 'shape' => 'TransformInstanceCount', ], 'VolumeKmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'TransformS3DataSource' => [ 'type' => 'structure', 'required' => [ 'S3DataType', 'S3Uri', ], 'members' => [ 'S3DataType' => [ 'shape' => 'S3DataType', ], 'S3Uri' => [ 'shape' => 'S3Uri', ], ], ], 'Trial' => [ 'type' => 'structure', 'members' => [ 'TrialName' => [ 'shape' => 'ExperimentEntityName', ], 'TrialArn' => [ 'shape' => 'TrialArn', ], 'DisplayName' => [ 'shape' => 'ExperimentEntityName', ], 'ExperimentName' => [ 'shape' => 'ExperimentEntityName', ], 'Source' => [ 'shape' => 'TrialSource', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'CreatedBy' => [ 'shape' => 'UserContext', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedBy' => [ 'shape' => 'UserContext', ], 'MetadataProperties' => [ 'shape' => 'MetadataProperties', ], 'Tags' => [ 'shape' => 'TagList', ], 'TrialComponentSummaries' => [ 'shape' => 'TrialComponentSimpleSummaries', ], ], ], 'TrialArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:experiment-trial/.*', ], 'TrialComponent' => [ 'type' => 'structure', 'members' => [ 'TrialComponentName' => [ 'shape' => 'ExperimentEntityName', ], 'DisplayName' => [ 'shape' => 'ExperimentEntityName', ], 'TrialComponentArn' => [ 'shape' => 'TrialComponentArn', ], 'Source' => [ 'shape' => 'TrialComponentSource', ], 'Status' => [ 'shape' => 'TrialComponentStatus', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'CreatedBy' => [ 'shape' => 'UserContext', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedBy' => [ 'shape' => 'UserContext', ], 'Parameters' => [ 'shape' => 'TrialComponentParameters', ], 'InputArtifacts' => [ 'shape' => 'TrialComponentArtifacts', ], 'OutputArtifacts' => [ 'shape' => 'TrialComponentArtifacts', ], 'Metrics' => [ 'shape' => 'TrialComponentMetricSummaries', ], 'MetadataProperties' => [ 'shape' => 'MetadataProperties', ], 'SourceDetail' => [ 'shape' => 'TrialComponentSourceDetail', ], 'Tags' => [ 'shape' => 'TagList', ], 'Parents' => [ 'shape' => 'Parents', ], ], ], 'TrialComponentArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:experiment-trial-component/.*', ], 'TrialComponentArtifact' => [ 'type' => 'structure', 'required' => [ 'Value', ], 'members' => [ 'MediaType' => [ 'shape' => 'MediaType', ], 'Value' => [ 'shape' => 'TrialComponentArtifactValue', ], ], ], 'TrialComponentArtifactValue' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '.*', ], 'TrialComponentArtifacts' => [ 'type' => 'map', 'key' => [ 'shape' => 'TrialComponentKey64', ], 'value' => [ 'shape' => 'TrialComponentArtifact', ], 'max' => 30, ], 'TrialComponentKey256' => [ 'type' => 'string', 'max' => 256, 'pattern' => '.*', ], 'TrialComponentKey64' => [ 'type' => 'string', 'max' => 64, 'pattern' => '.*', ], 'TrialComponentMetricSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrialComponentMetricSummary', ], ], 'TrialComponentMetricSummary' => [ 'type' => 'structure', 'members' => [ 'MetricName' => [ 'shape' => 'MetricName', ], 'SourceArn' => [ 'shape' => 'TrialComponentSourceArn', ], 'TimeStamp' => [ 'shape' => 'Timestamp', ], 'Max' => [ 'shape' => 'OptionalDouble', ], 'Min' => [ 'shape' => 'OptionalDouble', ], 'Last' => [ 'shape' => 'OptionalDouble', ], 'Count' => [ 'shape' => 'OptionalInteger', ], 'Avg' => [ 'shape' => 'OptionalDouble', ], 'StdDev' => [ 'shape' => 'OptionalDouble', ], ], ], 'TrialComponentParameterValue' => [ 'type' => 'structure', 'members' => [ 'StringValue' => [ 'shape' => 'StringParameterValue', ], 'NumberValue' => [ 'shape' => 'DoubleParameterValue', ], ], ], 'TrialComponentParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'TrialComponentKey256', ], 'value' => [ 'shape' => 'TrialComponentParameterValue', ], 'max' => 150, ], 'TrialComponentPrimaryStatus' => [ 'type' => 'string', 'enum' => [ 'InProgress', 'Completed', 'Failed', 'Stopping', 'Stopped', ], ], 'TrialComponentSimpleSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrialComponentSimpleSummary', ], ], 'TrialComponentSimpleSummary' => [ 'type' => 'structure', 'members' => [ 'TrialComponentName' => [ 'shape' => 'ExperimentEntityName', ], 'TrialComponentArn' => [ 'shape' => 'TrialComponentArn', ], 'TrialComponentSource' => [ 'shape' => 'TrialComponentSource', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'CreatedBy' => [ 'shape' => 'UserContext', ], ], ], 'TrialComponentSource' => [ 'type' => 'structure', 'required' => [ 'SourceArn', ], 'members' => [ 'SourceArn' => [ 'shape' => 'TrialComponentSourceArn', ], 'SourceType' => [ 'shape' => 'SourceType', ], ], ], 'TrialComponentSourceArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:.*', ], 'TrialComponentSourceDetail' => [ 'type' => 'structure', 'members' => [ 'SourceArn' => [ 'shape' => 'TrialComponentSourceArn', ], 'TrainingJob' => [ 'shape' => 'TrainingJob', ], 'ProcessingJob' => [ 'shape' => 'ProcessingJob', ], 'TransformJob' => [ 'shape' => 'TransformJob', ], ], ], 'TrialComponentStatus' => [ 'type' => 'structure', 'members' => [ 'PrimaryStatus' => [ 'shape' => 'TrialComponentPrimaryStatus', ], 'Message' => [ 'shape' => 'TrialComponentStatusMessage', ], ], ], 'TrialComponentStatusMessage' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '.*', ], 'TrialComponentSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrialComponentSummary', ], ], 'TrialComponentSummary' => [ 'type' => 'structure', 'members' => [ 'TrialComponentName' => [ 'shape' => 'ExperimentEntityName', ], 'TrialComponentArn' => [ 'shape' => 'TrialComponentArn', ], 'DisplayName' => [ 'shape' => 'ExperimentEntityName', ], 'TrialComponentSource' => [ 'shape' => 'TrialComponentSource', ], 'Status' => [ 'shape' => 'TrialComponentStatus', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'CreatedBy' => [ 'shape' => 'UserContext', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedBy' => [ 'shape' => 'UserContext', ], ], ], 'TrialSource' => [ 'type' => 'structure', 'required' => [ 'SourceArn', ], 'members' => [ 'SourceArn' => [ 'shape' => 'TrialSourceArn', ], 'SourceType' => [ 'shape' => 'SourceType', ], ], ], 'TrialSourceArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:.*', ], 'TrialSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrialSummary', ], ], 'TrialSummary' => [ 'type' => 'structure', 'members' => [ 'TrialArn' => [ 'shape' => 'TrialArn', ], 'TrialName' => [ 'shape' => 'ExperimentEntityName', ], 'DisplayName' => [ 'shape' => 'ExperimentEntityName', ], 'TrialSource' => [ 'shape' => 'TrialSource', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'TuningJobCompletionCriteria' => [ 'type' => 'structure', 'required' => [ 'TargetObjectiveMetricValue', ], 'members' => [ 'TargetObjectiveMetricValue' => [ 'shape' => 'TargetObjectiveMetricValue', ], ], ], 'USD' => [ 'type' => 'structure', 'members' => [ 'Dollars' => [ 'shape' => 'Dollars', ], 'Cents' => [ 'shape' => 'Cents', ], 'TenthFractionsOfACent' => [ 'shape' => 'TenthFractionsOfACent', ], ], ], 'UiConfig' => [ 'type' => 'structure', 'members' => [ 'UiTemplateS3Uri' => [ 'shape' => 'S3Uri', ], 'HumanTaskUiArn' => [ 'shape' => 'HumanTaskUiArn', ], ], ], 'UiTemplate' => [ 'type' => 'structure', 'required' => [ 'Content', ], 'members' => [ 'Content' => [ 'shape' => 'TemplateContent', ], ], ], 'UiTemplateInfo' => [ 'type' => 'structure', 'members' => [ 'Url' => [ 'shape' => 'TemplateUrl', ], 'ContentSha256' => [ 'shape' => 'TemplateContentSha256', ], ], ], 'UpdateActionRequest' => [ 'type' => 'structure', 'required' => [ 'ActionName', ], 'members' => [ 'ActionName' => [ 'shape' => 'ExperimentEntityName', ], 'Description' => [ 'shape' => 'ExperimentDescription', ], 'Status' => [ 'shape' => 'ActionStatus', ], 'Properties' => [ 'shape' => 'LineageEntityParameters', ], 'PropertiesToRemove' => [ 'shape' => 'ListLineageEntityParameterKey', ], ], ], 'UpdateActionResponse' => [ 'type' => 'structure', 'members' => [ 'ActionArn' => [ 'shape' => 'ActionArn', ], ], ], 'UpdateAppImageConfigRequest' => [ 'type' => 'structure', 'required' => [ 'AppImageConfigName', ], 'members' => [ 'AppImageConfigName' => [ 'shape' => 'AppImageConfigName', ], 'KernelGatewayImageConfig' => [ 'shape' => 'KernelGatewayImageConfig', ], ], ], 'UpdateAppImageConfigResponse' => [ 'type' => 'structure', 'members' => [ 'AppImageConfigArn' => [ 'shape' => 'AppImageConfigArn', ], ], ], 'UpdateArtifactRequest' => [ 'type' => 'structure', 'required' => [ 'ArtifactArn', ], 'members' => [ 'ArtifactArn' => [ 'shape' => 'ArtifactArn', ], 'ArtifactName' => [ 'shape' => 'ExperimentEntityName', ], 'Properties' => [ 'shape' => 'LineageEntityParameters', ], 'PropertiesToRemove' => [ 'shape' => 'ListLineageEntityParameterKey', ], ], ], 'UpdateArtifactResponse' => [ 'type' => 'structure', 'members' => [ 'ArtifactArn' => [ 'shape' => 'ArtifactArn', ], ], ], 'UpdateCodeRepositoryInput' => [ 'type' => 'structure', 'required' => [ 'CodeRepositoryName', ], 'members' => [ 'CodeRepositoryName' => [ 'shape' => 'EntityName', ], 'GitConfig' => [ 'shape' => 'GitConfigForUpdate', ], ], ], 'UpdateCodeRepositoryOutput' => [ 'type' => 'structure', 'required' => [ 'CodeRepositoryArn', ], 'members' => [ 'CodeRepositoryArn' => [ 'shape' => 'CodeRepositoryArn', ], ], ], 'UpdateContextRequest' => [ 'type' => 'structure', 'required' => [ 'ContextName', ], 'members' => [ 'ContextName' => [ 'shape' => 'ExperimentEntityName', ], 'Description' => [ 'shape' => 'ExperimentDescription', ], 'Properties' => [ 'shape' => 'LineageEntityParameters', ], 'PropertiesToRemove' => [ 'shape' => 'ListLineageEntityParameterKey', ], ], ], 'UpdateContextResponse' => [ 'type' => 'structure', 'members' => [ 'ContextArn' => [ 'shape' => 'ContextArn', ], ], ], 'UpdateDeviceFleetRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceFleetName', 'OutputConfig', ], 'members' => [ 'DeviceFleetName' => [ 'shape' => 'EntityName', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Description' => [ 'shape' => 'DeviceFleetDescription', ], 'OutputConfig' => [ 'shape' => 'EdgeOutputConfig', ], 'EnableIotRoleAlias' => [ 'shape' => 'EnableIotRoleAlias', ], ], ], 'UpdateDevicesRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceFleetName', 'Devices', ], 'members' => [ 'DeviceFleetName' => [ 'shape' => 'EntityName', ], 'Devices' => [ 'shape' => 'Devices', ], ], ], 'UpdateDomainRequest' => [ 'type' => 'structure', 'required' => [ 'DomainId', ], 'members' => [ 'DomainId' => [ 'shape' => 'DomainId', ], 'DefaultUserSettings' => [ 'shape' => 'UserSettings', ], ], ], 'UpdateDomainResponse' => [ 'type' => 'structure', 'members' => [ 'DomainArn' => [ 'shape' => 'DomainArn', ], ], ], 'UpdateEndpointInput' => [ 'type' => 'structure', 'required' => [ 'EndpointName', 'EndpointConfigName', ], 'members' => [ 'EndpointName' => [ 'shape' => 'EndpointName', ], 'EndpointConfigName' => [ 'shape' => 'EndpointConfigName', ], 'RetainAllVariantProperties' => [ 'shape' => 'Boolean', ], 'ExcludeRetainedVariantProperties' => [ 'shape' => 'VariantPropertyList', ], 'DeploymentConfig' => [ 'shape' => 'DeploymentConfig', ], ], ], 'UpdateEndpointOutput' => [ 'type' => 'structure', 'required' => [ 'EndpointArn', ], 'members' => [ 'EndpointArn' => [ 'shape' => 'EndpointArn', ], ], ], 'UpdateEndpointWeightsAndCapacitiesInput' => [ 'type' => 'structure', 'required' => [ 'EndpointName', 'DesiredWeightsAndCapacities', ], 'members' => [ 'EndpointName' => [ 'shape' => 'EndpointName', ], 'DesiredWeightsAndCapacities' => [ 'shape' => 'DesiredWeightAndCapacityList', ], ], ], 'UpdateEndpointWeightsAndCapacitiesOutput' => [ 'type' => 'structure', 'required' => [ 'EndpointArn', ], 'members' => [ 'EndpointArn' => [ 'shape' => 'EndpointArn', ], ], ], 'UpdateExperimentRequest' => [ 'type' => 'structure', 'required' => [ 'ExperimentName', ], 'members' => [ 'ExperimentName' => [ 'shape' => 'ExperimentEntityName', ], 'DisplayName' => [ 'shape' => 'ExperimentEntityName', ], 'Description' => [ 'shape' => 'ExperimentDescription', ], ], ], 'UpdateExperimentResponse' => [ 'type' => 'structure', 'members' => [ 'ExperimentArn' => [ 'shape' => 'ExperimentArn', ], ], ], 'UpdateImageRequest' => [ 'type' => 'structure', 'required' => [ 'ImageName', ], 'members' => [ 'DeleteProperties' => [ 'shape' => 'ImageDeletePropertyList', ], 'Description' => [ 'shape' => 'ImageDescription', ], 'DisplayName' => [ 'shape' => 'ImageDisplayName', ], 'ImageName' => [ 'shape' => 'ImageName', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], ], ], 'UpdateImageResponse' => [ 'type' => 'structure', 'members' => [ 'ImageArn' => [ 'shape' => 'ImageArn', ], ], ], 'UpdateModelPackageInput' => [ 'type' => 'structure', 'required' => [ 'ModelPackageArn', 'ModelApprovalStatus', ], 'members' => [ 'ModelPackageArn' => [ 'shape' => 'ModelPackageArn', ], 'ModelApprovalStatus' => [ 'shape' => 'ModelApprovalStatus', ], 'ApprovalDescription' => [ 'shape' => 'ApprovalDescription', ], ], ], 'UpdateModelPackageOutput' => [ 'type' => 'structure', 'required' => [ 'ModelPackageArn', ], 'members' => [ 'ModelPackageArn' => [ 'shape' => 'ModelPackageArn', ], ], ], 'UpdateMonitoringScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'MonitoringScheduleName', 'MonitoringScheduleConfig', ], 'members' => [ 'MonitoringScheduleName' => [ 'shape' => 'MonitoringScheduleName', ], 'MonitoringScheduleConfig' => [ 'shape' => 'MonitoringScheduleConfig', ], ], ], 'UpdateMonitoringScheduleResponse' => [ 'type' => 'structure', 'required' => [ 'MonitoringScheduleArn', ], 'members' => [ 'MonitoringScheduleArn' => [ 'shape' => 'MonitoringScheduleArn', ], ], ], 'UpdateNotebookInstanceInput' => [ 'type' => 'structure', 'required' => [ 'NotebookInstanceName', ], 'members' => [ 'NotebookInstanceName' => [ 'shape' => 'NotebookInstanceName', ], 'InstanceType' => [ 'shape' => 'InstanceType', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'LifecycleConfigName' => [ 'shape' => 'NotebookInstanceLifecycleConfigName', ], 'DisassociateLifecycleConfig' => [ 'shape' => 'DisassociateNotebookInstanceLifecycleConfig', ], 'VolumeSizeInGB' => [ 'shape' => 'NotebookInstanceVolumeSizeInGB', ], 'DefaultCodeRepository' => [ 'shape' => 'CodeRepositoryNameOrUrl', ], 'AdditionalCodeRepositories' => [ 'shape' => 'AdditionalCodeRepositoryNamesOrUrls', ], 'AcceleratorTypes' => [ 'shape' => 'NotebookInstanceAcceleratorTypes', ], 'DisassociateAcceleratorTypes' => [ 'shape' => 'DisassociateNotebookInstanceAcceleratorTypes', ], 'DisassociateDefaultCodeRepository' => [ 'shape' => 'DisassociateDefaultCodeRepository', ], 'DisassociateAdditionalCodeRepositories' => [ 'shape' => 'DisassociateAdditionalCodeRepositories', ], 'RootAccess' => [ 'shape' => 'RootAccess', ], ], ], 'UpdateNotebookInstanceLifecycleConfigInput' => [ 'type' => 'structure', 'required' => [ 'NotebookInstanceLifecycleConfigName', ], 'members' => [ 'NotebookInstanceLifecycleConfigName' => [ 'shape' => 'NotebookInstanceLifecycleConfigName', ], 'OnCreate' => [ 'shape' => 'NotebookInstanceLifecycleConfigList', ], 'OnStart' => [ 'shape' => 'NotebookInstanceLifecycleConfigList', ], ], ], 'UpdateNotebookInstanceLifecycleConfigOutput' => [ 'type' => 'structure', 'members' => [], ], 'UpdateNotebookInstanceOutput' => [ 'type' => 'structure', 'members' => [], ], 'UpdatePipelineExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'PipelineExecutionArn', ], 'members' => [ 'PipelineExecutionArn' => [ 'shape' => 'PipelineExecutionArn', ], 'PipelineExecutionDescription' => [ 'shape' => 'PipelineExecutionDescription', ], 'PipelineExecutionDisplayName' => [ 'shape' => 'PipelineExecutionName', ], ], ], 'UpdatePipelineExecutionResponse' => [ 'type' => 'structure', 'members' => [ 'PipelineExecutionArn' => [ 'shape' => 'PipelineExecutionArn', ], ], ], 'UpdatePipelineRequest' => [ 'type' => 'structure', 'required' => [ 'PipelineName', ], 'members' => [ 'PipelineName' => [ 'shape' => 'PipelineName', ], 'PipelineDisplayName' => [ 'shape' => 'PipelineName', ], 'PipelineDefinition' => [ 'shape' => 'PipelineDefinition', ], 'PipelineDescription' => [ 'shape' => 'PipelineDescription', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], ], ], 'UpdatePipelineResponse' => [ 'type' => 'structure', 'members' => [ 'PipelineArn' => [ 'shape' => 'PipelineArn', ], ], ], 'UpdateTrainingJobRequest' => [ 'type' => 'structure', 'required' => [ 'TrainingJobName', ], 'members' => [ 'TrainingJobName' => [ 'shape' => 'TrainingJobName', ], 'ProfilerConfig' => [ 'shape' => 'ProfilerConfigForUpdate', ], 'ProfilerRuleConfigurations' => [ 'shape' => 'ProfilerRuleConfigurations', ], ], ], 'UpdateTrainingJobResponse' => [ 'type' => 'structure', 'required' => [ 'TrainingJobArn', ], 'members' => [ 'TrainingJobArn' => [ 'shape' => 'TrainingJobArn', ], ], ], 'UpdateTrialComponentRequest' => [ 'type' => 'structure', 'required' => [ 'TrialComponentName', ], 'members' => [ 'TrialComponentName' => [ 'shape' => 'ExperimentEntityName', ], 'DisplayName' => [ 'shape' => 'ExperimentEntityName', ], 'Status' => [ 'shape' => 'TrialComponentStatus', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'Parameters' => [ 'shape' => 'TrialComponentParameters', ], 'ParametersToRemove' => [ 'shape' => 'ListTrialComponentKey256', ], 'InputArtifacts' => [ 'shape' => 'TrialComponentArtifacts', ], 'InputArtifactsToRemove' => [ 'shape' => 'ListTrialComponentKey256', ], 'OutputArtifacts' => [ 'shape' => 'TrialComponentArtifacts', ], 'OutputArtifactsToRemove' => [ 'shape' => 'ListTrialComponentKey256', ], ], ], 'UpdateTrialComponentResponse' => [ 'type' => 'structure', 'members' => [ 'TrialComponentArn' => [ 'shape' => 'TrialComponentArn', ], ], ], 'UpdateTrialRequest' => [ 'type' => 'structure', 'required' => [ 'TrialName', ], 'members' => [ 'TrialName' => [ 'shape' => 'ExperimentEntityName', ], 'DisplayName' => [ 'shape' => 'ExperimentEntityName', ], ], ], 'UpdateTrialResponse' => [ 'type' => 'structure', 'members' => [ 'TrialArn' => [ 'shape' => 'TrialArn', ], ], ], 'UpdateUserProfileRequest' => [ 'type' => 'structure', 'required' => [ 'DomainId', 'UserProfileName', ], 'members' => [ 'DomainId' => [ 'shape' => 'DomainId', ], 'UserProfileName' => [ 'shape' => 'UserProfileName', ], 'UserSettings' => [ 'shape' => 'UserSettings', ], ], ], 'UpdateUserProfileResponse' => [ 'type' => 'structure', 'members' => [ 'UserProfileArn' => [ 'shape' => 'UserProfileArn', ], ], ], 'UpdateWorkforceRequest' => [ 'type' => 'structure', 'required' => [ 'WorkforceName', ], 'members' => [ 'WorkforceName' => [ 'shape' => 'WorkforceName', ], 'SourceIpConfig' => [ 'shape' => 'SourceIpConfig', ], 'OidcConfig' => [ 'shape' => 'OidcConfig', ], ], ], 'UpdateWorkforceResponse' => [ 'type' => 'structure', 'required' => [ 'Workforce', ], 'members' => [ 'Workforce' => [ 'shape' => 'Workforce', ], ], ], 'UpdateWorkteamRequest' => [ 'type' => 'structure', 'required' => [ 'WorkteamName', ], 'members' => [ 'WorkteamName' => [ 'shape' => 'WorkteamName', ], 'MemberDefinitions' => [ 'shape' => 'MemberDefinitions', ], 'Description' => [ 'shape' => 'String200', ], 'NotificationConfiguration' => [ 'shape' => 'NotificationConfiguration', ], ], ], 'UpdateWorkteamResponse' => [ 'type' => 'structure', 'required' => [ 'Workteam', ], 'members' => [ 'Workteam' => [ 'shape' => 'Workteam', ], ], ], 'Url' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '^(https|s3)://([^/]+)/?(.*)$', ], 'UserContext' => [ 'type' => 'structure', 'members' => [ 'UserProfileArn' => [ 'shape' => 'String', ], 'UserProfileName' => [ 'shape' => 'String', ], 'DomainId' => [ 'shape' => 'String', ], ], ], 'UserProfileArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:user-profile/.*', ], 'UserProfileDetails' => [ 'type' => 'structure', 'members' => [ 'DomainId' => [ 'shape' => 'DomainId', ], 'UserProfileName' => [ 'shape' => 'UserProfileName', ], 'Status' => [ 'shape' => 'UserProfileStatus', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'LastModifiedTime' => [ 'shape' => 'LastModifiedTime', ], ], ], 'UserProfileList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserProfileDetails', ], ], 'UserProfileName' => [ 'type' => 'string', 'max' => 63, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,62}', ], 'UserProfileSortKey' => [ 'type' => 'string', 'enum' => [ 'CreationTime', 'LastModifiedTime', ], ], 'UserProfileStatus' => [ 'type' => 'string', 'enum' => [ 'Deleting', 'Failed', 'InService', 'Pending', 'Updating', 'Update_Failed', 'Delete_Failed', ], ], 'UserSettings' => [ 'type' => 'structure', 'members' => [ 'ExecutionRole' => [ 'shape' => 'RoleArn', ], 'SecurityGroups' => [ 'shape' => 'SecurityGroupIds', ], 'SharingSettings' => [ 'shape' => 'SharingSettings', ], 'JupyterServerAppSettings' => [ 'shape' => 'JupyterServerAppSettings', ], 'KernelGatewayAppSettings' => [ 'shape' => 'KernelGatewayAppSettings', ], 'TensorBoardAppSettings' => [ 'shape' => 'TensorBoardAppSettings', ], ], ], 'VariantName' => [ 'type' => 'string', 'max' => 63, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,62}', ], 'VariantProperty' => [ 'type' => 'structure', 'required' => [ 'VariantPropertyType', ], 'members' => [ 'VariantPropertyType' => [ 'shape' => 'VariantPropertyType', ], ], ], 'VariantPropertyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VariantProperty', ], 'max' => 3, 'min' => 0, ], 'VariantPropertyType' => [ 'type' => 'string', 'enum' => [ 'DesiredInstanceCount', 'DesiredWeight', 'DataCaptureConfig', ], ], 'VariantWeight' => [ 'type' => 'float', 'min' => 0, ], 'VersionedArnOrName' => [ 'type' => 'string', 'max' => 176, 'min' => 1, 'pattern' => '(arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:[a-z\\-]*\\/)?([a-zA-Z0-9]([a-zA-Z0-9-]){0,62})(?<!-)(\\/[0-9]{1,5})?$', ], 'VolumeSizeInGB' => [ 'type' => 'integer', 'min' => 1, ], 'VpcConfig' => [ 'type' => 'structure', 'required' => [ 'SecurityGroupIds', 'Subnets', ], 'members' => [ 'SecurityGroupIds' => [ 'shape' => 'VpcSecurityGroupIds', ], 'Subnets' => [ 'shape' => 'Subnets', ], ], ], 'VpcId' => [ 'type' => 'string', 'max' => 32, 'pattern' => '[-0-9a-zA-Z]+', ], 'VpcSecurityGroupIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'max' => 5, 'min' => 1, ], 'WaitIntervalInSeconds' => [ 'type' => 'integer', 'max' => 3600, 'min' => 0, ], 'Workforce' => [ 'type' => 'structure', 'required' => [ 'WorkforceName', 'WorkforceArn', ], 'members' => [ 'WorkforceName' => [ 'shape' => 'WorkforceName', ], 'WorkforceArn' => [ 'shape' => 'WorkforceArn', ], 'LastUpdatedDate' => [ 'shape' => 'Timestamp', ], 'SourceIpConfig' => [ 'shape' => 'SourceIpConfig', ], 'SubDomain' => [ 'shape' => 'String', ], 'CognitoConfig' => [ 'shape' => 'CognitoConfig', ], 'OidcConfig' => [ 'shape' => 'OidcConfigForResponse', ], 'CreateDate' => [ 'shape' => 'Timestamp', ], ], ], 'WorkforceArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:workforce/.*', ], 'WorkforceName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-zA-Z0-9]([a-zA-Z0-9\\-]){0,62}$', ], 'Workforces' => [ 'type' => 'list', 'member' => [ 'shape' => 'Workforce', ], ], 'Workteam' => [ 'type' => 'structure', 'required' => [ 'WorkteamName', 'MemberDefinitions', 'WorkteamArn', 'Description', ], 'members' => [ 'WorkteamName' => [ 'shape' => 'WorkteamName', ], 'MemberDefinitions' => [ 'shape' => 'MemberDefinitions', ], 'WorkteamArn' => [ 'shape' => 'WorkteamArn', ], 'WorkforceArn' => [ 'shape' => 'WorkforceArn', ], 'ProductListingIds' => [ 'shape' => 'ProductListings', ], 'Description' => [ 'shape' => 'String200', ], 'SubDomain' => [ 'shape' => 'String', ], 'CreateDate' => [ 'shape' => 'Timestamp', ], 'LastUpdatedDate' => [ 'shape' => 'Timestamp', ], 'NotificationConfiguration' => [ 'shape' => 'NotificationConfiguration', ], ], ], 'WorkteamArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:workteam/.*', ], 'WorkteamName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,62}', ], 'Workteams' => [ 'type' => 'list', 'member' => [ 'shape' => 'Workteam', ], ], ],];
