<?php
// This file was auto-generated from sdk-root/src/data/quicksight/2018-04-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-04-01', 'endpointPrefix' => 'quicksight', 'jsonVersion' => '1.0', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon QuickSight', 'serviceId' => 'QuickSight', 'signatureVersion' => 'v4', 'uid' => 'quicksight-2018-04-01', ], 'operations' => [ 'CancelIngestion' => [ 'name' => 'CancelIngestion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts/{AwsAccountId}/data-sets/{DataSetId}/ingestions/{IngestionId}', ], 'input' => [ 'shape' => 'CancelIngestionRequest', ], 'output' => [ 'shape' => 'CancelIngestionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateAccountCustomization' => [ 'name' => 'CreateAccountCustomization', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{AwsAccountId}/customizations', ], 'input' => [ 'shape' => 'CreateAccountCustomizationRequest', ], 'output' => [ 'shape' => 'CreateAccountCustomizationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'CreateAnalysis' => [ 'name' => 'CreateAnalysis', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{AwsAccountId}/analyses/{AnalysisId}', ], 'input' => [ 'shape' => 'CreateAnalysisRequest', ], 'output' => [ 'shape' => 'CreateAnalysisResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateDashboard' => [ 'name' => 'CreateDashboard', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{AwsAccountId}/dashboards/{DashboardId}', ], 'input' => [ 'shape' => 'CreateDashboardRequest', ], 'output' => [ 'shape' => 'CreateDashboardResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateDataSet' => [ 'name' => 'CreateDataSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{AwsAccountId}/data-sets', ], 'input' => [ 'shape' => 'CreateDataSetRequest', ], 'output' => [ 'shape' => 'CreateDataSetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateDataSource' => [ 'name' => 'CreateDataSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{AwsAccountId}/data-sources', ], 'input' => [ 'shape' => 'CreateDataSourceRequest', ], 'output' => [ 'shape' => 'CreateDataSourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateFolder' => [ 'name' => 'CreateFolder', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{AwsAccountId}/folders/{FolderId}', ], 'input' => [ 'shape' => 'CreateFolderRequest', ], 'output' => [ 'shape' => 'CreateFolderResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateFolderMembership' => [ 'name' => 'CreateFolderMembership', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{AwsAccountId}/folders/{FolderId}/members/{MemberType}/{MemberId}', ], 'input' => [ 'shape' => 'CreateFolderMembershipRequest', ], 'output' => [ 'shape' => 'CreateFolderMembershipResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateGroup' => [ 'name' => 'CreateGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{AwsAccountId}/namespaces/{Namespace}/groups', ], 'input' => [ 'shape' => 'CreateGroupRequest', ], 'output' => [ 'shape' => 'CreateGroupResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'PreconditionNotMetException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'CreateGroupMembership' => [ 'name' => 'CreateGroupMembership', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{AwsAccountId}/namespaces/{Namespace}/groups/{GroupName}/members/{MemberName}', ], 'input' => [ 'shape' => 'CreateGroupMembershipRequest', ], 'output' => [ 'shape' => 'CreateGroupMembershipResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'PreconditionNotMetException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'CreateIAMPolicyAssignment' => [ 'name' => 'CreateIAMPolicyAssignment', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{AwsAccountId}/namespaces/{Namespace}/iam-policy-assignments/', ], 'input' => [ 'shape' => 'CreateIAMPolicyAssignmentRequest', ], 'output' => [ 'shape' => 'CreateIAMPolicyAssignmentResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConcurrentUpdatingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateIngestion' => [ 'name' => 'CreateIngestion', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{AwsAccountId}/data-sets/{DataSetId}/ingestions/{IngestionId}', ], 'input' => [ 'shape' => 'CreateIngestionRequest', ], 'output' => [ 'shape' => 'CreateIngestionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateNamespace' => [ 'name' => 'CreateNamespace', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{AwsAccountId}', ], 'input' => [ 'shape' => 'CreateNamespaceRequest', ], 'output' => [ 'shape' => 'CreateNamespaceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'PreconditionNotMetException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'CreateTemplate' => [ 'name' => 'CreateTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{AwsAccountId}/templates/{TemplateId}', ], 'input' => [ 'shape' => 'CreateTemplateRequest', ], 'output' => [ 'shape' => 'CreateTemplateResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateTemplateAlias' => [ 'name' => 'CreateTemplateAlias', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{AwsAccountId}/templates/{TemplateId}/aliases/{AliasName}', ], 'input' => [ 'shape' => 'CreateTemplateAliasRequest', ], 'output' => [ 'shape' => 'CreateTemplateAliasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateTheme' => [ 'name' => 'CreateTheme', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{AwsAccountId}/themes/{ThemeId}', ], 'input' => [ 'shape' => 'CreateThemeRequest', ], 'output' => [ 'shape' => 'CreateThemeResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateThemeAlias' => [ 'name' => 'CreateThemeAlias', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{AwsAccountId}/themes/{ThemeId}/aliases/{AliasName}', ], 'input' => [ 'shape' => 'CreateThemeAliasRequest', ], 'output' => [ 'shape' => 'CreateThemeAliasResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteAccountCustomization' => [ 'name' => 'DeleteAccountCustomization', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts/{AwsAccountId}/customizations', ], 'input' => [ 'shape' => 'DeleteAccountCustomizationRequest', ], 'output' => [ 'shape' => 'DeleteAccountCustomizationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'DeleteAnalysis' => [ 'name' => 'DeleteAnalysis', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts/{AwsAccountId}/analyses/{AnalysisId}', ], 'input' => [ 'shape' => 'DeleteAnalysisRequest', ], 'output' => [ 'shape' => 'DeleteAnalysisResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteDashboard' => [ 'name' => 'DeleteDashboard', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts/{AwsAccountId}/dashboards/{DashboardId}', ], 'input' => [ 'shape' => 'DeleteDashboardRequest', ], 'output' => [ 'shape' => 'DeleteDashboardResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteDataSet' => [ 'name' => 'DeleteDataSet', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts/{AwsAccountId}/data-sets/{DataSetId}', ], 'input' => [ 'shape' => 'DeleteDataSetRequest', ], 'output' => [ 'shape' => 'DeleteDataSetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteDataSource' => [ 'name' => 'DeleteDataSource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts/{AwsAccountId}/data-sources/{DataSourceId}', ], 'input' => [ 'shape' => 'DeleteDataSourceRequest', ], 'output' => [ 'shape' => 'DeleteDataSourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteFolder' => [ 'name' => 'DeleteFolder', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts/{AwsAccountId}/folders/{FolderId}', ], 'input' => [ 'shape' => 'DeleteFolderRequest', ], 'output' => [ 'shape' => 'DeleteFolderResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'PreconditionNotMetException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteFolderMembership' => [ 'name' => 'DeleteFolderMembership', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts/{AwsAccountId}/folders/{FolderId}/members/{MemberType}/{MemberId}', ], 'input' => [ 'shape' => 'DeleteFolderMembershipRequest', ], 'output' => [ 'shape' => 'DeleteFolderMembershipResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteGroup' => [ 'name' => 'DeleteGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts/{AwsAccountId}/namespaces/{Namespace}/groups/{GroupName}', ], 'input' => [ 'shape' => 'DeleteGroupRequest', ], 'output' => [ 'shape' => 'DeleteGroupResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'PreconditionNotMetException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'DeleteGroupMembership' => [ 'name' => 'DeleteGroupMembership', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts/{AwsAccountId}/namespaces/{Namespace}/groups/{GroupName}/members/{MemberName}', ], 'input' => [ 'shape' => 'DeleteGroupMembershipRequest', ], 'output' => [ 'shape' => 'DeleteGroupMembershipResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'PreconditionNotMetException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'DeleteIAMPolicyAssignment' => [ 'name' => 'DeleteIAMPolicyAssignment', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts/{AwsAccountId}/namespace/{Namespace}/iam-policy-assignments/{AssignmentName}', ], 'input' => [ 'shape' => 'DeleteIAMPolicyAssignmentRequest', ], 'output' => [ 'shape' => 'DeleteIAMPolicyAssignmentResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConcurrentUpdatingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteNamespace' => [ 'name' => 'DeleteNamespace', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts/{AwsAccountId}/namespaces/{Namespace}', ], 'input' => [ 'shape' => 'DeleteNamespaceRequest', ], 'output' => [ 'shape' => 'DeleteNamespaceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'PreconditionNotMetException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'DeleteTemplate' => [ 'name' => 'DeleteTemplate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts/{AwsAccountId}/templates/{TemplateId}', ], 'input' => [ 'shape' => 'DeleteTemplateRequest', ], 'output' => [ 'shape' => 'DeleteTemplateResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteTemplateAlias' => [ 'name' => 'DeleteTemplateAlias', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts/{AwsAccountId}/templates/{TemplateId}/aliases/{AliasName}', ], 'input' => [ 'shape' => 'DeleteTemplateAliasRequest', ], 'output' => [ 'shape' => 'DeleteTemplateAliasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteTheme' => [ 'name' => 'DeleteTheme', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts/{AwsAccountId}/themes/{ThemeId}', ], 'input' => [ 'shape' => 'DeleteThemeRequest', ], 'output' => [ 'shape' => 'DeleteThemeResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteThemeAlias' => [ 'name' => 'DeleteThemeAlias', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts/{AwsAccountId}/themes/{ThemeId}/aliases/{AliasName}', ], 'input' => [ 'shape' => 'DeleteThemeAliasRequest', ], 'output' => [ 'shape' => 'DeleteThemeAliasResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteUser' => [ 'name' => 'DeleteUser', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts/{AwsAccountId}/namespaces/{Namespace}/users/{UserName}', ], 'input' => [ 'shape' => 'DeleteUserRequest', ], 'output' => [ 'shape' => 'DeleteUserResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'PreconditionNotMetException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'DeleteUserByPrincipalId' => [ 'name' => 'DeleteUserByPrincipalId', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts/{AwsAccountId}/namespaces/{Namespace}/user-principals/{PrincipalId}', ], 'input' => [ 'shape' => 'DeleteUserByPrincipalIdRequest', ], 'output' => [ 'shape' => 'DeleteUserByPrincipalIdResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'PreconditionNotMetException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'DescribeAccountCustomization' => [ 'name' => 'DescribeAccountCustomization', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/customizations', ], 'input' => [ 'shape' => 'DescribeAccountCustomizationRequest', ], 'output' => [ 'shape' => 'DescribeAccountCustomizationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'DescribeAccountSettings' => [ 'name' => 'DescribeAccountSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/settings', ], 'input' => [ 'shape' => 'DescribeAccountSettingsRequest', ], 'output' => [ 'shape' => 'DescribeAccountSettingsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'DescribeAnalysis' => [ 'name' => 'DescribeAnalysis', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/analyses/{AnalysisId}', ], 'input' => [ 'shape' => 'DescribeAnalysisRequest', ], 'output' => [ 'shape' => 'DescribeAnalysisResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeAnalysisPermissions' => [ 'name' => 'DescribeAnalysisPermissions', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/analyses/{AnalysisId}/permissions', ], 'input' => [ 'shape' => 'DescribeAnalysisPermissionsRequest', ], 'output' => [ 'shape' => 'DescribeAnalysisPermissionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeDashboard' => [ 'name' => 'DescribeDashboard', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/dashboards/{DashboardId}', ], 'input' => [ 'shape' => 'DescribeDashboardRequest', ], 'output' => [ 'shape' => 'DescribeDashboardResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeDashboardPermissions' => [ 'name' => 'DescribeDashboardPermissions', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/dashboards/{DashboardId}/permissions', ], 'input' => [ 'shape' => 'DescribeDashboardPermissionsRequest', ], 'output' => [ 'shape' => 'DescribeDashboardPermissionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeDataSet' => [ 'name' => 'DescribeDataSet', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/data-sets/{DataSetId}', ], 'input' => [ 'shape' => 'DescribeDataSetRequest', ], 'output' => [ 'shape' => 'DescribeDataSetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeDataSetPermissions' => [ 'name' => 'DescribeDataSetPermissions', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/data-sets/{DataSetId}/permissions', ], 'input' => [ 'shape' => 'DescribeDataSetPermissionsRequest', ], 'output' => [ 'shape' => 'DescribeDataSetPermissionsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeDataSource' => [ 'name' => 'DescribeDataSource', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/data-sources/{DataSourceId}', ], 'input' => [ 'shape' => 'DescribeDataSourceRequest', ], 'output' => [ 'shape' => 'DescribeDataSourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeDataSourcePermissions' => [ 'name' => 'DescribeDataSourcePermissions', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/data-sources/{DataSourceId}/permissions', ], 'input' => [ 'shape' => 'DescribeDataSourcePermissionsRequest', ], 'output' => [ 'shape' => 'DescribeDataSourcePermissionsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeFolder' => [ 'name' => 'DescribeFolder', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/folders/{FolderId}', ], 'input' => [ 'shape' => 'DescribeFolderRequest', ], 'output' => [ 'shape' => 'DescribeFolderResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeFolderPermissions' => [ 'name' => 'DescribeFolderPermissions', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/folders/{FolderId}/permissions', ], 'input' => [ 'shape' => 'DescribeFolderPermissionsRequest', ], 'output' => [ 'shape' => 'DescribeFolderPermissionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeFolderResolvedPermissions' => [ 'name' => 'DescribeFolderResolvedPermissions', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/folders/{FolderId}/resolved-permissions', ], 'input' => [ 'shape' => 'DescribeFolderResolvedPermissionsRequest', ], 'output' => [ 'shape' => 'DescribeFolderResolvedPermissionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeGroup' => [ 'name' => 'DescribeGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/namespaces/{Namespace}/groups/{GroupName}', ], 'input' => [ 'shape' => 'DescribeGroupRequest', ], 'output' => [ 'shape' => 'DescribeGroupResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'PreconditionNotMetException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'DescribeIAMPolicyAssignment' => [ 'name' => 'DescribeIAMPolicyAssignment', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/namespaces/{Namespace}/iam-policy-assignments/{AssignmentName}', ], 'input' => [ 'shape' => 'DescribeIAMPolicyAssignmentRequest', ], 'output' => [ 'shape' => 'DescribeIAMPolicyAssignmentResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeIngestion' => [ 'name' => 'DescribeIngestion', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/data-sets/{DataSetId}/ingestions/{IngestionId}', ], 'input' => [ 'shape' => 'DescribeIngestionRequest', ], 'output' => [ 'shape' => 'DescribeIngestionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeNamespace' => [ 'name' => 'DescribeNamespace', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/namespaces/{Namespace}', ], 'input' => [ 'shape' => 'DescribeNamespaceRequest', ], 'output' => [ 'shape' => 'DescribeNamespaceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'DescribeTemplate' => [ 'name' => 'DescribeTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/templates/{TemplateId}', ], 'input' => [ 'shape' => 'DescribeTemplateRequest', ], 'output' => [ 'shape' => 'DescribeTemplateResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeTemplateAlias' => [ 'name' => 'DescribeTemplateAlias', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/templates/{TemplateId}/aliases/{AliasName}', ], 'input' => [ 'shape' => 'DescribeTemplateAliasRequest', ], 'output' => [ 'shape' => 'DescribeTemplateAliasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeTemplatePermissions' => [ 'name' => 'DescribeTemplatePermissions', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/templates/{TemplateId}/permissions', ], 'input' => [ 'shape' => 'DescribeTemplatePermissionsRequest', ], 'output' => [ 'shape' => 'DescribeTemplatePermissionsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeTheme' => [ 'name' => 'DescribeTheme', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/themes/{ThemeId}', ], 'input' => [ 'shape' => 'DescribeThemeRequest', ], 'output' => [ 'shape' => 'DescribeThemeResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeThemeAlias' => [ 'name' => 'DescribeThemeAlias', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/themes/{ThemeId}/aliases/{AliasName}', ], 'input' => [ 'shape' => 'DescribeThemeAliasRequest', ], 'output' => [ 'shape' => 'DescribeThemeAliasResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeThemePermissions' => [ 'name' => 'DescribeThemePermissions', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/themes/{ThemeId}/permissions', ], 'input' => [ 'shape' => 'DescribeThemePermissionsRequest', ], 'output' => [ 'shape' => 'DescribeThemePermissionsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeUser' => [ 'name' => 'DescribeUser', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/namespaces/{Namespace}/users/{UserName}', ], 'input' => [ 'shape' => 'DescribeUserRequest', ], 'output' => [ 'shape' => 'DescribeUserResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'PreconditionNotMetException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'GetDashboardEmbedUrl' => [ 'name' => 'GetDashboardEmbedUrl', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/dashboards/{DashboardId}/embed-url', ], 'input' => [ 'shape' => 'GetDashboardEmbedUrlRequest', ], 'output' => [ 'shape' => 'GetDashboardEmbedUrlResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'DomainNotWhitelistedException', ], [ 'shape' => 'QuickSightUserNotFoundException', ], [ 'shape' => 'IdentityTypeNotSupportedException', ], [ 'shape' => 'SessionLifetimeInMinutesInvalidException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'UnsupportedPricingPlanException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'GetSessionEmbedUrl' => [ 'name' => 'GetSessionEmbedUrl', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/session-embed-url', ], 'input' => [ 'shape' => 'GetSessionEmbedUrlRequest', ], 'output' => [ 'shape' => 'GetSessionEmbedUrlResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'QuickSightUserNotFoundException', ], [ 'shape' => 'SessionLifetimeInMinutesInvalidException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListAnalyses' => [ 'name' => 'ListAnalyses', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/analyses', ], 'input' => [ 'shape' => 'ListAnalysesRequest', ], 'output' => [ 'shape' => 'ListAnalysesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListDashboardVersions' => [ 'name' => 'ListDashboardVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/dashboards/{DashboardId}/versions', ], 'input' => [ 'shape' => 'ListDashboardVersionsRequest', ], 'output' => [ 'shape' => 'ListDashboardVersionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListDashboards' => [ 'name' => 'ListDashboards', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/dashboards', ], 'input' => [ 'shape' => 'ListDashboardsRequest', ], 'output' => [ 'shape' => 'ListDashboardsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListDataSets' => [ 'name' => 'ListDataSets', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/data-sets', ], 'input' => [ 'shape' => 'ListDataSetsRequest', ], 'output' => [ 'shape' => 'ListDataSetsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListDataSources' => [ 'name' => 'ListDataSources', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/data-sources', ], 'input' => [ 'shape' => 'ListDataSourcesRequest', ], 'output' => [ 'shape' => 'ListDataSourcesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListFolderMembers' => [ 'name' => 'ListFolderMembers', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/folders/{FolderId}/members', ], 'input' => [ 'shape' => 'ListFolderMembersRequest', ], 'output' => [ 'shape' => 'ListFolderMembersResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListFolders' => [ 'name' => 'ListFolders', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/folders', ], 'input' => [ 'shape' => 'ListFoldersRequest', ], 'output' => [ 'shape' => 'ListFoldersResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListGroupMemberships' => [ 'name' => 'ListGroupMemberships', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/namespaces/{Namespace}/groups/{GroupName}/members', ], 'input' => [ 'shape' => 'ListGroupMembershipsRequest', ], 'output' => [ 'shape' => 'ListGroupMembershipsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'PreconditionNotMetException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'ListGroups' => [ 'name' => 'ListGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/namespaces/{Namespace}/groups', ], 'input' => [ 'shape' => 'ListGroupsRequest', ], 'output' => [ 'shape' => 'ListGroupsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'PreconditionNotMetException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'ListIAMPolicyAssignments' => [ 'name' => 'ListIAMPolicyAssignments', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/namespaces/{Namespace}/iam-policy-assignments', ], 'input' => [ 'shape' => 'ListIAMPolicyAssignmentsRequest', ], 'output' => [ 'shape' => 'ListIAMPolicyAssignmentsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListIAMPolicyAssignmentsForUser' => [ 'name' => 'ListIAMPolicyAssignmentsForUser', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/namespaces/{Namespace}/users/{UserName}/iam-policy-assignments', ], 'input' => [ 'shape' => 'ListIAMPolicyAssignmentsForUserRequest', ], 'output' => [ 'shape' => 'ListIAMPolicyAssignmentsForUserResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConcurrentUpdatingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListIngestions' => [ 'name' => 'ListIngestions', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/data-sets/{DataSetId}/ingestions', ], 'input' => [ 'shape' => 'ListIngestionsRequest', ], 'output' => [ 'shape' => 'ListIngestionsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListNamespaces' => [ 'name' => 'ListNamespaces', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/namespaces', ], 'input' => [ 'shape' => 'ListNamespacesRequest', ], 'output' => [ 'shape' => 'ListNamespacesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'PreconditionNotMetException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/resources/{ResourceArn}/tags', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListTemplateAliases' => [ 'name' => 'ListTemplateAliases', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/templates/{TemplateId}/aliases', ], 'input' => [ 'shape' => 'ListTemplateAliasesRequest', ], 'output' => [ 'shape' => 'ListTemplateAliasesResponse', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListTemplateVersions' => [ 'name' => 'ListTemplateVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/templates/{TemplateId}/versions', ], 'input' => [ 'shape' => 'ListTemplateVersionsRequest', ], 'output' => [ 'shape' => 'ListTemplateVersionsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListTemplates' => [ 'name' => 'ListTemplates', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/templates', ], 'input' => [ 'shape' => 'ListTemplatesRequest', ], 'output' => [ 'shape' => 'ListTemplatesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListThemeAliases' => [ 'name' => 'ListThemeAliases', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/themes/{ThemeId}/aliases', ], 'input' => [ 'shape' => 'ListThemeAliasesRequest', ], 'output' => [ 'shape' => 'ListThemeAliasesResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListThemeVersions' => [ 'name' => 'ListThemeVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/themes/{ThemeId}/versions', ], 'input' => [ 'shape' => 'ListThemeVersionsRequest', ], 'output' => [ 'shape' => 'ListThemeVersionsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListThemes' => [ 'name' => 'ListThemes', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/themes', ], 'input' => [ 'shape' => 'ListThemesRequest', ], 'output' => [ 'shape' => 'ListThemesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListUserGroups' => [ 'name' => 'ListUserGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/namespaces/{Namespace}/users/{UserName}/groups', ], 'input' => [ 'shape' => 'ListUserGroupsRequest', ], 'output' => [ 'shape' => 'ListUserGroupsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'PreconditionNotMetException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'ListUsers' => [ 'name' => 'ListUsers', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts/{AwsAccountId}/namespaces/{Namespace}/users', ], 'input' => [ 'shape' => 'ListUsersRequest', ], 'output' => [ 'shape' => 'ListUsersResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'PreconditionNotMetException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'RegisterUser' => [ 'name' => 'RegisterUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{AwsAccountId}/namespaces/{Namespace}/users', ], 'input' => [ 'shape' => 'RegisterUserRequest', ], 'output' => [ 'shape' => 'RegisterUserResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'PreconditionNotMetException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'RestoreAnalysis' => [ 'name' => 'RestoreAnalysis', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{AwsAccountId}/restore/analyses/{AnalysisId}', ], 'input' => [ 'shape' => 'RestoreAnalysisRequest', ], 'output' => [ 'shape' => 'RestoreAnalysisResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'SearchAnalyses' => [ 'name' => 'SearchAnalyses', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{AwsAccountId}/search/analyses', ], 'input' => [ 'shape' => 'SearchAnalysesRequest', ], 'output' => [ 'shape' => 'SearchAnalysesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'SearchDashboards' => [ 'name' => 'SearchDashboards', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{AwsAccountId}/search/dashboards', ], 'input' => [ 'shape' => 'SearchDashboardsRequest', ], 'output' => [ 'shape' => 'SearchDashboardsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'SearchFolders' => [ 'name' => 'SearchFolders', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{AwsAccountId}/search/folders', ], 'input' => [ 'shape' => 'SearchFoldersRequest', ], 'output' => [ 'shape' => 'SearchFoldersResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/resources/{ResourceArn}/tags', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/resources/{ResourceArn}/tags', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateAccountCustomization' => [ 'name' => 'UpdateAccountCustomization', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{AwsAccountId}/customizations', ], 'input' => [ 'shape' => 'UpdateAccountCustomizationRequest', ], 'output' => [ 'shape' => 'UpdateAccountCustomizationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'UpdateAccountSettings' => [ 'name' => 'UpdateAccountSettings', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{AwsAccountId}/settings', ], 'input' => [ 'shape' => 'UpdateAccountSettingsRequest', ], 'output' => [ 'shape' => 'UpdateAccountSettingsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'UpdateAnalysis' => [ 'name' => 'UpdateAnalysis', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{AwsAccountId}/analyses/{AnalysisId}', ], 'input' => [ 'shape' => 'UpdateAnalysisRequest', ], 'output' => [ 'shape' => 'UpdateAnalysisResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateAnalysisPermissions' => [ 'name' => 'UpdateAnalysisPermissions', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{AwsAccountId}/analyses/{AnalysisId}/permissions', ], 'input' => [ 'shape' => 'UpdateAnalysisPermissionsRequest', ], 'output' => [ 'shape' => 'UpdateAnalysisPermissionsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateDashboard' => [ 'name' => 'UpdateDashboard', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{AwsAccountId}/dashboards/{DashboardId}', ], 'input' => [ 'shape' => 'UpdateDashboardRequest', ], 'output' => [ 'shape' => 'UpdateDashboardResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateDashboardPermissions' => [ 'name' => 'UpdateDashboardPermissions', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{AwsAccountId}/dashboards/{DashboardId}/permissions', ], 'input' => [ 'shape' => 'UpdateDashboardPermissionsRequest', ], 'output' => [ 'shape' => 'UpdateDashboardPermissionsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateDashboardPublishedVersion' => [ 'name' => 'UpdateDashboardPublishedVersion', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{AwsAccountId}/dashboards/{DashboardId}/versions/{VersionNumber}', ], 'input' => [ 'shape' => 'UpdateDashboardPublishedVersionRequest', ], 'output' => [ 'shape' => 'UpdateDashboardPublishedVersionResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateDataSet' => [ 'name' => 'UpdateDataSet', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{AwsAccountId}/data-sets/{DataSetId}', ], 'input' => [ 'shape' => 'UpdateDataSetRequest', ], 'output' => [ 'shape' => 'UpdateDataSetResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateDataSetPermissions' => [ 'name' => 'UpdateDataSetPermissions', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{AwsAccountId}/data-sets/{DataSetId}/permissions', ], 'input' => [ 'shape' => 'UpdateDataSetPermissionsRequest', ], 'output' => [ 'shape' => 'UpdateDataSetPermissionsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateDataSource' => [ 'name' => 'UpdateDataSource', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{AwsAccountId}/data-sources/{DataSourceId}', ], 'input' => [ 'shape' => 'UpdateDataSourceRequest', ], 'output' => [ 'shape' => 'UpdateDataSourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateDataSourcePermissions' => [ 'name' => 'UpdateDataSourcePermissions', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts/{AwsAccountId}/data-sources/{DataSourceId}/permissions', ], 'input' => [ 'shape' => 'UpdateDataSourcePermissionsRequest', ], 'output' => [ 'shape' => 'UpdateDataSourcePermissionsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateFolder' => [ 'name' => 'UpdateFolder', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{AwsAccountId}/folders/{FolderId}', ], 'input' => [ 'shape' => 'UpdateFolderRequest', ], 'output' => [ 'shape' => 'UpdateFolderResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateFolderPermissions' => [ 'name' => 'UpdateFolderPermissions', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{AwsAccountId}/folders/{FolderId}/permissions', ], 'input' => [ 'shape' => 'UpdateFolderPermissionsRequest', ], 'output' => [ 'shape' => 'UpdateFolderPermissionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateGroup' => [ 'name' => 'UpdateGroup', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{AwsAccountId}/namespaces/{Namespace}/groups/{GroupName}', ], 'input' => [ 'shape' => 'UpdateGroupRequest', ], 'output' => [ 'shape' => 'UpdateGroupResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'PreconditionNotMetException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'UpdateIAMPolicyAssignment' => [ 'name' => 'UpdateIAMPolicyAssignment', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{AwsAccountId}/namespaces/{Namespace}/iam-policy-assignments/{AssignmentName}', ], 'input' => [ 'shape' => 'UpdateIAMPolicyAssignmentRequest', ], 'output' => [ 'shape' => 'UpdateIAMPolicyAssignmentResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConcurrentUpdatingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateTemplate' => [ 'name' => 'UpdateTemplate', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{AwsAccountId}/templates/{TemplateId}', ], 'input' => [ 'shape' => 'UpdateTemplateRequest', ], 'output' => [ 'shape' => 'UpdateTemplateResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateTemplateAlias' => [ 'name' => 'UpdateTemplateAlias', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{AwsAccountId}/templates/{TemplateId}/aliases/{AliasName}', ], 'input' => [ 'shape' => 'UpdateTemplateAliasRequest', ], 'output' => [ 'shape' => 'UpdateTemplateAliasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateTemplatePermissions' => [ 'name' => 'UpdateTemplatePermissions', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{AwsAccountId}/templates/{TemplateId}/permissions', ], 'input' => [ 'shape' => 'UpdateTemplatePermissionsRequest', ], 'output' => [ 'shape' => 'UpdateTemplatePermissionsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateTheme' => [ 'name' => 'UpdateTheme', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{AwsAccountId}/themes/{ThemeId}', ], 'input' => [ 'shape' => 'UpdateThemeRequest', ], 'output' => [ 'shape' => 'UpdateThemeResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateThemeAlias' => [ 'name' => 'UpdateThemeAlias', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{AwsAccountId}/themes/{ThemeId}/aliases/{AliasName}', ], 'input' => [ 'shape' => 'UpdateThemeAliasRequest', ], 'output' => [ 'shape' => 'UpdateThemeAliasResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateThemePermissions' => [ 'name' => 'UpdateThemePermissions', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{AwsAccountId}/themes/{ThemeId}/permissions', ], 'input' => [ 'shape' => 'UpdateThemePermissionsRequest', ], 'output' => [ 'shape' => 'UpdateThemePermissionsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedUserEditionException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateUser' => [ 'name' => 'UpdateUser', 'http' => [ 'method' => 'PUT', 'requestUri' => '/accounts/{AwsAccountId}/namespaces/{Namespace}/users/{UserName}', ], 'input' => [ 'shape' => 'UpdateUserRequest', ], 'output' => [ 'shape' => 'UpdateUserResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'PreconditionNotMetException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], 'AccountCustomization' => [ 'type' => 'structure', 'members' => [ 'DefaultTheme' => [ 'shape' => 'Arn', ], ], ], 'AccountSettings' => [ 'type' => 'structure', 'members' => [ 'AccountName' => [ 'shape' => 'String', ], 'Edition' => [ 'shape' => 'Edition', ], 'DefaultNamespace' => [ 'shape' => 'Namespace', ], 'NotificationEmail' => [ 'shape' => 'String', ], ], ], 'ActionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 16, 'min' => 1, ], 'ActiveIAMPolicyAssignment' => [ 'type' => 'structure', 'members' => [ 'AssignmentName' => [ 'shape' => 'IAMPolicyAssignmentName', ], 'PolicyArn' => [ 'shape' => 'Arn', ], ], ], 'ActiveIAMPolicyAssignmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ActiveIAMPolicyAssignment', ], ], 'AdHocFilteringOption' => [ 'type' => 'structure', 'members' => [ 'AvailabilityStatus' => [ 'shape' => 'DashboardBehavior', ], ], ], 'AdditionalDashboardIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RestrictiveResourceId', ], 'max' => 20, 'min' => 1, ], 'AliasName' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[\\w\\-]+|(\\$LATEST)|(\\$PUBLISHED)', ], 'AmazonElasticsearchParameters' => [ 'type' => 'structure', 'required' => [ 'Domain', ], 'members' => [ 'Domain' => [ 'shape' => 'Domain', ], ], ], 'Analysis' => [ 'type' => 'structure', 'members' => [ 'AnalysisId' => [ 'shape' => 'RestrictiveResourceId', ], 'Arn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'AnalysisName', ], 'Status' => [ 'shape' => 'ResourceStatus', ], 'Errors' => [ 'shape' => 'AnalysisErrorList', ], 'DataSetArns' => [ 'shape' => 'DataSetArnsList', ], 'ThemeArn' => [ 'shape' => 'Arn', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'Sheets' => [ 'shape' => 'SheetList', ], ], ], 'AnalysisError' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'AnalysisErrorType', ], 'Message' => [ 'shape' => 'NonEmptyString', ], ], ], 'AnalysisErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalysisError', ], 'min' => 1, ], 'AnalysisErrorType' => [ 'type' => 'string', 'enum' => [ 'ACCESS_DENIED', 'SOURCE_NOT_FOUND', 'DATA_SET_NOT_FOUND', 'INTERNAL_FAILURE', 'PARAMETER_VALUE_INCOMPATIBLE', 'PARAMETER_TYPE_INVALID', 'PARAMETER_NOT_FOUND', 'COLUMN_TYPE_MISMATCH', 'COLUMN_GEOGRAPHIC_ROLE_MISMATCH', 'COLUMN_REPLACEMENT_MISSING', ], ], 'AnalysisFilterAttribute' => [ 'type' => 'string', 'enum' => [ 'QUICKSIGHT_USER', ], ], 'AnalysisName' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'AnalysisSearchFilter' => [ 'type' => 'structure', 'members' => [ 'Operator' => [ 'shape' => 'FilterOperator', ], 'Name' => [ 'shape' => 'AnalysisFilterAttribute', ], 'Value' => [ 'shape' => 'String', ], ], ], 'AnalysisSearchFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalysisSearchFilter', ], 'max' => 1, 'min' => 1, ], 'AnalysisSourceEntity' => [ 'type' => 'structure', 'members' => [ 'SourceTemplate' => [ 'shape' => 'AnalysisSourceTemplate', ], ], ], 'AnalysisSourceTemplate' => [ 'type' => 'structure', 'required' => [ 'DataSetReferences', 'Arn', ], 'members' => [ 'DataSetReferences' => [ 'shape' => 'DataSetReferenceList', ], 'Arn' => [ 'shape' => 'Arn', ], ], ], 'AnalysisSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'AnalysisId' => [ 'shape' => 'RestrictiveResourceId', ], 'Name' => [ 'shape' => 'AnalysisName', ], 'Status' => [ 'shape' => 'ResourceStatus', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], ], ], 'AnalysisSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalysisSummary', ], 'max' => 100, ], 'Arn' => [ 'type' => 'string', ], 'AssignmentStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DRAFT', 'DISABLED', ], ], 'AthenaParameters' => [ 'type' => 'structure', 'members' => [ 'WorkGroup' => [ 'shape' => 'WorkGroup', ], ], ], 'AuroraParameters' => [ 'type' => 'structure', 'required' => [ 'Host', 'Port', 'Database', ], 'members' => [ 'Host' => [ 'shape' => 'Host', ], 'Port' => [ 'shape' => 'Port', ], 'Database' => [ 'shape' => 'Database', ], ], ], 'AuroraPostgreSqlParameters' => [ 'type' => 'structure', 'required' => [ 'Host', 'Port', 'Database', ], 'members' => [ 'Host' => [ 'shape' => 'Host', ], 'Port' => [ 'shape' => 'Port', ], 'Database' => [ 'shape' => 'Database', ], ], ], 'AwsAccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^[0-9]{12}$', ], 'AwsAndAccountId' => [ 'type' => 'string', 'pattern' => '^(aws|[0-9]{12})$', ], 'AwsIotAnalyticsParameters' => [ 'type' => 'structure', 'required' => [ 'DataSetName', ], 'members' => [ 'DataSetName' => [ 'shape' => 'DataSetName', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'BorderStyle' => [ 'type' => 'structure', 'members' => [ 'Show' => [ 'shape' => 'boolean', 'box' => true, ], ], ], 'CalculatedColumn' => [ 'type' => 'structure', 'required' => [ 'ColumnName', 'ColumnId', 'Expression', ], 'members' => [ 'ColumnName' => [ 'shape' => 'ColumnName', ], 'ColumnId' => [ 'shape' => 'ColumnId', ], 'Expression' => [ 'shape' => 'Expression', ], ], ], 'CalculatedColumnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CalculatedColumn', ], 'max' => 128, 'min' => 1, ], 'CancelIngestionRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'DataSetId', 'IngestionId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'DataSetId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'DataSetId', ], 'IngestionId' => [ 'shape' => 'IngestionId', 'location' => 'uri', 'locationName' => 'IngestionId', ], ], ], 'CancelIngestionResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'IngestionId' => [ 'shape' => 'IngestionId', ], 'RequestId' => [ 'shape' => 'string', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'CastColumnTypeOperation' => [ 'type' => 'structure', 'required' => [ 'ColumnName', 'NewColumnType', ], 'members' => [ 'ColumnName' => [ 'shape' => 'ColumnName', ], 'NewColumnType' => [ 'shape' => 'ColumnDataType', ], 'Format' => [ 'shape' => 'TypeCastFormat', ], ], ], 'Catalog' => [ 'type' => 'string', 'max' => 128, ], 'ClusterId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ColorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HexColor', ], 'max' => 100, ], 'ColumnDataType' => [ 'type' => 'string', 'enum' => [ 'STRING', 'INTEGER', 'DECIMAL', 'DATETIME', ], ], 'ColumnDescription' => [ 'type' => 'structure', 'members' => [ 'Text' => [ 'shape' => 'ColumnDescriptiveText', ], ], ], 'ColumnDescriptiveText' => [ 'type' => 'string', 'max' => 500, 'min' => 0, ], 'ColumnGroup' => [ 'type' => 'structure', 'members' => [ 'GeoSpatialColumnGroup' => [ 'shape' => 'GeoSpatialColumnGroup', ], ], ], 'ColumnGroupColumnSchema' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], ], ], 'ColumnGroupColumnSchemaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnGroupColumnSchema', ], 'max' => 500, ], 'ColumnGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnGroup', ], 'max' => 8, 'min' => 1, ], 'ColumnGroupName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ColumnGroupSchema' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'ColumnGroupColumnSchemaList' => [ 'shape' => 'ColumnGroupColumnSchemaList', ], ], ], 'ColumnGroupSchemaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnGroupSchema', ], 'max' => 500, ], 'ColumnId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ColumnLevelPermissionRule' => [ 'type' => 'structure', 'members' => [ 'Principals' => [ 'shape' => 'PrincipalList', ], 'ColumnNames' => [ 'shape' => 'ColumnNameList', ], ], ], 'ColumnLevelPermissionRuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnLevelPermissionRule', ], 'min' => 1, ], 'ColumnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnName', ], 'max' => 16, 'min' => 1, ], 'ColumnName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'ColumnNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'min' => 1, ], 'ColumnSchema' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'DataType' => [ 'shape' => 'String', ], 'GeographicRole' => [ 'shape' => 'String', ], ], ], 'ColumnSchemaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnSchema', ], 'max' => 500, ], 'ColumnTag' => [ 'type' => 'structure', 'members' => [ 'ColumnGeographicRole' => [ 'shape' => 'GeoSpatialDataRole', ], 'ColumnDescription' => [ 'shape' => 'ColumnDescription', ], ], ], 'ColumnTagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnTag', ], 'max' => 16, 'min' => 1, ], 'ConcurrentUpdatingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'CopySourceArn' => [ 'type' => 'string', 'pattern' => '^arn:[-a-z0-9]*:quicksight:[-a-z0-9]*:[0-9]{12}:datasource/.+', ], 'CreateAccountCustomizationRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'AccountCustomization', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'querystring', 'locationName' => 'namespace', ], 'AccountCustomization' => [ 'shape' => 'AccountCustomization', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateAccountCustomizationResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'AwsAccountId' => [ 'shape' => 'AwsAccountId', ], 'Namespace' => [ 'shape' => 'Namespace', ], 'AccountCustomization' => [ 'shape' => 'AccountCustomization', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'CreateAnalysisRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'AnalysisId', 'Name', 'SourceEntity', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'AnalysisId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'AnalysisId', ], 'Name' => [ 'shape' => 'AnalysisName', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'Permissions' => [ 'shape' => 'ResourcePermissionList', ], 'SourceEntity' => [ 'shape' => 'AnalysisSourceEntity', ], 'ThemeArn' => [ 'shape' => 'Arn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateAnalysisResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'AnalysisId' => [ 'shape' => 'RestrictiveResourceId', ], 'CreationStatus' => [ 'shape' => 'ResourceStatus', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'CreateColumnsOperation' => [ 'type' => 'structure', 'required' => [ 'Columns', ], 'members' => [ 'Columns' => [ 'shape' => 'CalculatedColumnList', ], ], ], 'CreateDashboardRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'DashboardId', 'Name', 'SourceEntity', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'DashboardId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'DashboardId', ], 'Name' => [ 'shape' => 'DashboardName', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'Permissions' => [ 'shape' => 'ResourcePermissionList', ], 'SourceEntity' => [ 'shape' => 'DashboardSourceEntity', ], 'Tags' => [ 'shape' => 'TagList', ], 'VersionDescription' => [ 'shape' => 'VersionDescription', ], 'DashboardPublishOptions' => [ 'shape' => 'DashboardPublishOptions', ], 'ThemeArn' => [ 'shape' => 'Arn', ], ], ], 'CreateDashboardResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'VersionArn' => [ 'shape' => 'Arn', ], 'DashboardId' => [ 'shape' => 'RestrictiveResourceId', ], 'CreationStatus' => [ 'shape' => 'ResourceStatus', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'CreateDataSetRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'DataSetId', 'Name', 'PhysicalTableMap', 'ImportMode', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'DataSetId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], 'PhysicalTableMap' => [ 'shape' => 'PhysicalTableMap', ], 'LogicalTableMap' => [ 'shape' => 'LogicalTableMap', ], 'ImportMode' => [ 'shape' => 'DataSetImportMode', ], 'ColumnGroups' => [ 'shape' => 'ColumnGroupList', ], 'FieldFolders' => [ 'shape' => 'FieldFolderMap', ], 'Permissions' => [ 'shape' => 'ResourcePermissionList', ], 'RowLevelPermissionDataSet' => [ 'shape' => 'RowLevelPermissionDataSet', ], 'ColumnLevelPermissionRules' => [ 'shape' => 'ColumnLevelPermissionRuleList', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDataSetResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'DataSetId' => [ 'shape' => 'ResourceId', ], 'IngestionArn' => [ 'shape' => 'Arn', ], 'IngestionId' => [ 'shape' => 'ResourceId', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'CreateDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'DataSourceId', 'Name', 'Type', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'DataSourceId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], 'Type' => [ 'shape' => 'DataSourceType', ], 'DataSourceParameters' => [ 'shape' => 'DataSourceParameters', ], 'Credentials' => [ 'shape' => 'DataSourceCredentials', ], 'Permissions' => [ 'shape' => 'ResourcePermissionList', ], 'VpcConnectionProperties' => [ 'shape' => 'VpcConnectionProperties', ], 'SslProperties' => [ 'shape' => 'SslProperties', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDataSourceResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'DataSourceId' => [ 'shape' => 'ResourceId', ], 'CreationStatus' => [ 'shape' => 'ResourceStatus', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'CreateFolderMembershipRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'FolderId', 'MemberId', 'MemberType', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'FolderId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'FolderId', ], 'MemberId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'MemberId', ], 'MemberType' => [ 'shape' => 'MemberType', 'location' => 'uri', 'locationName' => 'MemberType', ], ], ], 'CreateFolderMembershipResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'StatusCode', ], 'FolderMember' => [ 'shape' => 'FolderMember', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'CreateFolderRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'FolderId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'FolderId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'FolderId', ], 'Name' => [ 'shape' => 'FolderName', ], 'FolderType' => [ 'shape' => 'FolderType', ], 'ParentFolderArn' => [ 'shape' => 'Arn', ], 'Permissions' => [ 'shape' => 'ResourcePermissionList', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateFolderResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'Arn' => [ 'shape' => 'Arn', ], 'FolderId' => [ 'shape' => 'RestrictiveResourceId', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'CreateGroupMembershipRequest' => [ 'type' => 'structure', 'required' => [ 'MemberName', 'GroupName', 'AwsAccountId', 'Namespace', ], 'members' => [ 'MemberName' => [ 'shape' => 'GroupMemberName', 'location' => 'uri', 'locationName' => 'MemberName', ], 'GroupName' => [ 'shape' => 'GroupName', 'location' => 'uri', 'locationName' => 'GroupName', ], 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'uri', 'locationName' => 'Namespace', ], ], ], 'CreateGroupMembershipResponse' => [ 'type' => 'structure', 'members' => [ 'GroupMember' => [ 'shape' => 'GroupMember', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'CreateGroupRequest' => [ 'type' => 'structure', 'required' => [ 'GroupName', 'AwsAccountId', 'Namespace', ], 'members' => [ 'GroupName' => [ 'shape' => 'GroupName', ], 'Description' => [ 'shape' => 'GroupDescription', ], 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'uri', 'locationName' => 'Namespace', ], ], ], 'CreateGroupResponse' => [ 'type' => 'structure', 'members' => [ 'Group' => [ 'shape' => 'Group', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'CreateIAMPolicyAssignmentRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'AssignmentName', 'AssignmentStatus', 'Namespace', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'AssignmentName' => [ 'shape' => 'IAMPolicyAssignmentName', ], 'AssignmentStatus' => [ 'shape' => 'AssignmentStatus', ], 'PolicyArn' => [ 'shape' => 'Arn', ], 'Identities' => [ 'shape' => 'IdentityMap', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'uri', 'locationName' => 'Namespace', ], ], ], 'CreateIAMPolicyAssignmentResponse' => [ 'type' => 'structure', 'members' => [ 'AssignmentName' => [ 'shape' => 'IAMPolicyAssignmentName', ], 'AssignmentId' => [ 'shape' => 'String', ], 'AssignmentStatus' => [ 'shape' => 'AssignmentStatus', ], 'PolicyArn' => [ 'shape' => 'Arn', ], 'Identities' => [ 'shape' => 'IdentityMap', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'CreateIngestionRequest' => [ 'type' => 'structure', 'required' => [ 'DataSetId', 'IngestionId', 'AwsAccountId', ], 'members' => [ 'DataSetId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'DataSetId', ], 'IngestionId' => [ 'shape' => 'IngestionId', 'location' => 'uri', 'locationName' => 'IngestionId', ], 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], ], ], 'CreateIngestionResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'IngestionId' => [ 'shape' => 'IngestionId', ], 'IngestionStatus' => [ 'shape' => 'IngestionStatus', ], 'RequestId' => [ 'shape' => 'string', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'CreateNamespaceRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'Namespace', 'IdentityStore', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'Namespace' => [ 'shape' => 'Namespace', ], 'IdentityStore' => [ 'shape' => 'IdentityStore', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateNamespaceResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'Namespace', ], 'CapacityRegion' => [ 'shape' => 'String', ], 'CreationStatus' => [ 'shape' => 'NamespaceStatus', ], 'IdentityStore' => [ 'shape' => 'IdentityStore', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'CreateTemplateAliasRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'TemplateId', 'AliasName', 'TemplateVersionNumber', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'TemplateId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'TemplateId', ], 'AliasName' => [ 'shape' => 'AliasName', 'location' => 'uri', 'locationName' => 'AliasName', ], 'TemplateVersionNumber' => [ 'shape' => 'VersionNumber', ], ], ], 'CreateTemplateAliasResponse' => [ 'type' => 'structure', 'members' => [ 'TemplateAlias' => [ 'shape' => 'TemplateAlias', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'CreateTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'TemplateId', 'SourceEntity', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'TemplateId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'TemplateId', ], 'Name' => [ 'shape' => 'TemplateName', ], 'Permissions' => [ 'shape' => 'ResourcePermissionList', ], 'SourceEntity' => [ 'shape' => 'TemplateSourceEntity', ], 'Tags' => [ 'shape' => 'TagList', ], 'VersionDescription' => [ 'shape' => 'VersionDescription', ], ], ], 'CreateTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'VersionArn' => [ 'shape' => 'Arn', ], 'TemplateId' => [ 'shape' => 'RestrictiveResourceId', ], 'CreationStatus' => [ 'shape' => 'ResourceStatus', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'CreateThemeAliasRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'ThemeId', 'AliasName', 'ThemeVersionNumber', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'ThemeId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'ThemeId', ], 'AliasName' => [ 'shape' => 'AliasName', 'location' => 'uri', 'locationName' => 'AliasName', ], 'ThemeVersionNumber' => [ 'shape' => 'VersionNumber', ], ], ], 'CreateThemeAliasResponse' => [ 'type' => 'structure', 'members' => [ 'ThemeAlias' => [ 'shape' => 'ThemeAlias', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'CreateThemeRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'ThemeId', 'Name', 'BaseThemeId', 'Configuration', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'ThemeId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'ThemeId', ], 'Name' => [ 'shape' => 'ThemeName', ], 'BaseThemeId' => [ 'shape' => 'RestrictiveResourceId', ], 'VersionDescription' => [ 'shape' => 'VersionDescription', ], 'Configuration' => [ 'shape' => 'ThemeConfiguration', ], 'Permissions' => [ 'shape' => 'ResourcePermissionList', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateThemeResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'VersionArn' => [ 'shape' => 'Arn', ], 'ThemeId' => [ 'shape' => 'RestrictiveResourceId', ], 'CreationStatus' => [ 'shape' => 'ResourceStatus', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'CredentialPair' => [ 'type' => 'structure', 'required' => [ 'Username', 'Password', ], 'members' => [ 'Username' => [ 'shape' => 'Username', ], 'Password' => [ 'shape' => 'Password', ], 'AlternateDataSourceParameters' => [ 'shape' => 'DataSourceParametersList', ], ], ], 'CustomSql' => [ 'type' => 'structure', 'required' => [ 'DataSourceArn', 'Name', 'SqlQuery', ], 'members' => [ 'DataSourceArn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'CustomSqlName', ], 'SqlQuery' => [ 'shape' => 'SqlQuery', ], 'Columns' => [ 'shape' => 'InputColumnList', ], ], ], 'CustomSqlName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'Dashboard' => [ 'type' => 'structure', 'members' => [ 'DashboardId' => [ 'shape' => 'RestrictiveResourceId', ], 'Arn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'DashboardName', ], 'Version' => [ 'shape' => 'DashboardVersion', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'LastPublishedTime' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], ], ], 'DashboardBehavior' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'DashboardError' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'DashboardErrorType', ], 'Message' => [ 'shape' => 'NonEmptyString', ], ], ], 'DashboardErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DashboardError', ], 'min' => 1, ], 'DashboardErrorType' => [ 'type' => 'string', 'enum' => [ 'ACCESS_DENIED', 'SOURCE_NOT_FOUND', 'DATA_SET_NOT_FOUND', 'INTERNAL_FAILURE', 'PARAMETER_VALUE_INCOMPATIBLE', 'PARAMETER_TYPE_INVALID', 'PARAMETER_NOT_FOUND', 'COLUMN_TYPE_MISMATCH', 'COLUMN_GEOGRAPHIC_ROLE_MISMATCH', 'COLUMN_REPLACEMENT_MISSING', ], ], 'DashboardFilterAttribute' => [ 'type' => 'string', 'enum' => [ 'QUICKSIGHT_USER', ], ], 'DashboardName' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'DashboardPublishOptions' => [ 'type' => 'structure', 'members' => [ 'AdHocFilteringOption' => [ 'shape' => 'AdHocFilteringOption', ], 'ExportToCSVOption' => [ 'shape' => 'ExportToCSVOption', ], 'SheetControlsOption' => [ 'shape' => 'SheetControlsOption', ], ], ], 'DashboardSearchFilter' => [ 'type' => 'structure', 'required' => [ 'Operator', ], 'members' => [ 'Operator' => [ 'shape' => 'FilterOperator', ], 'Name' => [ 'shape' => 'DashboardFilterAttribute', ], 'Value' => [ 'shape' => 'String', ], ], ], 'DashboardSearchFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DashboardSearchFilter', ], 'max' => 1, 'min' => 1, ], 'DashboardSourceEntity' => [ 'type' => 'structure', 'members' => [ 'SourceTemplate' => [ 'shape' => 'DashboardSourceTemplate', ], ], ], 'DashboardSourceTemplate' => [ 'type' => 'structure', 'required' => [ 'DataSetReferences', 'Arn', ], 'members' => [ 'DataSetReferences' => [ 'shape' => 'DataSetReferenceList', ], 'Arn' => [ 'shape' => 'Arn', ], ], ], 'DashboardSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'DashboardId' => [ 'shape' => 'RestrictiveResourceId', ], 'Name' => [ 'shape' => 'DashboardName', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'PublishedVersionNumber' => [ 'shape' => 'VersionNumber', ], 'LastPublishedTime' => [ 'shape' => 'Timestamp', ], ], ], 'DashboardSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DashboardSummary', ], 'max' => 100, ], 'DashboardUIState' => [ 'type' => 'string', 'enum' => [ 'EXPANDED', 'COLLAPSED', ], ], 'DashboardVersion' => [ 'type' => 'structure', 'members' => [ 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'Errors' => [ 'shape' => 'DashboardErrorList', ], 'VersionNumber' => [ 'shape' => 'VersionNumber', ], 'Status' => [ 'shape' => 'ResourceStatus', ], 'Arn' => [ 'shape' => 'Arn', ], 'SourceEntityArn' => [ 'shape' => 'Arn', ], 'DataSetArns' => [ 'shape' => 'DataSetArnsList', ], 'Description' => [ 'shape' => 'VersionDescription', ], 'ThemeArn' => [ 'shape' => 'Arn', ], 'Sheets' => [ 'shape' => 'SheetList', ], ], ], 'DashboardVersionSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'VersionNumber' => [ 'shape' => 'VersionNumber', ], 'Status' => [ 'shape' => 'ResourceStatus', ], 'SourceEntityArn' => [ 'shape' => 'Arn', ], 'Description' => [ 'shape' => 'VersionDescription', ], ], ], 'DashboardVersionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DashboardVersionSummary', ], 'max' => 100, ], 'DataColorPalette' => [ 'type' => 'structure', 'members' => [ 'Colors' => [ 'shape' => 'ColorList', ], 'MinMaxGradient' => [ 'shape' => 'ColorList', ], 'EmptyFillColor' => [ 'shape' => 'HexColor', ], ], ], 'DataSet' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'DataSetId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'PhysicalTableMap' => [ 'shape' => 'PhysicalTableMap', ], 'LogicalTableMap' => [ 'shape' => 'LogicalTableMap', ], 'OutputColumns' => [ 'shape' => 'OutputColumnList', ], 'ImportMode' => [ 'shape' => 'DataSetImportMode', ], 'ConsumedSpiceCapacityInBytes' => [ 'shape' => 'Long', ], 'ColumnGroups' => [ 'shape' => 'ColumnGroupList', ], 'FieldFolders' => [ 'shape' => 'FieldFolderMap', ], 'RowLevelPermissionDataSet' => [ 'shape' => 'RowLevelPermissionDataSet', ], 'ColumnLevelPermissionRules' => [ 'shape' => 'ColumnLevelPermissionRuleList', ], ], ], 'DataSetArnsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], 'max' => 100, ], 'DataSetConfiguration' => [ 'type' => 'structure', 'members' => [ 'Placeholder' => [ 'shape' => 'String', ], 'DataSetSchema' => [ 'shape' => 'DataSetSchema', ], 'ColumnGroupSchemaList' => [ 'shape' => 'ColumnGroupSchemaList', ], ], ], 'DataSetConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSetConfiguration', ], 'max' => 30, ], 'DataSetImportMode' => [ 'type' => 'string', 'enum' => [ 'SPICE', 'DIRECT_QUERY', ], ], 'DataSetName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'DataSetReference' => [ 'type' => 'structure', 'required' => [ 'DataSetPlaceholder', 'DataSetArn', ], 'members' => [ 'DataSetPlaceholder' => [ 'shape' => 'NonEmptyString', ], 'DataSetArn' => [ 'shape' => 'Arn', ], ], ], 'DataSetReferenceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSetReference', ], 'min' => 1, ], 'DataSetSchema' => [ 'type' => 'structure', 'members' => [ 'ColumnSchemaList' => [ 'shape' => 'ColumnSchemaList', ], ], ], 'DataSetSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'DataSetId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'ImportMode' => [ 'shape' => 'DataSetImportMode', ], 'RowLevelPermissionDataSet' => [ 'shape' => 'RowLevelPermissionDataSet', ], 'ColumnLevelPermissionRulesApplied' => [ 'shape' => 'Boolean', ], ], ], 'DataSetSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSetSummary', ], ], 'DataSource' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'DataSourceId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], 'Type' => [ 'shape' => 'DataSourceType', ], 'Status' => [ 'shape' => 'ResourceStatus', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'DataSourceParameters' => [ 'shape' => 'DataSourceParameters', ], 'AlternateDataSourceParameters' => [ 'shape' => 'DataSourceParametersList', ], 'VpcConnectionProperties' => [ 'shape' => 'VpcConnectionProperties', ], 'SslProperties' => [ 'shape' => 'SslProperties', ], 'ErrorInfo' => [ 'shape' => 'DataSourceErrorInfo', ], ], ], 'DataSourceCredentials' => [ 'type' => 'structure', 'members' => [ 'CredentialPair' => [ 'shape' => 'CredentialPair', ], 'CopySourceArn' => [ 'shape' => 'CopySourceArn', ], ], 'sensitive' => true, ], 'DataSourceErrorInfo' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'DataSourceErrorInfoType', ], 'Message' => [ 'shape' => 'String', ], ], ], 'DataSourceErrorInfoType' => [ 'type' => 'string', 'enum' => [ 'ACCESS_DENIED', 'COPY_SOURCE_NOT_FOUND', 'TIMEOUT', 'ENGINE_VERSION_NOT_SUPPORTED', 'UNKNOWN_HOST', 'GENERIC_SQL_FAILURE', 'CONFLICT', 'UNKNOWN', ], ], 'DataSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSource', ], ], 'DataSourceParameters' => [ 'type' => 'structure', 'members' => [ 'AmazonElasticsearchParameters' => [ 'shape' => 'AmazonElasticsearchParameters', ], 'AthenaParameters' => [ 'shape' => 'AthenaParameters', ], 'AuroraParameters' => [ 'shape' => 'AuroraParameters', ], 'AuroraPostgreSqlParameters' => [ 'shape' => 'AuroraPostgreSqlParameters', ], 'AwsIotAnalyticsParameters' => [ 'shape' => 'AwsIotAnalyticsParameters', ], 'JiraParameters' => [ 'shape' => 'JiraParameters', ], 'MariaDbParameters' => [ 'shape' => 'MariaDbParameters', ], 'MySqlParameters' => [ 'shape' => 'MySqlParameters', ], 'OracleParameters' => [ 'shape' => 'OracleParameters', ], 'PostgreSqlParameters' => [ 'shape' => 'PostgreSqlParameters', ], 'PrestoParameters' => [ 'shape' => 'PrestoParameters', ], 'RdsParameters' => [ 'shape' => 'RdsParameters', ], 'RedshiftParameters' => [ 'shape' => 'RedshiftParameters', ], 'S3Parameters' => [ 'shape' => 'S3Parameters', ], 'ServiceNowParameters' => [ 'shape' => 'ServiceNowParameters', ], 'SnowflakeParameters' => [ 'shape' => 'SnowflakeParameters', ], 'SparkParameters' => [ 'shape' => 'SparkParameters', ], 'SqlServerParameters' => [ 'shape' => 'SqlServerParameters', ], 'TeradataParameters' => [ 'shape' => 'TeradataParameters', ], 'TwitterParameters' => [ 'shape' => 'TwitterParameters', ], ], ], 'DataSourceParametersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSourceParameters', ], 'max' => 50, 'min' => 1, ], 'DataSourceType' => [ 'type' => 'string', 'enum' => [ 'ADOBE_ANALYTICS', 'AMAZON_ELASTICSEARCH', 'ATHENA', 'AURORA', 'AURORA_POSTGRESQL', 'AWS_IOT_ANALYTICS', 'GITHUB', 'JIRA', 'MARIADB', 'MYSQL', 'ORACLE', 'POSTGRESQL', 'PRESTO', 'REDSHIFT', 'S3', 'SALESFORCE', 'SERVICENOW', 'SNOWFLAKE', 'SPARK', 'SQLSERVER', 'TERADATA', 'TWITTER', 'TIMESTREAM', ], ], 'Database' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'DateTimeParameter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Values' => [ 'shape' => 'TimestampList', ], ], ], 'DateTimeParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DateTimeParameter', ], 'max' => 100, ], 'DecimalParameter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Values' => [ 'shape' => 'DoubleList', ], ], ], 'DecimalParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DecimalParameter', ], 'max' => 100, ], 'DeleteAccountCustomizationRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'querystring', 'locationName' => 'namespace', ], ], ], 'DeleteAccountCustomizationResponse' => [ 'type' => 'structure', 'members' => [ 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'DeleteAnalysisRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'AnalysisId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'AnalysisId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'AnalysisId', ], 'RecoveryWindowInDays' => [ 'shape' => 'RecoveryWindowInDays', 'location' => 'querystring', 'locationName' => 'recovery-window-in-days', ], 'ForceDeleteWithoutRecovery' => [ 'shape' => 'boolean', 'location' => 'querystring', 'locationName' => 'force-delete-without-recovery', ], ], ], 'DeleteAnalysisResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'Arn' => [ 'shape' => 'Arn', ], 'AnalysisId' => [ 'shape' => 'RestrictiveResourceId', ], 'DeletionTime' => [ 'shape' => 'Timestamp', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'DeleteDashboardRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'DashboardId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'DashboardId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'DashboardId', ], 'VersionNumber' => [ 'shape' => 'VersionNumber', 'location' => 'querystring', 'locationName' => 'version-number', ], ], ], 'DeleteDashboardResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'Arn' => [ 'shape' => 'Arn', ], 'DashboardId' => [ 'shape' => 'RestrictiveResourceId', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'DeleteDataSetRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'DataSetId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'DataSetId' => [ 'shape' => 'ResourceId', 'location' => 'uri', 'locationName' => 'DataSetId', ], ], ], 'DeleteDataSetResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'DataSetId' => [ 'shape' => 'ResourceId', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'DeleteDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'DataSourceId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'DataSourceId' => [ 'shape' => 'ResourceId', 'location' => 'uri', 'locationName' => 'DataSourceId', ], ], ], 'DeleteDataSourceResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'DataSourceId' => [ 'shape' => 'ResourceId', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'DeleteFolderMembershipRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'FolderId', 'MemberId', 'MemberType', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'FolderId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'FolderId', ], 'MemberId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'MemberId', ], 'MemberType' => [ 'shape' => 'MemberType', 'location' => 'uri', 'locationName' => 'MemberType', ], ], ], 'DeleteFolderMembershipResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'StatusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'DeleteFolderRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'FolderId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'FolderId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'FolderId', ], ], ], 'DeleteFolderResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'Arn' => [ 'shape' => 'Arn', ], 'FolderId' => [ 'shape' => 'RestrictiveResourceId', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'DeleteGroupMembershipRequest' => [ 'type' => 'structure', 'required' => [ 'MemberName', 'GroupName', 'AwsAccountId', 'Namespace', ], 'members' => [ 'MemberName' => [ 'shape' => 'GroupMemberName', 'location' => 'uri', 'locationName' => 'MemberName', ], 'GroupName' => [ 'shape' => 'GroupName', 'location' => 'uri', 'locationName' => 'GroupName', ], 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'uri', 'locationName' => 'Namespace', ], ], ], 'DeleteGroupMembershipResponse' => [ 'type' => 'structure', 'members' => [ 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'DeleteGroupRequest' => [ 'type' => 'structure', 'required' => [ 'GroupName', 'AwsAccountId', 'Namespace', ], 'members' => [ 'GroupName' => [ 'shape' => 'GroupName', 'location' => 'uri', 'locationName' => 'GroupName', ], 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'uri', 'locationName' => 'Namespace', ], ], ], 'DeleteGroupResponse' => [ 'type' => 'structure', 'members' => [ 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'DeleteIAMPolicyAssignmentRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'AssignmentName', 'Namespace', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'AssignmentName' => [ 'shape' => 'IAMPolicyAssignmentName', 'location' => 'uri', 'locationName' => 'AssignmentName', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'uri', 'locationName' => 'Namespace', ], ], ], 'DeleteIAMPolicyAssignmentResponse' => [ 'type' => 'structure', 'members' => [ 'AssignmentName' => [ 'shape' => 'IAMPolicyAssignmentName', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'DeleteNamespaceRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'Namespace', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'uri', 'locationName' => 'Namespace', ], ], ], 'DeleteNamespaceResponse' => [ 'type' => 'structure', 'members' => [ 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'DeleteTemplateAliasRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'TemplateId', 'AliasName', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'TemplateId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'TemplateId', ], 'AliasName' => [ 'shape' => 'AliasName', 'location' => 'uri', 'locationName' => 'AliasName', ], ], ], 'DeleteTemplateAliasResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'TemplateId' => [ 'shape' => 'RestrictiveResourceId', ], 'AliasName' => [ 'shape' => 'AliasName', ], 'Arn' => [ 'shape' => 'Arn', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'DeleteTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'TemplateId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'TemplateId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'TemplateId', ], 'VersionNumber' => [ 'shape' => 'VersionNumber', 'location' => 'querystring', 'locationName' => 'version-number', ], ], ], 'DeleteTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'RequestId' => [ 'shape' => 'String', ], 'Arn' => [ 'shape' => 'Arn', ], 'TemplateId' => [ 'shape' => 'RestrictiveResourceId', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'DeleteThemeAliasRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'ThemeId', 'AliasName', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'ThemeId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'ThemeId', ], 'AliasName' => [ 'shape' => 'AliasName', 'location' => 'uri', 'locationName' => 'AliasName', ], ], ], 'DeleteThemeAliasResponse' => [ 'type' => 'structure', 'members' => [ 'AliasName' => [ 'shape' => 'AliasName', ], 'Arn' => [ 'shape' => 'Arn', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'ThemeId' => [ 'shape' => 'RestrictiveResourceId', ], ], ], 'DeleteThemeRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'ThemeId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'ThemeId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'ThemeId', ], 'VersionNumber' => [ 'shape' => 'VersionNumber', 'location' => 'querystring', 'locationName' => 'version-number', ], ], ], 'DeleteThemeResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'ThemeId' => [ 'shape' => 'RestrictiveResourceId', ], ], ], 'DeleteUserByPrincipalIdRequest' => [ 'type' => 'structure', 'required' => [ 'PrincipalId', 'AwsAccountId', 'Namespace', ], 'members' => [ 'PrincipalId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'PrincipalId', ], 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'uri', 'locationName' => 'Namespace', ], ], ], 'DeleteUserByPrincipalIdResponse' => [ 'type' => 'structure', 'members' => [ 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'DeleteUserRequest' => [ 'type' => 'structure', 'required' => [ 'UserName', 'AwsAccountId', 'Namespace', ], 'members' => [ 'UserName' => [ 'shape' => 'UserName', 'location' => 'uri', 'locationName' => 'UserName', ], 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'uri', 'locationName' => 'Namespace', ], ], ], 'DeleteUserResponse' => [ 'type' => 'structure', 'members' => [ 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'Delimiter' => [ 'type' => 'string', 'max' => 1, 'min' => 1, ], 'DescribeAccountCustomizationRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'querystring', 'locationName' => 'namespace', ], 'Resolved' => [ 'shape' => 'boolean', 'location' => 'querystring', 'locationName' => 'resolved', ], ], ], 'DescribeAccountCustomizationResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'AwsAccountId' => [ 'shape' => 'AwsAccountId', ], 'Namespace' => [ 'shape' => 'Namespace', ], 'AccountCustomization' => [ 'shape' => 'AccountCustomization', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'DescribeAccountSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], ], ], 'DescribeAccountSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'AccountSettings' => [ 'shape' => 'AccountSettings', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'DescribeAnalysisPermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'AnalysisId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'AnalysisId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'AnalysisId', ], ], ], 'DescribeAnalysisPermissionsResponse' => [ 'type' => 'structure', 'members' => [ 'AnalysisId' => [ 'shape' => 'RestrictiveResourceId', ], 'AnalysisArn' => [ 'shape' => 'Arn', ], 'Permissions' => [ 'shape' => 'ResourcePermissionList', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'DescribeAnalysisRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'AnalysisId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'AnalysisId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'AnalysisId', ], ], ], 'DescribeAnalysisResponse' => [ 'type' => 'structure', 'members' => [ 'Analysis' => [ 'shape' => 'Analysis', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'DescribeDashboardPermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'DashboardId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'DashboardId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'DashboardId', ], ], ], 'DescribeDashboardPermissionsResponse' => [ 'type' => 'structure', 'members' => [ 'DashboardId' => [ 'shape' => 'RestrictiveResourceId', ], 'DashboardArn' => [ 'shape' => 'Arn', ], 'Permissions' => [ 'shape' => 'ResourcePermissionList', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'DescribeDashboardRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'DashboardId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'DashboardId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'DashboardId', ], 'VersionNumber' => [ 'shape' => 'VersionNumber', 'location' => 'querystring', 'locationName' => 'version-number', ], 'AliasName' => [ 'shape' => 'AliasName', 'location' => 'querystring', 'locationName' => 'alias-name', ], ], ], 'DescribeDashboardResponse' => [ 'type' => 'structure', 'members' => [ 'Dashboard' => [ 'shape' => 'Dashboard', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'DescribeDataSetPermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'DataSetId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'DataSetId' => [ 'shape' => 'ResourceId', 'location' => 'uri', 'locationName' => 'DataSetId', ], ], ], 'DescribeDataSetPermissionsResponse' => [ 'type' => 'structure', 'members' => [ 'DataSetArn' => [ 'shape' => 'Arn', ], 'DataSetId' => [ 'shape' => 'ResourceId', ], 'Permissions' => [ 'shape' => 'ResourcePermissionList', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'DescribeDataSetRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'DataSetId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'DataSetId' => [ 'shape' => 'ResourceId', 'location' => 'uri', 'locationName' => 'DataSetId', ], ], ], 'DescribeDataSetResponse' => [ 'type' => 'structure', 'members' => [ 'DataSet' => [ 'shape' => 'DataSet', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'DescribeDataSourcePermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'DataSourceId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'DataSourceId' => [ 'shape' => 'ResourceId', 'location' => 'uri', 'locationName' => 'DataSourceId', ], ], ], 'DescribeDataSourcePermissionsResponse' => [ 'type' => 'structure', 'members' => [ 'DataSourceArn' => [ 'shape' => 'Arn', ], 'DataSourceId' => [ 'shape' => 'ResourceId', ], 'Permissions' => [ 'shape' => 'ResourcePermissionList', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'DescribeDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'DataSourceId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'DataSourceId' => [ 'shape' => 'ResourceId', 'location' => 'uri', 'locationName' => 'DataSourceId', ], ], ], 'DescribeDataSourceResponse' => [ 'type' => 'structure', 'members' => [ 'DataSource' => [ 'shape' => 'DataSource', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'DescribeFolderPermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'FolderId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'FolderId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'FolderId', ], ], ], 'DescribeFolderPermissionsResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'FolderId' => [ 'shape' => 'RestrictiveResourceId', ], 'Arn' => [ 'shape' => 'Arn', ], 'Permissions' => [ 'shape' => 'ResourcePermissionList', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'DescribeFolderRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'FolderId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'FolderId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'FolderId', ], ], ], 'DescribeFolderResolvedPermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'FolderId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'FolderId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'FolderId', ], ], ], 'DescribeFolderResolvedPermissionsResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'FolderId' => [ 'shape' => 'RestrictiveResourceId', ], 'Arn' => [ 'shape' => 'Arn', ], 'Permissions' => [ 'shape' => 'ResourcePermissionList', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'DescribeFolderResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'Folder' => [ 'shape' => 'Folder', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'DescribeGroupRequest' => [ 'type' => 'structure', 'required' => [ 'GroupName', 'AwsAccountId', 'Namespace', ], 'members' => [ 'GroupName' => [ 'shape' => 'GroupName', 'location' => 'uri', 'locationName' => 'GroupName', ], 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'uri', 'locationName' => 'Namespace', ], ], ], 'DescribeGroupResponse' => [ 'type' => 'structure', 'members' => [ 'Group' => [ 'shape' => 'Group', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'DescribeIAMPolicyAssignmentRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'AssignmentName', 'Namespace', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'AssignmentName' => [ 'shape' => 'IAMPolicyAssignmentName', 'location' => 'uri', 'locationName' => 'AssignmentName', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'uri', 'locationName' => 'Namespace', ], ], ], 'DescribeIAMPolicyAssignmentResponse' => [ 'type' => 'structure', 'members' => [ 'IAMPolicyAssignment' => [ 'shape' => 'IAMPolicyAssignment', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'DescribeIngestionRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'DataSetId', 'IngestionId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'DataSetId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'DataSetId', ], 'IngestionId' => [ 'shape' => 'IngestionId', 'location' => 'uri', 'locationName' => 'IngestionId', ], ], ], 'DescribeIngestionResponse' => [ 'type' => 'structure', 'members' => [ 'Ingestion' => [ 'shape' => 'Ingestion', ], 'RequestId' => [ 'shape' => 'string', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'DescribeNamespaceRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'Namespace', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'uri', 'locationName' => 'Namespace', ], ], ], 'DescribeNamespaceResponse' => [ 'type' => 'structure', 'members' => [ 'Namespace' => [ 'shape' => 'NamespaceInfoV2', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'DescribeTemplateAliasRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'TemplateId', 'AliasName', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'TemplateId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'TemplateId', ], 'AliasName' => [ 'shape' => 'AliasName', 'location' => 'uri', 'locationName' => 'AliasName', ], ], ], 'DescribeTemplateAliasResponse' => [ 'type' => 'structure', 'members' => [ 'TemplateAlias' => [ 'shape' => 'TemplateAlias', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'DescribeTemplatePermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'TemplateId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'TemplateId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'TemplateId', ], ], ], 'DescribeTemplatePermissionsResponse' => [ 'type' => 'structure', 'members' => [ 'TemplateId' => [ 'shape' => 'RestrictiveResourceId', ], 'TemplateArn' => [ 'shape' => 'Arn', ], 'Permissions' => [ 'shape' => 'ResourcePermissionList', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'DescribeTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'TemplateId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'TemplateId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'TemplateId', ], 'VersionNumber' => [ 'shape' => 'VersionNumber', 'location' => 'querystring', 'locationName' => 'version-number', ], 'AliasName' => [ 'shape' => 'AliasName', 'location' => 'querystring', 'locationName' => 'alias-name', ], ], ], 'DescribeTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'Template' => [ 'shape' => 'Template', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'DescribeThemeAliasRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'ThemeId', 'AliasName', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'ThemeId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'ThemeId', ], 'AliasName' => [ 'shape' => 'AliasName', 'location' => 'uri', 'locationName' => 'AliasName', ], ], ], 'DescribeThemeAliasResponse' => [ 'type' => 'structure', 'members' => [ 'ThemeAlias' => [ 'shape' => 'ThemeAlias', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'DescribeThemePermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'ThemeId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'ThemeId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'ThemeId', ], ], ], 'DescribeThemePermissionsResponse' => [ 'type' => 'structure', 'members' => [ 'ThemeId' => [ 'shape' => 'RestrictiveResourceId', ], 'ThemeArn' => [ 'shape' => 'Arn', ], 'Permissions' => [ 'shape' => 'ResourcePermissionList', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'DescribeThemeRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'ThemeId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAndAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'ThemeId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'ThemeId', ], 'VersionNumber' => [ 'shape' => 'VersionNumber', 'location' => 'querystring', 'locationName' => 'version-number', ], 'AliasName' => [ 'shape' => 'AliasName', 'location' => 'querystring', 'locationName' => 'alias-name', ], ], ], 'DescribeThemeResponse' => [ 'type' => 'structure', 'members' => [ 'Theme' => [ 'shape' => 'Theme', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'DescribeUserRequest' => [ 'type' => 'structure', 'required' => [ 'UserName', 'AwsAccountId', 'Namespace', ], 'members' => [ 'UserName' => [ 'shape' => 'UserName', 'location' => 'uri', 'locationName' => 'UserName', ], 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'uri', 'locationName' => 'Namespace', ], ], ], 'DescribeUserResponse' => [ 'type' => 'structure', 'members' => [ 'User' => [ 'shape' => 'User', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'Domain' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'DomainNotWhitelistedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'Double' => [ 'type' => 'double', ], 'DoubleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Double', ], ], 'Edition' => [ 'type' => 'string', 'enum' => [ 'STANDARD', 'ENTERPRISE', ], ], 'EmbeddingIdentityType' => [ 'type' => 'string', 'enum' => [ 'IAM', 'QUICKSIGHT', 'ANONYMOUS', ], ], 'EmbeddingUrl' => [ 'type' => 'string', 'sensitive' => true, ], 'EntryPoint' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'ErrorInfo' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'IngestionErrorType', ], 'Message' => [ 'shape' => 'string', ], ], ], 'ExceptionResourceType' => [ 'type' => 'string', 'enum' => [ 'USER', 'GROUP', 'NAMESPACE', 'ACCOUNT_SETTINGS', 'IAMPOLICY_ASSIGNMENT', 'DATA_SOURCE', 'DATA_SET', 'VPC_CONNECTION', 'INGESTION', ], ], 'ExportToCSVOption' => [ 'type' => 'structure', 'members' => [ 'AvailabilityStatus' => [ 'shape' => 'DashboardBehavior', ], ], ], 'Expression' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'FieldFolder' => [ 'type' => 'structure', 'members' => [ 'description' => [ 'shape' => 'FieldFolderDescription', ], 'columns' => [ 'shape' => 'FolderColumnList', ], ], ], 'FieldFolderDescription' => [ 'type' => 'string', 'max' => 500, ], 'FieldFolderMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'FieldFolderPath', ], 'value' => [ 'shape' => 'FieldFolder', ], ], 'FieldFolderPath' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'FileFormat' => [ 'type' => 'string', 'enum' => [ 'CSV', 'TSV', 'CLF', 'ELF', 'XLSX', 'JSON', ], ], 'FilterOperation' => [ 'type' => 'structure', 'required' => [ 'ConditionExpression', ], 'members' => [ 'ConditionExpression' => [ 'shape' => 'Expression', ], ], ], 'FilterOperator' => [ 'type' => 'string', 'enum' => [ 'StringEquals', ], ], 'Folder' => [ 'type' => 'structure', 'members' => [ 'FolderId' => [ 'shape' => 'RestrictiveResourceId', ], 'Arn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'FolderName', ], 'FolderType' => [ 'shape' => 'FolderType', ], 'FolderPath' => [ 'shape' => 'Path', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], ], ], 'FolderColumnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 5000, ], 'FolderFilterAttribute' => [ 'type' => 'string', 'enum' => [ 'PARENT_FOLDER_ARN', ], ], 'FolderMember' => [ 'type' => 'structure', 'members' => [ 'MemberId' => [ 'shape' => 'RestrictiveResourceId', ], 'MemberType' => [ 'shape' => 'MemberType', ], ], ], 'FolderMemberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MemberIdArnPair', ], 'max' => 100, ], 'FolderName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'FolderSearchFilter' => [ 'type' => 'structure', 'members' => [ 'Operator' => [ 'shape' => 'FilterOperator', ], 'Name' => [ 'shape' => 'FolderFilterAttribute', ], 'Value' => [ 'shape' => 'String', ], ], ], 'FolderSearchFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FolderSearchFilter', ], 'max' => 100, ], 'FolderSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'FolderId' => [ 'shape' => 'RestrictiveResourceId', ], 'Name' => [ 'shape' => 'FolderName', ], 'FolderType' => [ 'shape' => 'FolderType', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], ], ], 'FolderSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FolderSummary', ], 'max' => 100, ], 'FolderType' => [ 'type' => 'string', 'enum' => [ 'SHARED', ], ], 'GeoSpatialColumnGroup' => [ 'type' => 'structure', 'required' => [ 'Name', 'CountryCode', 'Columns', ], 'members' => [ 'Name' => [ 'shape' => 'ColumnGroupName', ], 'CountryCode' => [ 'shape' => 'GeoSpatialCountryCode', ], 'Columns' => [ 'shape' => 'ColumnList', ], ], ], 'GeoSpatialCountryCode' => [ 'type' => 'string', 'enum' => [ 'US', ], ], 'GeoSpatialDataRole' => [ 'type' => 'string', 'enum' => [ 'COUNTRY', 'STATE', 'COUNTY', 'CITY', 'POSTCODE', 'LONGITUDE', 'LATITUDE', ], ], 'GetDashboardEmbedUrlRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'DashboardId', 'IdentityType', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'DashboardId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'DashboardId', ], 'IdentityType' => [ 'shape' => 'EmbeddingIdentityType', 'location' => 'querystring', 'locationName' => 'creds-type', ], 'SessionLifetimeInMinutes' => [ 'shape' => 'SessionLifetimeInMinutes', 'location' => 'querystring', 'locationName' => 'session-lifetime', ], 'UndoRedoDisabled' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'undo-redo-disabled', ], 'ResetDisabled' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'reset-disabled', ], 'StatePersistenceEnabled' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'state-persistence-enabled', ], 'UserArn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'user-arn', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'querystring', 'locationName' => 'namespace', ], 'AdditionalDashboardIds' => [ 'shape' => 'AdditionalDashboardIdList', 'location' => 'querystring', 'locationName' => 'additional-dashboard-ids', ], ], ], 'GetDashboardEmbedUrlResponse' => [ 'type' => 'structure', 'members' => [ 'EmbedUrl' => [ 'shape' => 'EmbeddingUrl', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'GetSessionEmbedUrlRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'EntryPoint' => [ 'shape' => 'EntryPoint', 'location' => 'querystring', 'locationName' => 'entry-point', ], 'SessionLifetimeInMinutes' => [ 'shape' => 'SessionLifetimeInMinutes', 'location' => 'querystring', 'locationName' => 'session-lifetime', ], 'UserArn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'user-arn', ], ], ], 'GetSessionEmbedUrlResponse' => [ 'type' => 'structure', 'members' => [ 'EmbedUrl' => [ 'shape' => 'EmbeddingUrl', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'Group' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'GroupName' => [ 'shape' => 'GroupName', ], 'Description' => [ 'shape' => 'GroupDescription', ], 'PrincipalId' => [ 'shape' => 'String', ], ], ], 'GroupDescription' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'GroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Group', ], ], 'GroupMember' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'MemberName' => [ 'shape' => 'GroupMemberName', ], ], ], 'GroupMemberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupMember', ], ], 'GroupMemberName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\u0020-\\u00FF]+', ], 'GroupName' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[\\u0020-\\u00FF]+', ], 'GutterStyle' => [ 'type' => 'structure', 'members' => [ 'Show' => [ 'shape' => 'boolean', 'box' => true, ], ], ], 'HexColor' => [ 'type' => 'string', 'pattern' => '^#[A-F0-9]{6}$', ], 'Host' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'IAMPolicyAssignment' => [ 'type' => 'structure', 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', ], 'AssignmentId' => [ 'shape' => 'String', ], 'AssignmentName' => [ 'shape' => 'IAMPolicyAssignmentName', ], 'PolicyArn' => [ 'shape' => 'Arn', ], 'Identities' => [ 'shape' => 'IdentityMap', ], 'AssignmentStatus' => [ 'shape' => 'AssignmentStatus', ], ], ], 'IAMPolicyAssignmentName' => [ 'type' => 'string', 'min' => 1, 'pattern' => '(?=^.{2,256}$)(?!.*\\s)[0-9a-zA-Z-_.:=+@]*$', ], 'IAMPolicyAssignmentSummary' => [ 'type' => 'structure', 'members' => [ 'AssignmentName' => [ 'shape' => 'IAMPolicyAssignmentName', ], 'AssignmentStatus' => [ 'shape' => 'AssignmentStatus', ], ], ], 'IAMPolicyAssignmentSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IAMPolicyAssignmentSummary', ], ], 'IdentityMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'IdentityNameList', ], ], 'IdentityName' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[\\u0020-\\u00FF]+', ], 'IdentityNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdentityName', ], ], 'IdentityStore' => [ 'type' => 'string', 'enum' => [ 'QUICKSIGHT', ], ], 'IdentityType' => [ 'type' => 'string', 'enum' => [ 'IAM', 'QUICKSIGHT', ], ], 'IdentityTypeNotSupportedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'Ingestion' => [ 'type' => 'structure', 'required' => [ 'Arn', 'IngestionStatus', 'CreatedTime', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'IngestionId' => [ 'shape' => 'IngestionId', ], 'IngestionStatus' => [ 'shape' => 'IngestionStatus', ], 'ErrorInfo' => [ 'shape' => 'ErrorInfo', ], 'RowInfo' => [ 'shape' => 'RowInfo', ], 'QueueInfo' => [ 'shape' => 'QueueInfo', ], 'CreatedTime' => [ 'shape' => 'timestamp', ], 'IngestionTimeInSeconds' => [ 'shape' => 'long', 'box' => true, ], 'IngestionSizeInBytes' => [ 'shape' => 'long', 'box' => true, ], 'RequestSource' => [ 'shape' => 'IngestionRequestSource', ], 'RequestType' => [ 'shape' => 'IngestionRequestType', ], ], ], 'IngestionErrorType' => [ 'type' => 'string', 'enum' => [ 'FAILURE_TO_ASSUME_ROLE', 'INGESTION_SUPERSEDED', 'INGESTION_CANCELED', 'DATA_SET_DELETED', 'DATA_SET_NOT_SPICE', 'S3_UPLOADED_FILE_DELETED', 'S3_MANIFEST_ERROR', 'DATA_TOLERANCE_EXCEPTION', 'SPICE_TABLE_NOT_FOUND', 'DATA_SET_SIZE_LIMIT_EXCEEDED', 'ROW_SIZE_LIMIT_EXCEEDED', 'ACCOUNT_CAPACITY_LIMIT_EXCEEDED', 'CUSTOMER_ERROR', 'DATA_SOURCE_NOT_FOUND', 'IAM_ROLE_NOT_AVAILABLE', 'CONNECTION_FAILURE', 'SQL_TABLE_NOT_FOUND', 'PERMISSION_DENIED', 'SSL_CERTIFICATE_VALIDATION_FAILURE', 'OAUTH_TOKEN_FAILURE', 'SOURCE_API_LIMIT_EXCEEDED_FAILURE', 'PASSWORD_AUTHENTICATION_FAILURE', 'SQL_SCHEMA_MISMATCH_ERROR', 'INVALID_DATE_FORMAT', 'INVALID_DATAPREP_SYNTAX', 'SOURCE_RESOURCE_LIMIT_EXCEEDED', 'SQL_INVALID_PARAMETER_VALUE', 'QUERY_TIMEOUT', 'SQL_NUMERIC_OVERFLOW', 'UNRESOLVABLE_HOST', 'UNROUTABLE_HOST', 'SQL_EXCEPTION', 'S3_FILE_INACCESSIBLE', 'IOT_FILE_NOT_FOUND', 'IOT_DATA_SET_FILE_EMPTY', 'INVALID_DATA_SOURCE_CONFIG', 'DATA_SOURCE_AUTH_FAILED', 'DATA_SOURCE_CONNECTION_FAILED', 'FAILURE_TO_PROCESS_JSON_FILE', 'INTERNAL_SERVICE_ERROR', ], ], 'IngestionId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-_]+$', ], 'IngestionMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'IngestionRequestSource' => [ 'type' => 'string', 'enum' => [ 'MANUAL', 'SCHEDULED', ], ], 'IngestionRequestType' => [ 'type' => 'string', 'enum' => [ 'INITIAL_INGESTION', 'EDIT', 'INCREMENTAL_REFRESH', 'FULL_REFRESH', ], ], 'IngestionStatus' => [ 'type' => 'string', 'enum' => [ 'INITIALIZED', 'QUEUED', 'RUNNING', 'FAILED', 'COMPLETED', 'CANCELLED', ], ], 'Ingestions' => [ 'type' => 'list', 'member' => [ 'shape' => 'Ingestion', ], ], 'InputColumn' => [ 'type' => 'structure', 'required' => [ 'Name', 'Type', ], 'members' => [ 'Name' => [ 'shape' => 'ColumnName', ], 'Type' => [ 'shape' => 'InputColumnDataType', ], ], ], 'InputColumnDataType' => [ 'type' => 'string', 'enum' => [ 'STRING', 'INTEGER', 'DECIMAL', 'DATETIME', 'BIT', 'BOOLEAN', 'JSON', ], ], 'InputColumnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputColumn', ], 'max' => 2048, 'min' => 1, ], 'InstanceId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'IntegerParameter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Values' => [ 'shape' => 'LongList', ], ], ], 'IntegerParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IntegerParameter', ], 'max' => 100, ], 'InternalFailureException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InvalidNextTokenException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidParameterValueException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'JiraParameters' => [ 'type' => 'structure', 'required' => [ 'SiteBaseUrl', ], 'members' => [ 'SiteBaseUrl' => [ 'shape' => 'SiteBaseUrl', ], ], ], 'JoinInstruction' => [ 'type' => 'structure', 'required' => [ 'LeftOperand', 'RightOperand', 'Type', 'OnClause', ], 'members' => [ 'LeftOperand' => [ 'shape' => 'LogicalTableId', ], 'RightOperand' => [ 'shape' => 'LogicalTableId', ], 'LeftJoinKeyProperties' => [ 'shape' => 'JoinKeyProperties', ], 'RightJoinKeyProperties' => [ 'shape' => 'JoinKeyProperties', ], 'Type' => [ 'shape' => 'JoinType', ], 'OnClause' => [ 'shape' => 'OnClause', ], ], ], 'JoinKeyProperties' => [ 'type' => 'structure', 'members' => [ 'UniqueKey' => [ 'shape' => 'Boolean', 'box' => true, ], ], ], 'JoinType' => [ 'type' => 'string', 'enum' => [ 'INNER', 'OUTER', 'LEFT', 'RIGHT', ], ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'ExceptionResourceType', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ListAnalysesRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListAnalysesResponse' => [ 'type' => 'structure', 'members' => [ 'AnalysisSummaryList' => [ 'shape' => 'AnalysisSummaryList', ], 'NextToken' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'ListDashboardVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'DashboardId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'DashboardId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'DashboardId', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListDashboardVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'DashboardVersionSummaryList' => [ 'shape' => 'DashboardVersionSummaryList', ], 'NextToken' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'ListDashboardsRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListDashboardsResponse' => [ 'type' => 'structure', 'members' => [ 'DashboardSummaryList' => [ 'shape' => 'DashboardSummaryList', ], 'NextToken' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'ListDataSetsRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListDataSetsResponse' => [ 'type' => 'structure', 'members' => [ 'DataSetSummaries' => [ 'shape' => 'DataSetSummaryList', ], 'NextToken' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'ListDataSourcesRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListDataSourcesResponse' => [ 'type' => 'structure', 'members' => [ 'DataSources' => [ 'shape' => 'DataSourceList', ], 'NextToken' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'ListFolderMembersRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'FolderId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'FolderId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'FolderId', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListFolderMembersResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'FolderMemberList' => [ 'shape' => 'FolderMemberList', ], 'NextToken' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'ListFoldersRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListFoldersResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'FolderSummaryList' => [ 'shape' => 'FolderSummaryList', ], 'NextToken' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'ListGroupMembershipsRequest' => [ 'type' => 'structure', 'required' => [ 'GroupName', 'AwsAccountId', 'Namespace', ], 'members' => [ 'GroupName' => [ 'shape' => 'GroupName', 'location' => 'uri', 'locationName' => 'GroupName', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'max-results', ], 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'uri', 'locationName' => 'Namespace', ], ], ], 'ListGroupMembershipsResponse' => [ 'type' => 'structure', 'members' => [ 'GroupMemberList' => [ 'shape' => 'GroupMemberList', ], 'NextToken' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'ListGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'Namespace', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'max-results', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'uri', 'locationName' => 'Namespace', ], ], ], 'ListGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'GroupList' => [ 'shape' => 'GroupList', ], 'NextToken' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'ListIAMPolicyAssignmentsForUserRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'UserName', 'Namespace', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'UserName' => [ 'shape' => 'UserName', 'location' => 'uri', 'locationName' => 'UserName', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'max-results', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'uri', 'locationName' => 'Namespace', ], ], ], 'ListIAMPolicyAssignmentsForUserResponse' => [ 'type' => 'structure', 'members' => [ 'ActiveAssignments' => [ 'shape' => 'ActiveIAMPolicyAssignmentList', ], 'RequestId' => [ 'shape' => 'String', ], 'NextToken' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'ListIAMPolicyAssignmentsRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'Namespace', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'AssignmentStatus' => [ 'shape' => 'AssignmentStatus', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'uri', 'locationName' => 'Namespace', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListIAMPolicyAssignmentsResponse' => [ 'type' => 'structure', 'members' => [ 'IAMPolicyAssignments' => [ 'shape' => 'IAMPolicyAssignmentSummaryList', ], 'NextToken' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'ListIngestionsRequest' => [ 'type' => 'structure', 'required' => [ 'DataSetId', 'AwsAccountId', ], 'members' => [ 'DataSetId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'DataSetId', ], 'NextToken' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'next-token', ], 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'MaxResults' => [ 'shape' => 'IngestionMaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListIngestionsResponse' => [ 'type' => 'structure', 'members' => [ 'Ingestions' => [ 'shape' => 'Ingestions', ], 'NextToken' => [ 'shape' => 'string', ], 'RequestId' => [ 'shape' => 'string', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'ListNamespacesRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListNamespacesResponse' => [ 'type' => 'structure', 'members' => [ 'Namespaces' => [ 'shape' => 'Namespaces', ], 'NextToken' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'ListTemplateAliasesRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'TemplateId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'TemplateId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'TemplateId', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'max-result', ], ], ], 'ListTemplateAliasesResponse' => [ 'type' => 'structure', 'members' => [ 'TemplateAliasList' => [ 'shape' => 'TemplateAliasList', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListTemplateVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'TemplateId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'TemplateId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'TemplateId', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListTemplateVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'TemplateVersionSummaryList' => [ 'shape' => 'TemplateVersionSummaryList', ], 'NextToken' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'ListTemplatesRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'max-result', ], ], ], 'ListTemplatesResponse' => [ 'type' => 'structure', 'members' => [ 'TemplateSummaryList' => [ 'shape' => 'TemplateSummaryList', ], 'NextToken' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'ListThemeAliasesRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'ThemeId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'ThemeId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'ThemeId', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'max-result', ], ], ], 'ListThemeAliasesResponse' => [ 'type' => 'structure', 'members' => [ 'ThemeAliasList' => [ 'shape' => 'ThemeAliasList', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListThemeVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'ThemeId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'ThemeId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'ThemeId', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListThemeVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'ThemeVersionSummaryList' => [ 'shape' => 'ThemeVersionSummaryList', ], 'NextToken' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'ListThemesRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'max-results', ], 'Type' => [ 'shape' => 'ThemeType', 'location' => 'querystring', 'locationName' => 'type', ], ], ], 'ListThemesResponse' => [ 'type' => 'structure', 'members' => [ 'ThemeSummaryList' => [ 'shape' => 'ThemeSummaryList', ], 'NextToken' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'ListUserGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'UserName', 'AwsAccountId', 'Namespace', ], 'members' => [ 'UserName' => [ 'shape' => 'UserName', 'location' => 'uri', 'locationName' => 'UserName', ], 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'uri', 'locationName' => 'Namespace', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListUserGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'GroupList' => [ 'shape' => 'GroupList', ], 'NextToken' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'ListUsersRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'Namespace', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'max-results', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'uri', 'locationName' => 'Namespace', ], ], ], 'ListUsersResponse' => [ 'type' => 'structure', 'members' => [ 'UserList' => [ 'shape' => 'UserList', ], 'NextToken' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'LogicalTable' => [ 'type' => 'structure', 'required' => [ 'Alias', 'Source', ], 'members' => [ 'Alias' => [ 'shape' => 'LogicalTableAlias', ], 'DataTransforms' => [ 'shape' => 'TransformOperationList', ], 'Source' => [ 'shape' => 'LogicalTableSource', ], ], ], 'LogicalTableAlias' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'LogicalTableId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[0-9a-zA-Z-]*', ], 'LogicalTableMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'LogicalTableId', ], 'value' => [ 'shape' => 'LogicalTable', ], 'max' => 64, 'min' => 1, ], 'LogicalTableSource' => [ 'type' => 'structure', 'members' => [ 'JoinInstruction' => [ 'shape' => 'JoinInstruction', ], 'PhysicalTableId' => [ 'shape' => 'PhysicalTableId', ], ], ], 'Long' => [ 'type' => 'long', ], 'LongList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Long', ], ], 'ManifestFileLocation' => [ 'type' => 'structure', 'required' => [ 'Bucket', 'Key', ], 'members' => [ 'Bucket' => [ 'shape' => 'S3Bucket', ], 'Key' => [ 'shape' => 'S3Key', ], ], ], 'MarginStyle' => [ 'type' => 'structure', 'members' => [ 'Show' => [ 'shape' => 'boolean', 'box' => true, ], ], ], 'MariaDbParameters' => [ 'type' => 'structure', 'required' => [ 'Host', 'Port', 'Database', ], 'members' => [ 'Host' => [ 'shape' => 'Host', ], 'Port' => [ 'shape' => 'Port', ], 'Database' => [ 'shape' => 'Database', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MemberIdArnPair' => [ 'type' => 'structure', 'members' => [ 'MemberId' => [ 'shape' => 'RestrictiveResourceId', ], 'MemberArn' => [ 'shape' => 'Arn', ], ], ], 'MemberType' => [ 'type' => 'string', 'enum' => [ 'DASHBOARD', 'ANALYSIS', 'DATASET', ], ], 'MySqlParameters' => [ 'type' => 'structure', 'required' => [ 'Host', 'Port', 'Database', ], 'members' => [ 'Host' => [ 'shape' => 'Host', ], 'Port' => [ 'shape' => 'Port', ], 'Database' => [ 'shape' => 'Database', ], ], ], 'Namespace' => [ 'type' => 'string', 'max' => 64, 'pattern' => '^[a-zA-Z0-9._-]*$', ], 'NamespaceError' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NamespaceErrorType', ], 'Message' => [ 'shape' => 'String', ], ], ], 'NamespaceErrorType' => [ 'type' => 'string', 'enum' => [ 'PERMISSION_DENIED', 'INTERNAL_SERVICE_ERROR', ], ], 'NamespaceInfoV2' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'Namespace', ], 'Arn' => [ 'shape' => 'Arn', ], 'CapacityRegion' => [ 'shape' => 'String', ], 'CreationStatus' => [ 'shape' => 'NamespaceStatus', ], 'IdentityStore' => [ 'shape' => 'IdentityStore', ], 'NamespaceError' => [ 'shape' => 'NamespaceError', ], ], ], 'NamespaceStatus' => [ 'type' => 'string', 'enum' => [ 'CREATED', 'CREATING', 'DELETING', 'RETRYABLE_FAILURE', 'NON_RETRYABLE_FAILURE', ], ], 'Namespaces' => [ 'type' => 'list', 'member' => [ 'shape' => 'NamespaceInfoV2', ], ], 'NonEmptyString' => [ 'type' => 'string', 'pattern' => '.*\\S.*', ], 'OnClause' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'OptionalPort' => [ 'type' => 'integer', 'max' => 65535, 'min' => 0, ], 'OracleParameters' => [ 'type' => 'structure', 'required' => [ 'Host', 'Port', 'Database', ], 'members' => [ 'Host' => [ 'shape' => 'Host', ], 'Port' => [ 'shape' => 'Port', ], 'Database' => [ 'shape' => 'Database', ], ], ], 'OutputColumn' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ColumnName', ], 'Description' => [ 'shape' => 'ColumnDescriptiveText', ], 'Type' => [ 'shape' => 'ColumnDataType', ], ], ], 'OutputColumnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutputColumn', ], ], 'Parameters' => [ 'type' => 'structure', 'members' => [ 'StringParameters' => [ 'shape' => 'StringParameterList', ], 'IntegerParameters' => [ 'shape' => 'IntegerParameterList', ], 'DecimalParameters' => [ 'shape' => 'DecimalParameterList', ], 'DateTimeParameters' => [ 'shape' => 'DateTimeParameterList', ], ], ], 'Password' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'Path' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], 'max' => 10, 'min' => 1, ], 'PhysicalTable' => [ 'type' => 'structure', 'members' => [ 'RelationalTable' => [ 'shape' => 'RelationalTable', ], 'CustomSql' => [ 'shape' => 'CustomSql', ], 'S3Source' => [ 'shape' => 'S3Source', ], ], ], 'PhysicalTableId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[0-9a-zA-Z-]*', ], 'PhysicalTableMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'PhysicalTableId', ], 'value' => [ 'shape' => 'PhysicalTable', ], 'max' => 32, 'min' => 1, ], 'Port' => [ 'type' => 'integer', 'max' => 65535, 'min' => 1, ], 'PositiveInteger' => [ 'type' => 'integer', 'min' => 1, ], 'PostgreSqlParameters' => [ 'type' => 'structure', 'required' => [ 'Host', 'Port', 'Database', ], 'members' => [ 'Host' => [ 'shape' => 'Host', ], 'Port' => [ 'shape' => 'Port', ], 'Database' => [ 'shape' => 'Database', ], ], ], 'PreconditionNotMetException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'PrestoParameters' => [ 'type' => 'structure', 'required' => [ 'Host', 'Port', 'Catalog', ], 'members' => [ 'Host' => [ 'shape' => 'Host', ], 'Port' => [ 'shape' => 'Port', ], 'Catalog' => [ 'shape' => 'Catalog', ], ], ], 'Principal' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'PrincipalList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 100, 'min' => 1, ], 'ProjectOperation' => [ 'type' => 'structure', 'required' => [ 'ProjectedColumns', ], 'members' => [ 'ProjectedColumns' => [ 'shape' => 'ProjectedColumnList', ], ], ], 'ProjectedColumnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 2000, 'min' => 1, ], 'Query' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'QueueInfo' => [ 'type' => 'structure', 'required' => [ 'WaitingOnIngestion', 'QueuedIngestion', ], 'members' => [ 'WaitingOnIngestion' => [ 'shape' => 'string', ], 'QueuedIngestion' => [ 'shape' => 'string', ], ], ], 'QuickSightUserNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'RdsParameters' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Database', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'Database' => [ 'shape' => 'Database', ], ], ], 'RecoveryWindowInDays' => [ 'type' => 'long', 'max' => 30, 'min' => 7, ], 'RedshiftParameters' => [ 'type' => 'structure', 'required' => [ 'Database', ], 'members' => [ 'Host' => [ 'shape' => 'Host', ], 'Port' => [ 'shape' => 'OptionalPort', ], 'Database' => [ 'shape' => 'Database', ], 'ClusterId' => [ 'shape' => 'ClusterId', ], ], ], 'RegisterUserRequest' => [ 'type' => 'structure', 'required' => [ 'IdentityType', 'Email', 'UserRole', 'AwsAccountId', 'Namespace', ], 'members' => [ 'IdentityType' => [ 'shape' => 'IdentityType', ], 'Email' => [ 'shape' => 'String', ], 'UserRole' => [ 'shape' => 'UserRole', ], 'IamArn' => [ 'shape' => 'String', ], 'SessionName' => [ 'shape' => 'RoleSessionName', ], 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'uri', 'locationName' => 'Namespace', ], 'UserName' => [ 'shape' => 'UserName', ], 'CustomPermissionsName' => [ 'shape' => 'RoleName', ], 'ExternalLoginFederationProviderType' => [ 'shape' => 'String', ], 'CustomFederationProviderUrl' => [ 'shape' => 'String', ], 'ExternalLoginId' => [ 'shape' => 'String', ], ], ], 'RegisterUserResponse' => [ 'type' => 'structure', 'members' => [ 'User' => [ 'shape' => 'User', ], 'UserInvitationUrl' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'RelationalTable' => [ 'type' => 'structure', 'required' => [ 'DataSourceArn', 'Name', 'InputColumns', ], 'members' => [ 'DataSourceArn' => [ 'shape' => 'Arn', ], 'Catalog' => [ 'shape' => 'RelationalTableCatalog', ], 'Schema' => [ 'shape' => 'RelationalTableSchema', ], 'Name' => [ 'shape' => 'RelationalTableName', ], 'InputColumns' => [ 'shape' => 'InputColumnList', ], ], ], 'RelationalTableCatalog' => [ 'type' => 'string', 'max' => 256, ], 'RelationalTableName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'RelationalTableSchema' => [ 'type' => 'string', 'max' => 64, ], 'RenameColumnOperation' => [ 'type' => 'structure', 'required' => [ 'ColumnName', 'NewColumnName', ], 'members' => [ 'ColumnName' => [ 'shape' => 'ColumnName', ], 'NewColumnName' => [ 'shape' => 'ColumnName', ], ], ], 'ResourceExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'ExceptionResourceType', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ResourceId' => [ 'type' => 'string', ], 'ResourceName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'ExceptionResourceType', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ResourcePermission' => [ 'type' => 'structure', 'required' => [ 'Principal', 'Actions', ], 'members' => [ 'Principal' => [ 'shape' => 'Principal', ], 'Actions' => [ 'shape' => 'ActionList', ], ], ], 'ResourcePermissionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourcePermission', ], 'max' => 64, 'min' => 1, ], 'ResourceStatus' => [ 'type' => 'string', 'enum' => [ 'CREATION_IN_PROGRESS', 'CREATION_SUCCESSFUL', 'CREATION_FAILED', 'UPDATE_IN_PROGRESS', 'UPDATE_SUCCESSFUL', 'UPDATE_FAILED', 'DELETED', ], ], 'ResourceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'ExceptionResourceType', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, ], 'RestoreAnalysisRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'AnalysisId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'AnalysisId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'AnalysisId', ], ], ], 'RestoreAnalysisResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'Arn' => [ 'shape' => 'Arn', ], 'AnalysisId' => [ 'shape' => 'RestrictiveResourceId', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'RestrictiveResourceId' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[\\w\\-]+', ], 'RoleName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9+=,.@_-]+$', ], 'RoleSessionName' => [ 'type' => 'string', 'max' => 64, 'min' => 2, 'pattern' => '[\\w+=.@-]*', ], 'RowInfo' => [ 'type' => 'structure', 'members' => [ 'RowsIngested' => [ 'shape' => 'long', 'box' => true, ], 'RowsDropped' => [ 'shape' => 'long', 'box' => true, ], ], ], 'RowLevelPermissionDataSet' => [ 'type' => 'structure', 'required' => [ 'Arn', 'PermissionPolicy', ], 'members' => [ 'Namespace' => [ 'shape' => 'Namespace', ], 'Arn' => [ 'shape' => 'Arn', ], 'PermissionPolicy' => [ 'shape' => 'RowLevelPermissionPolicy', ], 'FormatVersion' => [ 'shape' => 'RowLevelPermissionFormatVersion', ], ], ], 'RowLevelPermissionFormatVersion' => [ 'type' => 'string', 'enum' => [ 'VERSION_1', 'VERSION_2', ], ], 'RowLevelPermissionPolicy' => [ 'type' => 'string', 'enum' => [ 'GRANT_ACCESS', 'DENY_ACCESS', ], ], 'S3Bucket' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'S3Key' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'S3Parameters' => [ 'type' => 'structure', 'required' => [ 'ManifestFileLocation', ], 'members' => [ 'ManifestFileLocation' => [ 'shape' => 'ManifestFileLocation', ], ], ], 'S3Source' => [ 'type' => 'structure', 'required' => [ 'DataSourceArn', 'InputColumns', ], 'members' => [ 'DataSourceArn' => [ 'shape' => 'Arn', ], 'UploadSettings' => [ 'shape' => 'UploadSettings', ], 'InputColumns' => [ 'shape' => 'InputColumnList', ], ], ], 'SearchAnalysesRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'Filters', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'Filters' => [ 'shape' => 'AnalysisSearchFilterList', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], ], ], 'SearchAnalysesResponse' => [ 'type' => 'structure', 'members' => [ 'AnalysisSummaryList' => [ 'shape' => 'AnalysisSummaryList', ], 'NextToken' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'SearchDashboardsRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'Filters', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'Filters' => [ 'shape' => 'DashboardSearchFilterList', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], ], ], 'SearchDashboardsResponse' => [ 'type' => 'structure', 'members' => [ 'DashboardSummaryList' => [ 'shape' => 'DashboardSummaryList', ], 'NextToken' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'SearchFoldersRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'Filters', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'Filters' => [ 'shape' => 'FolderSearchFilterList', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], ], ], 'SearchFoldersResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'FolderSummaryList' => [ 'shape' => 'FolderSummaryList', ], 'NextToken' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'ServiceNowParameters' => [ 'type' => 'structure', 'required' => [ 'SiteBaseUrl', ], 'members' => [ 'SiteBaseUrl' => [ 'shape' => 'SiteBaseUrl', ], ], ], 'SessionLifetimeInMinutes' => [ 'type' => 'long', 'max' => 600, 'min' => 15, ], 'SessionLifetimeInMinutesInvalidException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Sheet' => [ 'type' => 'structure', 'members' => [ 'SheetId' => [ 'shape' => 'RestrictiveResourceId', ], 'Name' => [ 'shape' => 'NonEmptyString', ], ], ], 'SheetControlsOption' => [ 'type' => 'structure', 'members' => [ 'VisibilityState' => [ 'shape' => 'DashboardUIState', ], ], ], 'SheetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Sheet', ], 'max' => 20, ], 'SheetStyle' => [ 'type' => 'structure', 'members' => [ 'Tile' => [ 'shape' => 'TileStyle', ], 'TileLayout' => [ 'shape' => 'TileLayoutStyle', ], ], ], 'SiteBaseUrl' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'SnowflakeParameters' => [ 'type' => 'structure', 'required' => [ 'Host', 'Database', 'Warehouse', ], 'members' => [ 'Host' => [ 'shape' => 'Host', ], 'Database' => [ 'shape' => 'Database', ], 'Warehouse' => [ 'shape' => 'Warehouse', ], ], ], 'SparkParameters' => [ 'type' => 'structure', 'required' => [ 'Host', 'Port', ], 'members' => [ 'Host' => [ 'shape' => 'Host', ], 'Port' => [ 'shape' => 'Port', ], ], ], 'SqlQuery' => [ 'type' => 'string', 'max' => 65536, 'min' => 1, ], 'SqlServerParameters' => [ 'type' => 'structure', 'required' => [ 'Host', 'Port', 'Database', ], 'members' => [ 'Host' => [ 'shape' => 'Host', ], 'Port' => [ 'shape' => 'Port', ], 'Database' => [ 'shape' => 'Database', ], ], ], 'SslProperties' => [ 'type' => 'structure', 'members' => [ 'DisableSsl' => [ 'shape' => 'Boolean', ], ], ], 'StatusCode' => [ 'type' => 'integer', ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'StringParameter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Values' => [ 'shape' => 'StringList', ], ], ], 'StringParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringParameter', ], 'max' => 100, ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagColumnOperation' => [ 'type' => 'structure', 'required' => [ 'ColumnName', 'Tags', ], 'members' => [ 'ColumnName' => [ 'shape' => 'ColumnName', ], 'Tags' => [ 'shape' => 'ColumnTagList', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 1, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [ 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'Template' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'TemplateName', ], 'Version' => [ 'shape' => 'TemplateVersion', ], 'TemplateId' => [ 'shape' => 'RestrictiveResourceId', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], ], ], 'TemplateAlias' => [ 'type' => 'structure', 'members' => [ 'AliasName' => [ 'shape' => 'AliasName', ], 'Arn' => [ 'shape' => 'Arn', ], 'TemplateVersionNumber' => [ 'shape' => 'VersionNumber', ], ], ], 'TemplateAliasList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TemplateAlias', ], 'max' => 100, ], 'TemplateError' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'TemplateErrorType', ], 'Message' => [ 'shape' => 'NonEmptyString', ], ], ], 'TemplateErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TemplateError', ], 'min' => 1, ], 'TemplateErrorType' => [ 'type' => 'string', 'enum' => [ 'SOURCE_NOT_FOUND', 'DATA_SET_NOT_FOUND', 'INTERNAL_FAILURE', 'ACCESS_DENIED', ], ], 'TemplateName' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'TemplateSourceAnalysis' => [ 'type' => 'structure', 'required' => [ 'Arn', 'DataSetReferences', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'DataSetReferences' => [ 'shape' => 'DataSetReferenceList', ], ], ], 'TemplateSourceEntity' => [ 'type' => 'structure', 'members' => [ 'SourceAnalysis' => [ 'shape' => 'TemplateSourceAnalysis', ], 'SourceTemplate' => [ 'shape' => 'TemplateSourceTemplate', ], ], ], 'TemplateSourceTemplate' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], ], ], 'TemplateSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'TemplateId' => [ 'shape' => 'RestrictiveResourceId', ], 'Name' => [ 'shape' => 'TemplateName', ], 'LatestVersionNumber' => [ 'shape' => 'VersionNumber', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], ], ], 'TemplateSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TemplateSummary', ], 'max' => 100, ], 'TemplateVersion' => [ 'type' => 'structure', 'members' => [ 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'Errors' => [ 'shape' => 'TemplateErrorList', ], 'VersionNumber' => [ 'shape' => 'VersionNumber', ], 'Status' => [ 'shape' => 'ResourceStatus', ], 'DataSetConfigurations' => [ 'shape' => 'DataSetConfigurationList', ], 'Description' => [ 'shape' => 'VersionDescription', ], 'SourceEntityArn' => [ 'shape' => 'Arn', ], 'ThemeArn' => [ 'shape' => 'Arn', ], 'Sheets' => [ 'shape' => 'SheetList', ], ], ], 'TemplateVersionSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'VersionNumber' => [ 'shape' => 'VersionNumber', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'Status' => [ 'shape' => 'ResourceStatus', ], 'Description' => [ 'shape' => 'VersionDescription', ], ], ], 'TemplateVersionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TemplateVersionSummary', ], 'max' => 100, ], 'TeradataParameters' => [ 'type' => 'structure', 'required' => [ 'Host', 'Port', 'Database', ], 'members' => [ 'Host' => [ 'shape' => 'Host', ], 'Port' => [ 'shape' => 'Port', ], 'Database' => [ 'shape' => 'Database', ], ], ], 'TextQualifier' => [ 'type' => 'string', 'enum' => [ 'DOUBLE_QUOTE', 'SINGLE_QUOTE', ], ], 'Theme' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'ThemeName', ], 'ThemeId' => [ 'shape' => 'RestrictiveResourceId', ], 'Version' => [ 'shape' => 'ThemeVersion', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'Type' => [ 'shape' => 'ThemeType', ], ], ], 'ThemeAlias' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'AliasName' => [ 'shape' => 'AliasName', ], 'ThemeVersionNumber' => [ 'shape' => 'VersionNumber', ], ], ], 'ThemeAliasList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThemeAlias', ], 'max' => 100, ], 'ThemeConfiguration' => [ 'type' => 'structure', 'members' => [ 'DataColorPalette' => [ 'shape' => 'DataColorPalette', ], 'UIColorPalette' => [ 'shape' => 'UIColorPalette', ], 'Sheet' => [ 'shape' => 'SheetStyle', ], ], ], 'ThemeError' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'ThemeErrorType', ], 'Message' => [ 'shape' => 'NonEmptyString', ], ], ], 'ThemeErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThemeError', ], 'min' => 1, ], 'ThemeErrorType' => [ 'type' => 'string', 'enum' => [ 'INTERNAL_FAILURE', ], ], 'ThemeName' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'ThemeSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'ThemeName', ], 'ThemeId' => [ 'shape' => 'RestrictiveResourceId', ], 'LatestVersionNumber' => [ 'shape' => 'VersionNumber', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], ], ], 'ThemeSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThemeSummary', ], 'max' => 100, ], 'ThemeType' => [ 'type' => 'string', 'enum' => [ 'QUICKSIGHT', 'CUSTOM', 'ALL', ], ], 'ThemeVersion' => [ 'type' => 'structure', 'members' => [ 'VersionNumber' => [ 'shape' => 'VersionNumber', ], 'Arn' => [ 'shape' => 'Arn', ], 'Description' => [ 'shape' => 'VersionDescription', ], 'BaseThemeId' => [ 'shape' => 'RestrictiveResourceId', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'Configuration' => [ 'shape' => 'ThemeConfiguration', ], 'Errors' => [ 'shape' => 'ThemeErrorList', ], 'Status' => [ 'shape' => 'ResourceStatus', ], ], ], 'ThemeVersionSummary' => [ 'type' => 'structure', 'members' => [ 'VersionNumber' => [ 'shape' => 'VersionNumber', ], 'Arn' => [ 'shape' => 'Arn', ], 'Description' => [ 'shape' => 'VersionDescription', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'Status' => [ 'shape' => 'ResourceStatus', ], ], ], 'ThemeVersionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThemeVersionSummary', ], 'max' => 100, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'TileLayoutStyle' => [ 'type' => 'structure', 'members' => [ 'Gutter' => [ 'shape' => 'GutterStyle', ], 'Margin' => [ 'shape' => 'MarginStyle', ], ], ], 'TileStyle' => [ 'type' => 'structure', 'members' => [ 'Border' => [ 'shape' => 'BorderStyle', ], ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TimestampList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Timestamp', ], ], 'TransformOperation' => [ 'type' => 'structure', 'members' => [ 'ProjectOperation' => [ 'shape' => 'ProjectOperation', ], 'FilterOperation' => [ 'shape' => 'FilterOperation', ], 'CreateColumnsOperation' => [ 'shape' => 'CreateColumnsOperation', ], 'RenameColumnOperation' => [ 'shape' => 'RenameColumnOperation', ], 'CastColumnTypeOperation' => [ 'shape' => 'CastColumnTypeOperation', ], 'TagColumnOperation' => [ 'shape' => 'TagColumnOperation', ], ], ], 'TransformOperationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TransformOperation', ], 'max' => 2048, 'min' => 1, ], 'TwitterParameters' => [ 'type' => 'structure', 'required' => [ 'Query', 'MaxRows', ], 'members' => [ 'Query' => [ 'shape' => 'Query', ], 'MaxRows' => [ 'shape' => 'PositiveInteger', ], ], ], 'TypeCastFormat' => [ 'type' => 'string', 'max' => 32, ], 'UIColorPalette' => [ 'type' => 'structure', 'members' => [ 'PrimaryForeground' => [ 'shape' => 'HexColor', ], 'PrimaryBackground' => [ 'shape' => 'HexColor', ], 'SecondaryForeground' => [ 'shape' => 'HexColor', ], 'SecondaryBackground' => [ 'shape' => 'HexColor', ], 'Accent' => [ 'shape' => 'HexColor', ], 'AccentForeground' => [ 'shape' => 'HexColor', ], 'Danger' => [ 'shape' => 'HexColor', ], 'DangerForeground' => [ 'shape' => 'HexColor', ], 'Warning' => [ 'shape' => 'HexColor', ], 'WarningForeground' => [ 'shape' => 'HexColor', ], 'Success' => [ 'shape' => 'HexColor', ], 'SuccessForeground' => [ 'shape' => 'HexColor', ], 'Dimension' => [ 'shape' => 'HexColor', ], 'DimensionForeground' => [ 'shape' => 'HexColor', ], 'Measure' => [ 'shape' => 'HexColor', ], 'MeasureForeground' => [ 'shape' => 'HexColor', ], ], ], 'UnsupportedPricingPlanException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'UnsupportedUserEditionException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'keys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [ 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'UpdateAccountCustomizationRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'AccountCustomization', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'querystring', 'locationName' => 'namespace', ], 'AccountCustomization' => [ 'shape' => 'AccountCustomization', ], ], ], 'UpdateAccountCustomizationResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'AwsAccountId' => [ 'shape' => 'AwsAccountId', ], 'Namespace' => [ 'shape' => 'Namespace', ], 'AccountCustomization' => [ 'shape' => 'AccountCustomization', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'UpdateAccountSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'DefaultNamespace', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'DefaultNamespace' => [ 'shape' => 'Namespace', ], 'NotificationEmail' => [ 'shape' => 'String', ], ], ], 'UpdateAccountSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'UpdateAnalysisPermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'AnalysisId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'AnalysisId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'AnalysisId', ], 'GrantPermissions' => [ 'shape' => 'UpdateResourcePermissionList', ], 'RevokePermissions' => [ 'shape' => 'UpdateResourcePermissionList', ], ], ], 'UpdateAnalysisPermissionsResponse' => [ 'type' => 'structure', 'members' => [ 'AnalysisArn' => [ 'shape' => 'Arn', ], 'AnalysisId' => [ 'shape' => 'RestrictiveResourceId', ], 'Permissions' => [ 'shape' => 'ResourcePermissionList', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'UpdateAnalysisRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'AnalysisId', 'Name', 'SourceEntity', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'AnalysisId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'AnalysisId', ], 'Name' => [ 'shape' => 'AnalysisName', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'SourceEntity' => [ 'shape' => 'AnalysisSourceEntity', ], 'ThemeArn' => [ 'shape' => 'Arn', ], ], ], 'UpdateAnalysisResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'AnalysisId' => [ 'shape' => 'RestrictiveResourceId', ], 'UpdateStatus' => [ 'shape' => 'ResourceStatus', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'UpdateDashboardPermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'DashboardId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'DashboardId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'DashboardId', ], 'GrantPermissions' => [ 'shape' => 'UpdateResourcePermissionList', ], 'RevokePermissions' => [ 'shape' => 'UpdateResourcePermissionList', ], ], ], 'UpdateDashboardPermissionsResponse' => [ 'type' => 'structure', 'members' => [ 'DashboardArn' => [ 'shape' => 'Arn', ], 'DashboardId' => [ 'shape' => 'RestrictiveResourceId', ], 'Permissions' => [ 'shape' => 'ResourcePermissionList', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'UpdateDashboardPublishedVersionRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'DashboardId', 'VersionNumber', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'DashboardId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'DashboardId', ], 'VersionNumber' => [ 'shape' => 'VersionNumber', 'location' => 'uri', 'locationName' => 'VersionNumber', ], ], ], 'UpdateDashboardPublishedVersionResponse' => [ 'type' => 'structure', 'members' => [ 'DashboardId' => [ 'shape' => 'RestrictiveResourceId', ], 'DashboardArn' => [ 'shape' => 'Arn', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'UpdateDashboardRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'DashboardId', 'Name', 'SourceEntity', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'DashboardId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'DashboardId', ], 'Name' => [ 'shape' => 'DashboardName', ], 'SourceEntity' => [ 'shape' => 'DashboardSourceEntity', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'VersionDescription' => [ 'shape' => 'VersionDescription', ], 'DashboardPublishOptions' => [ 'shape' => 'DashboardPublishOptions', ], 'ThemeArn' => [ 'shape' => 'Arn', ], ], ], 'UpdateDashboardResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'VersionArn' => [ 'shape' => 'Arn', ], 'DashboardId' => [ 'shape' => 'RestrictiveResourceId', ], 'CreationStatus' => [ 'shape' => 'ResourceStatus', ], 'Status' => [ 'shape' => 'StatusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'UpdateDataSetPermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'DataSetId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'DataSetId' => [ 'shape' => 'ResourceId', 'location' => 'uri', 'locationName' => 'DataSetId', ], 'GrantPermissions' => [ 'shape' => 'ResourcePermissionList', ], 'RevokePermissions' => [ 'shape' => 'ResourcePermissionList', ], ], ], 'UpdateDataSetPermissionsResponse' => [ 'type' => 'structure', 'members' => [ 'DataSetArn' => [ 'shape' => 'Arn', ], 'DataSetId' => [ 'shape' => 'ResourceId', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'UpdateDataSetRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'DataSetId', 'Name', 'PhysicalTableMap', 'ImportMode', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'DataSetId' => [ 'shape' => 'ResourceId', 'location' => 'uri', 'locationName' => 'DataSetId', ], 'Name' => [ 'shape' => 'ResourceName', ], 'PhysicalTableMap' => [ 'shape' => 'PhysicalTableMap', ], 'LogicalTableMap' => [ 'shape' => 'LogicalTableMap', ], 'ImportMode' => [ 'shape' => 'DataSetImportMode', ], 'ColumnGroups' => [ 'shape' => 'ColumnGroupList', ], 'FieldFolders' => [ 'shape' => 'FieldFolderMap', ], 'RowLevelPermissionDataSet' => [ 'shape' => 'RowLevelPermissionDataSet', ], 'ColumnLevelPermissionRules' => [ 'shape' => 'ColumnLevelPermissionRuleList', ], ], ], 'UpdateDataSetResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'DataSetId' => [ 'shape' => 'ResourceId', ], 'IngestionArn' => [ 'shape' => 'Arn', ], 'IngestionId' => [ 'shape' => 'ResourceId', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'UpdateDataSourcePermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'DataSourceId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'DataSourceId' => [ 'shape' => 'ResourceId', 'location' => 'uri', 'locationName' => 'DataSourceId', ], 'GrantPermissions' => [ 'shape' => 'ResourcePermissionList', ], 'RevokePermissions' => [ 'shape' => 'ResourcePermissionList', ], ], ], 'UpdateDataSourcePermissionsResponse' => [ 'type' => 'structure', 'members' => [ 'DataSourceArn' => [ 'shape' => 'Arn', ], 'DataSourceId' => [ 'shape' => 'ResourceId', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'UpdateDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'DataSourceId', 'Name', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'DataSourceId' => [ 'shape' => 'ResourceId', 'location' => 'uri', 'locationName' => 'DataSourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], 'DataSourceParameters' => [ 'shape' => 'DataSourceParameters', ], 'Credentials' => [ 'shape' => 'DataSourceCredentials', ], 'VpcConnectionProperties' => [ 'shape' => 'VpcConnectionProperties', ], 'SslProperties' => [ 'shape' => 'SslProperties', ], ], ], 'UpdateDataSourceResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'DataSourceId' => [ 'shape' => 'ResourceId', ], 'UpdateStatus' => [ 'shape' => 'ResourceStatus', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'UpdateFolderPermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'FolderId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'FolderId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'FolderId', ], 'GrantPermissions' => [ 'shape' => 'ResourcePermissionList', ], 'RevokePermissions' => [ 'shape' => 'ResourcePermissionList', ], ], ], 'UpdateFolderPermissionsResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'StatusCode', ], 'Arn' => [ 'shape' => 'Arn', ], 'FolderId' => [ 'shape' => 'RestrictiveResourceId', ], 'Permissions' => [ 'shape' => 'ResourcePermissionList', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'UpdateFolderRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'FolderId', 'Name', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'FolderId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'FolderId', ], 'Name' => [ 'shape' => 'FolderName', ], ], ], 'UpdateFolderResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'Arn' => [ 'shape' => 'Arn', ], 'FolderId' => [ 'shape' => 'RestrictiveResourceId', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'UpdateGroupRequest' => [ 'type' => 'structure', 'required' => [ 'GroupName', 'AwsAccountId', 'Namespace', ], 'members' => [ 'GroupName' => [ 'shape' => 'GroupName', 'location' => 'uri', 'locationName' => 'GroupName', ], 'Description' => [ 'shape' => 'GroupDescription', ], 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'uri', 'locationName' => 'Namespace', ], ], ], 'UpdateGroupResponse' => [ 'type' => 'structure', 'members' => [ 'Group' => [ 'shape' => 'Group', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'UpdateIAMPolicyAssignmentRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'AssignmentName', 'Namespace', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'AssignmentName' => [ 'shape' => 'IAMPolicyAssignmentName', 'location' => 'uri', 'locationName' => 'AssignmentName', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'uri', 'locationName' => 'Namespace', ], 'AssignmentStatus' => [ 'shape' => 'AssignmentStatus', ], 'PolicyArn' => [ 'shape' => 'Arn', ], 'Identities' => [ 'shape' => 'IdentityMap', ], ], ], 'UpdateIAMPolicyAssignmentResponse' => [ 'type' => 'structure', 'members' => [ 'AssignmentName' => [ 'shape' => 'IAMPolicyAssignmentName', ], 'AssignmentId' => [ 'shape' => 'String', ], 'PolicyArn' => [ 'shape' => 'Arn', ], 'Identities' => [ 'shape' => 'IdentityMap', ], 'AssignmentStatus' => [ 'shape' => 'AssignmentStatus', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'UpdateResourcePermissionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourcePermission', ], 'max' => 100, ], 'UpdateTemplateAliasRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'TemplateId', 'AliasName', 'TemplateVersionNumber', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'TemplateId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'TemplateId', ], 'AliasName' => [ 'shape' => 'AliasName', 'location' => 'uri', 'locationName' => 'AliasName', ], 'TemplateVersionNumber' => [ 'shape' => 'VersionNumber', ], ], ], 'UpdateTemplateAliasResponse' => [ 'type' => 'structure', 'members' => [ 'TemplateAlias' => [ 'shape' => 'TemplateAlias', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'UpdateTemplatePermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'TemplateId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'TemplateId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'TemplateId', ], 'GrantPermissions' => [ 'shape' => 'UpdateResourcePermissionList', ], 'RevokePermissions' => [ 'shape' => 'UpdateResourcePermissionList', ], ], ], 'UpdateTemplatePermissionsResponse' => [ 'type' => 'structure', 'members' => [ 'TemplateId' => [ 'shape' => 'RestrictiveResourceId', ], 'TemplateArn' => [ 'shape' => 'Arn', ], 'Permissions' => [ 'shape' => 'ResourcePermissionList', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'UpdateTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'TemplateId', 'SourceEntity', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'TemplateId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'TemplateId', ], 'SourceEntity' => [ 'shape' => 'TemplateSourceEntity', ], 'VersionDescription' => [ 'shape' => 'VersionDescription', ], 'Name' => [ 'shape' => 'TemplateName', ], ], ], 'UpdateTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'TemplateId' => [ 'shape' => 'RestrictiveResourceId', ], 'Arn' => [ 'shape' => 'Arn', ], 'VersionArn' => [ 'shape' => 'Arn', ], 'CreationStatus' => [ 'shape' => 'ResourceStatus', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'UpdateThemeAliasRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'ThemeId', 'AliasName', 'ThemeVersionNumber', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'ThemeId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'ThemeId', ], 'AliasName' => [ 'shape' => 'AliasName', 'location' => 'uri', 'locationName' => 'AliasName', ], 'ThemeVersionNumber' => [ 'shape' => 'VersionNumber', ], ], ], 'UpdateThemeAliasResponse' => [ 'type' => 'structure', 'members' => [ 'ThemeAlias' => [ 'shape' => 'ThemeAlias', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'UpdateThemePermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'ThemeId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'ThemeId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'ThemeId', ], 'GrantPermissions' => [ 'shape' => 'UpdateResourcePermissionList', ], 'RevokePermissions' => [ 'shape' => 'UpdateResourcePermissionList', ], ], ], 'UpdateThemePermissionsResponse' => [ 'type' => 'structure', 'members' => [ 'ThemeId' => [ 'shape' => 'RestrictiveResourceId', ], 'ThemeArn' => [ 'shape' => 'Arn', ], 'Permissions' => [ 'shape' => 'ResourcePermissionList', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'UpdateThemeRequest' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', 'ThemeId', 'BaseThemeId', ], 'members' => [ 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'ThemeId' => [ 'shape' => 'RestrictiveResourceId', 'location' => 'uri', 'locationName' => 'ThemeId', ], 'Name' => [ 'shape' => 'ThemeName', ], 'BaseThemeId' => [ 'shape' => 'RestrictiveResourceId', ], 'VersionDescription' => [ 'shape' => 'VersionDescription', ], 'Configuration' => [ 'shape' => 'ThemeConfiguration', ], ], ], 'UpdateThemeResponse' => [ 'type' => 'structure', 'members' => [ 'ThemeId' => [ 'shape' => 'RestrictiveResourceId', ], 'Arn' => [ 'shape' => 'Arn', ], 'VersionArn' => [ 'shape' => 'Arn', ], 'CreationStatus' => [ 'shape' => 'ResourceStatus', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], 'RequestId' => [ 'shape' => 'String', ], ], ], 'UpdateUserRequest' => [ 'type' => 'structure', 'required' => [ 'UserName', 'AwsAccountId', 'Namespace', 'Email', 'Role', ], 'members' => [ 'UserName' => [ 'shape' => 'UserName', 'location' => 'uri', 'locationName' => 'UserName', ], 'AwsAccountId' => [ 'shape' => 'AwsAccountId', 'location' => 'uri', 'locationName' => 'AwsAccountId', ], 'Namespace' => [ 'shape' => 'Namespace', 'location' => 'uri', 'locationName' => 'Namespace', ], 'Email' => [ 'shape' => 'String', ], 'Role' => [ 'shape' => 'UserRole', ], 'CustomPermissionsName' => [ 'shape' => 'RoleName', ], 'UnapplyCustomPermissions' => [ 'shape' => 'Boolean', ], 'ExternalLoginFederationProviderType' => [ 'shape' => 'String', ], 'CustomFederationProviderUrl' => [ 'shape' => 'String', ], 'ExternalLoginId' => [ 'shape' => 'String', ], ], ], 'UpdateUserResponse' => [ 'type' => 'structure', 'members' => [ 'User' => [ 'shape' => 'User', ], 'RequestId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'UploadSettings' => [ 'type' => 'structure', 'members' => [ 'Format' => [ 'shape' => 'FileFormat', ], 'StartFromRow' => [ 'shape' => 'PositiveInteger', 'box' => true, ], 'ContainsHeader' => [ 'shape' => 'Boolean', 'box' => true, ], 'TextQualifier' => [ 'shape' => 'TextQualifier', ], 'Delimiter' => [ 'shape' => 'Delimiter', ], ], ], 'User' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'UserName' => [ 'shape' => 'UserName', ], 'Email' => [ 'shape' => 'String', ], 'Role' => [ 'shape' => 'UserRole', ], 'IdentityType' => [ 'shape' => 'IdentityType', ], 'Active' => [ 'shape' => 'Boolean', ], 'PrincipalId' => [ 'shape' => 'String', ], 'CustomPermissionsName' => [ 'shape' => 'RoleName', ], 'ExternalLoginFederationProviderType' => [ 'shape' => 'String', ], 'ExternalLoginFederationProviderUrl' => [ 'shape' => 'String', ], 'ExternalLoginId' => [ 'shape' => 'String', ], ], ], 'UserList' => [ 'type' => 'list', 'member' => [ 'shape' => 'User', ], ], 'UserName' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[\\u0020-\\u00FF]+', ], 'UserRole' => [ 'type' => 'string', 'enum' => [ 'ADMIN', 'AUTHOR', 'READER', 'RESTRICTED_AUTHOR', 'RESTRICTED_READER', ], ], 'Username' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'VersionDescription' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'VersionNumber' => [ 'type' => 'long', 'min' => 1, ], 'VpcConnectionProperties' => [ 'type' => 'structure', 'required' => [ 'VpcConnectionArn', ], 'members' => [ 'VpcConnectionArn' => [ 'shape' => 'Arn', ], ], ], 'Warehouse' => [ 'type' => 'string', 'max' => 128, ], 'WorkGroup' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'boolean' => [ 'type' => 'boolean', ], 'long' => [ 'type' => 'long', ], 'string' => [ 'type' => 'string', ], 'timestamp' => [ 'type' => 'timestamp', ], ],];
