<?php
namespace Aws\MediaStore;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS Elemental MediaStore** service.
 * @method \Aws\Result createContainer(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createContainerAsync(array $args = [])
 * @method \Aws\Result deleteContainer(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteContainerAsync(array $args = [])
 * @method \Aws\Result deleteContainerPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteContainerPolicyAsync(array $args = [])
 * @method \Aws\Result deleteCorsPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteCorsPolicyAsync(array $args = [])
 * @method \Aws\Result deleteLifecyclePolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteLifecyclePolicyAsync(array $args = [])
 * @method \Aws\Result deleteMetricPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteMetricPolicyAsync(array $args = [])
 * @method \Aws\Result describeContainer(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeContainerAsync(array $args = [])
 * @method \Aws\Result getContainerPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getContainerPolicyAsync(array $args = [])
 * @method \Aws\Result getCorsPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCorsPolicyAsync(array $args = [])
 * @method \Aws\Result getLifecyclePolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getLifecyclePolicyAsync(array $args = [])
 * @method \Aws\Result getMetricPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getMetricPolicyAsync(array $args = [])
 * @method \Aws\Result listContainers(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listContainersAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result putContainerPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putContainerPolicyAsync(array $args = [])
 * @method \Aws\Result putCorsPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putCorsPolicyAsync(array $args = [])
 * @method \Aws\Result putLifecyclePolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putLifecyclePolicyAsync(array $args = [])
 * @method \Aws\Result putMetricPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putMetricPolicyAsync(array $args = [])
 * @method \Aws\Result startAccessLogging(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startAccessLoggingAsync(array $args = [])
 * @method \Aws\Result stopAccessLogging(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopAccessLoggingAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 */
class MediaStoreClient extends AwsClient {}
